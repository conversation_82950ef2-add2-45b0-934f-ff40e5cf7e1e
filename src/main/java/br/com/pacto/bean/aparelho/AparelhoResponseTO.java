package br.com.pacto.bean.aparelho;

import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeAparelhoResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 01/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoResponseTO {

    private Integer id;
    private String nome;
    private List<AparelhoAjusteResponseTO> ajustes = new ArrayList<>();
    private List<AtividadeAparelhoResponseTO> atividades = new ArrayList<>();

    public AparelhoResponseTO(){

    }

    public AparelhoResponseTO(Aparelho aparelho){
        this.id = aparelho.getCodigo();
        this.nome = aparelho.getNome();
        for (AparelhoAjuste aparelhoAjuste: aparelho.getAjustes()){
            getAjustes().add(new AparelhoAjusteResponseTO(aparelhoAjuste.getCodigo(), aparelhoAjuste.getNome()));
        }
        for (AtividadeAparelho atividadeAparelho: aparelho.getAtividades()){
            getAtividades().add(new AtividadeAparelhoResponseTO(atividadeAparelho.getCodigo(), atividadeAparelho.getAtividade().getNome()));
        }
    }

    public AparelhoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AparelhoAjusteResponseTO> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AparelhoAjusteResponseTO> ajustes) {
        this.ajustes = ajustes;
    }

    public List<AtividadeAparelhoResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAparelhoResponseTO> atividades) {
        this.atividades = atividades;
    }
}
