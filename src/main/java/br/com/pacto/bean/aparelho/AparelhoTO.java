package br.com.pacto.bean.aparelho;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 29/07/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoTO {

    private Integer id;
    private String nome;
    private List<Integer> ajusteIds = new ArrayList<>();
    private List<Integer> atividadeIds = new ArrayList<>();
    private List<String> novosAjustes = new ArrayList<>();

    public AparelhoTO(){

    }

    public AparelhoTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAjusteIds() {
        return ajusteIds;
    }

    public void setAjusteIds(List<Integer> ajusteIds) {
        this.ajusteIds = ajusteIds;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<String> getNovosAjustes() {
        return novosAjustes;
    }

    public void setNovosAjustes(List<String> novosAjustes) {
        this.novosAjustes = novosAjustes;
    }
}
