package br.com.pacto.bean.aparelho;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 29/07/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoAjusteResponseTO {

    private Integer id;
    private String nome;

    public AparelhoAjusteResponseTO(){

    }

    public AparelhoAjusteResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
