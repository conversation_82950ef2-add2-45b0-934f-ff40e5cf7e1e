package br.com.pacto.bean.usuarioEmail;

import br.com.pacto.bean.usuario.Usuario;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by <PERSON> on 27/12/2016.
 */
@Entity(name = "UsuarioEmail")
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "usuarioEmail_email_key", columnNames = {"email"})})
public class UsuarioEmail implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String email;

    private Boolean verificado;

    @JoinColumn(name = "usuario", foreignKey = @ForeignKey(name = "fk_usuarioemail_usuario"))
    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REMOVE})
    private Usuario usuario;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getVerificado() {
        if (verificado == null) {
            verificado = false;
        }
        return verificado;
    }

    public void setVerificado(Boolean verificado) {
        this.verificado = verificado;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
