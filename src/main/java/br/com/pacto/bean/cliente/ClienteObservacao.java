/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.cliente;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;


import org.hibernate.annotations.Type;

import javax.persistence.*;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ClienteObservacao implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    private Integer usuario_codigo;
    @Column(columnDefinition = "boolean DEFAULT false")
    private Boolean importante = Boolean.FALSE;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataObservacao;
    private Boolean avaliacaoFisica = Boolean.FALSE;
    private String anexoKey;
    @Column(columnDefinition = "text")
    private String nomeArquivo;
    @Column(columnDefinition = "text")
    private String formatoArquivo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataObservacao() {
        return dataObservacao;
    }

    public void setDataObservacao(Date dataObservacao) {
        this.dataObservacao = dataObservacao;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario) {
        this.usuario_codigo = usuario;
    }
    
    public String getDataObservacaoApresentar() {
        try {
            return Uteis.getDataAplicandoFormatacao(dataObservacao, "dd/MM/yyyy");
        } catch (Exception e) {
            return "";
        }

    }

    public String getHoraObservacaoApresentar(){
        try {
            return Uteis.getDataAplicandoFormatacao(dataObservacao, "HH:mm");
        } catch (Exception e) {
            return "";
        }
    }
    
    public String getAnexoKey() {
        return anexoKey;
    }

    public void setAnexoKey(String anexoKey) {
        this.anexoKey = anexoKey;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }
}
