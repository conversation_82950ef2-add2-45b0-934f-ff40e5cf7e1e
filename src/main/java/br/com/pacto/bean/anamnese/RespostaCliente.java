package br.com.pacto.bean.anamnese;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.avaliacao.RespostaClienteParQ;
import br.com.pacto.bean.cliente.ClienteSintetico;
import javax.persistence.Column;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Type;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Entity
@Table
public class RespostaCliente {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String resposta;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String obs;
    @ManyToOne
    private ItemAvaliacaoFisica itemAvaliacao;
    @ManyToOne
    private RespostaClienteParQ respostaClienteParQ;
    @ManyToOne
    private AvaliacaoFisica avaliacao;
    @ManyToOne
    private PerguntaAnamnese perguntaAnamnese;

    public ItemAvaliacaoFisica getItemAvaliacao() {
        return itemAvaliacao;
    }

    public void setItemAvaliacao(ItemAvaliacaoFisica itemAvaliacao) {
        this.itemAvaliacao = itemAvaliacao;
    }

    public RespostaClienteParQ getRespostaClienteParQ() {
        return respostaClienteParQ;
    }

    public void setRespostaClienteParQ(RespostaClienteParQ respostaClienteParQ) {
        this.respostaClienteParQ = respostaClienteParQ;
    }

    public AvaliacaoFisica getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(AvaliacaoFisica avaliacao) {
        this.avaliacao = avaliacao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public String getResposta() {
        return resposta == null ? "" : this.resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public PerguntaAnamnese getPerguntaAnamnese() {
        return perguntaAnamnese;
    }

    public void setPerguntaAnamnese(PerguntaAnamnese perguntaAnamnese) {
        this.perguntaAnamnese = perguntaAnamnese;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }
    
    
}
