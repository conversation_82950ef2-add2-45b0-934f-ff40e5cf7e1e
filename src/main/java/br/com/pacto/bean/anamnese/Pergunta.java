package br.com.pacto.bean.anamnese;

import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;

import javax.faces.model.SelectItem;
import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON>ao Alcides on 04/05/2017.
 */
@Entity
@Table
public class Pergunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer codigo;
    protected Integer ordem;
    protected String descricao;
    @Enumerated(EnumType.ORDINAL)
    private AgrupamentoAvaliacaoIntegradaEnum agrupamento = null;
    @Enumerated(EnumType.ORDINAL)
    protected TiposPerguntaEnum tipoPergunta;
    @Transient
    private List<OpcaoPergunta> opcoes = new ArrayList<OpcaoPergunta>();
    @Transient
    private Boolean respondida = Boolean.FALSE;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public List<SelectItem> getItens(){
        List<SelectItem> list = new ArrayList<SelectItem>();
        for(OpcaoPergunta o : opcoes){
            list.add(new SelectItem(o.getCodigo(), (o.getPeso() == null ? "" : (o.getPeso().toString().concat(" - "))).concat(o.getOpcao())));
        }
        return list;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TiposPerguntaEnum getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(TiposPerguntaEnum tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public List<OpcaoPergunta> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPergunta> opcoes) {
        this.opcoes = opcoes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Boolean getRespondida() {
        return respondida;
    }

    public void setRespondida(Boolean respondida) {
        this.respondida = respondida;
    }

    public AgrupamentoAvaliacaoIntegradaEnum getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(AgrupamentoAvaliacaoIntegradaEnum agrupamento) {
        this.agrupamento = agrupamento;
    }
}
