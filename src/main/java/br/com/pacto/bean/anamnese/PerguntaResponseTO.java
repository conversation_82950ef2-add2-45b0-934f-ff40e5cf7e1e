package br.com.pacto.bean.anamnese;

import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerguntaResponseTO {

    private Integer id;
    private String pergunta;
    private Integer ordem;
    private TipoPerguntaDTOEnum tipo;
    private List<OpcaoPerguntaResponseTO> opcoes = new ArrayList<>();
    private Boolean apresentarLeiParq;

    public PerguntaResponseTO(Pergunta pergunta, Integer perguntaAnamneseId) {
        this.id = perguntaAnamneseId;
        this.pergunta = pergunta.getDescricao();
        this.ordem = pergunta.getOrdem();
        this.tipo = pergunta.getTipoPergunta().getTipoPerguntaDTOEnum();
        this.opcoes.clear();
        for (OpcaoPergunta opcao: pergunta.getOpcoes()) {
            this.opcoes.add(new OpcaoPerguntaResponseTO(opcao));
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public TipoPerguntaDTOEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoPerguntaDTOEnum tipo) {
        this.tipo = tipo;
    }

    public List<OpcaoPerguntaResponseTO> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPerguntaResponseTO> opcoes) {
        this.opcoes = opcoes;
    }

    public Boolean getApresentarLeiParq() {
        return apresentarLeiParq;
    }

    public void setApresentarLeiParq(Boolean apresentarLeiParq) {
        this.apresentarLeiParq = apresentarLeiParq;
    }
}