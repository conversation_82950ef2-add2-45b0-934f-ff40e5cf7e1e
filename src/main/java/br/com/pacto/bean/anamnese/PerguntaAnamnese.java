package br.com.pacto.bean.anamnese;


import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 04/05/2017.
 */
@Entity
@Table
public class PerguntaAnamnese {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Anamnese anamnese;
    @ManyToOne
    private Pergunta pergunta;
    @Transient
    private String resposta;
    @Transient
    private List<String> respostas = new ArrayList<String>();
    @Transient
    private String complemento;

    public PerguntaAnamnese() {
    }

    public PerguntaAnamnese(PerguntaAnamnese pa) {
        this.codigo = pa.codigo;
        this.anamnese = pa.anamnese;
        this.pergunta = pa.pergunta;
        this.resposta = pa.resposta;
        this.respostas = new ArrayList<String>(pa.respostas);
        this.complemento = pa.complemento;
    }

    public List<String> getRespostas() {
        return respostas;
    }

    public void setRespostas(List<String> respostas) {
        this.respostas = respostas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Anamnese getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(Anamnese anamnese) {
        this.anamnese = anamnese;
    }

    public Pergunta getPergunta() {
        return pergunta;
    }

    public void setPergunta(Pergunta pergunta) {
        this.pergunta = pergunta;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public int getOrdem(){
        try{
            return getPergunta().getOrdem();
        }catch (Exception ignore){
            return 0;
        }
    }
}
