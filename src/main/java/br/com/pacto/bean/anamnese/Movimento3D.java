package br.com.pacto.bean.anamnese;

import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;

import javax.persistence.*;

@Entity
@Table
public class Movimento3D {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ItemAvaliacaoFisica item;
    @Enumerated(EnumType.ORDINAL)
    protected Movimento3DEnum movimento;
    private Integer esquerda = 0;
    private Integer direita = 0;

    public Movimento3D() {
    }

    public Movimento3D(Movimento3DEnum m) {
        this.movimento = m;
    }

    public Integer getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(Integer esquerda) {
        this.esquerda = esquerda;
    }

    public Integer getDireita() {
        return direita;
    }

    public void setDireita(Integer direita) {
        this.direita = direita;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ItemAvaliacaoFisica getItem() {
        return item;
    }

    public void setItem(ItemAvaliacaoFisica item) {
        this.item = item;
    }

    public Movimento3DEnum getMovimento() {
        return movimento;
    }

    public void setMovimento(Movimento3DEnum movimento) {
        this.movimento = movimento;
    }
}
