package br.com.pacto.bean.pessoa;

/**
 * Created by ulisses on 27/08/2018.
 */
public enum SexoEnum {

    N("N","Não Informado"),
    M("<PERSON>","<PERSON><PERSON><PERSON><PERSON>"),
    F("F","<PERSON><PERSON><PERSON>");

    private String sexo;
    private String descricao;

    private SexoEnum(String sexo, String descricao) {
        this.sexo = sexo;
        this.descricao = descricao;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static SexoEnum getFromId(int id){
        for(SexoEnum tt : values()){
            if(tt.ordinal() == id){
                return tt;
            }
        }
        return null;
    }



    public static SexoEnum getSexo(String str) {
        for (SexoEnum sexo : SexoEnum.values()) {
            if (sexo.sexo.equals(str)) {
                return sexo;
            }
        }
        return null;
    }

}