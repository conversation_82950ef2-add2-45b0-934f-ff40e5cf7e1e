/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.serie;

/**
 *
 * <AUTHOR>
 */
public enum DescansoEnum {

    SEGUNDOS_0(0, "00:00", "0\""),
    SEGUNDOS_30(1, "00:30", "30\""),
    SEGUNDOS_60(2, "01:00", "1'"),
    SEGUNDOS_90(3, "01:30", "1'30\"");
    private Integer id;
    private String tempo;
    private String label;

    private DescansoEnum(Integer id, String tempo, String label) {
        this.id = id;
        this.tempo = tempo;
        this.label = label;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTempo() {
        return tempo;
    }

    public void setTempo(String tempo) {
        this.tempo = tempo;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
