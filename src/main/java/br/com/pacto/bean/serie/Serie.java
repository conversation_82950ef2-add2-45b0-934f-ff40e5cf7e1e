/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.serie;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.NumeroTO;
import java.io.Serializable;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.persistence.*;

import br.com.pacto.util.impl.UtilReflection;
import org.hibernate.annotations.Type;

import static org.apache.commons.lang.StringUtils.isNumeric;

/**
 *
 * <AUTHOR>
 */
@Entity
public class Serie implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private AtividadeFicha atividadeFicha;
    private Integer repeticao = 0;
    private String repeticaoComp;//generico para repeticao
    private Double carga = 0.0;//gramas
    private String cargaComp;//generico para carga
    private Integer duracao = 0;//minutos
    private Integer distancia = 0;//metros
    private Double velocidade = 0.0;//km/h
    private String cargaApp;
    private String repeticaoApp;
    private Boolean atualizadoApp = Boolean.FALSE;
    private Boolean serieRealizada = Boolean.FALSE;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String complemento;
    private Integer descanso = 0;//segundos
    private Integer ordem = null;
    private String cadencia;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataUltimaExecucao = null;
    @Transient
    private Integer quantidade = 1;
    @Transient
    private Boolean realizada = false;
    @Transient
    private Boolean iniciada = Boolean.FALSE;
    @Transient
    private String descansoStr = null;
    @Transient
    private String duracaoStr = null;
    @Transient
    private String duracaoPrevistaStr = null;
    @Transient
    private Integer repeticaoPrevista = 0;
    @Transient
    private Double cargaPrevista = 0.0;
    @Transient
    private Integer duracaoPrevista = 0;
    @Transient
    private Integer distanciaPrevista = 0;
    @Transient
    private Double velocidadePrevista = 0.0;
    @Transient
    private NumeroTO cargaSpin;
    @Transient
    private NumeroTO repeticaoSpin;
    @Transient
    private NumeroTO duracaoSpin;
    @Transient
    private NumeroTO velocidadeSpin;
    @Transient
    private NumeroTO distanciaSpin;
    @Transient
    private NumeroTO quantidadeSpin;
    @Transient
    private Long idTela;
    @Transient
    private String repeticoesVetor;
    @Transient
    private String cargaVetor;
    @Transient
    private String repeticaoConcatenado;
    @Transient
    private String cargaConcatenado;
    @Transient
    private Boolean adicionarDescanso = false;
    @Transient
    private String velocidadeContatenado;
    @Transient
    private String distanciaConcatenado;
    @Transient
    private String complementoConcatenado;

    public Serie getClone() {
        Serie clone = new Serie();
        clone.setCodigo(this.codigo != null ? this.getCodigo() : 0);
        clone.setAtividadeFicha(this.atividadeFicha != null ? this.getAtividadeFicha() : new AtividadeFicha());
        clone.setRepeticao(this.repeticao != null ? this.getRepeticao() : 0);
        clone.setCarga(this.carga != null ? this.getCarga() : 0.0);
        clone.setDuracao(this.duracao != null ? this.getDuracao() : 0);
        clone.setDistancia(this.distancia != null ? this.getDistancia() : 0);
        clone.setVelocidade(this.velocidade != null ? this.getVelocidade() : 0.0);
        return clone;
    }

    public NumeroTO getQuantidadeSpin() {
        if (quantidadeSpin == null) {
            quantidadeSpin = new NumeroTO(quantidade);
        }
        return quantidadeSpin;
    }

    public void setQuantidadeSpin(NumeroTO quantidadeSpin) {
        this.quantidadeSpin = quantidadeSpin;
    }

    public NumeroTO getRepeticaoSpin() {
        if (repeticaoSpin == null) {
            repeticaoSpin = new NumeroTO(repeticao);
        }
        return repeticaoSpin;
    }

    public void setRepeticaoSpin(NumeroTO repeticaoSpin) {
        this.repeticaoSpin = repeticaoSpin;
    }

    public Boolean valoresZerados(){

        return (duracaoSpin.getDezena() +duracaoSpin.getCentena() + duracaoSpin.getUnidade() + duracaoSpin.getDecimal()) == 0;
    }
    public NumeroTO getDuracaoSpin() {
        if (duracaoSpin == null) {
            duracaoSpin = new NumeroTO(getDuracaoStr());
        }else if(valoresZerados()){
            duracaoSpin = new NumeroTO(getDuracaoStr());
        }
        return duracaoSpin;
    }

    public void setDuracaoSpin(NumeroTO duracaoSpin) {
        this.duracaoSpin = duracaoSpin;
    }

    public NumeroTO getVelocidadeSpin() {
        if (velocidadeSpin == null) {
            velocidadeSpin = new NumeroTO(velocidade);
        }
        return velocidadeSpin;
    }

    public void setVelocidadeSpin(NumeroTO velocidadeSpin) {
        this.velocidadeSpin = velocidadeSpin;
    }

    public NumeroTO getDistanciaSpin() {
        if (distanciaSpin == null) {
            distanciaSpin = new NumeroTO(distancia);
        }
        return distanciaSpin;
    }

    public void setDistanciaSpin(NumeroTO distanciaSpin) {
        this.distanciaSpin = distanciaSpin;
    }

    public NumeroTO getCargaSpin() {
        if (cargaSpin == null) {
            cargaSpin = new NumeroTO(carga);
        }
        return cargaSpin;
    }

    public void setCargaSpin(NumeroTO cargaSpin) {
        this.cargaSpin = cargaSpin;
    }

    public String getDescansoStr() {
        if (descansoStr == null) {
            descansoStr = Uteis.converterSegundosEmMinutos(descanso);
        }
        return descansoStr;
    }

    public void obterCompValores(){
        if(Uteis.valorVazioString(getRepeticaoComp()) && !UteisValidacao.emptyNumber(getRepeticao())){
            setRepeticaoComp(getRepeticao().toString());
        }
        if(Uteis.valorVazioString(getCargaComp()) && !UteisValidacao.emptyNumber(getCarga())){
            setCargaComp(getCarga().toString());
        }
    }

    public void setarCompValores(){
        if(!UteisValidacao.emptyString(getCargaComp())){
            try {
                setCarga(Double.valueOf(getCargaComp()));
            }catch (Exception e){
                try {
                    String[] split = getCargaComp().split("\\D+");
                    setCarga(Double.valueOf(split[0]));
                }catch (Exception ex){}
            }
        }else {
            if(UteisValidacao.emptyNumber(getCarga())) {
                setCarga(0.0);
            } else if(UteisValidacao.emptyString(getCargaComp())) {
                setCargaComp(String.valueOf(getCarga()));
            }
        }
        if(!UteisValidacao.emptyString(getRepeticaoComp())){
            try {
                setRepeticao(Integer.valueOf(getRepeticaoComp()));
            }catch (Exception e){
                try {
                    String[] split = getRepeticaoComp().split("\\D+");
                    setRepeticao(Integer.valueOf(split[0]));
                }catch (Exception ex){}
            }
        }else{
            if(UteisValidacao.emptyNumber(getRepeticao())) {
                setRepeticao(0);
            } else if(UteisValidacao.emptyString(getRepeticaoComp())) {
                setRepeticaoComp(String.valueOf(getRepeticao()));
            }
        }
    }

    public void setarSegundos(boolean resetSpins) {
        if (duracaoStr != null) {
            //duracaoStr = getDuracaoSpin().getValorHora();
            duracao = Uteis.converterMinutosEmSegundos(duracaoStr);
        }
        if (descansoStr != null) {
            descanso = Uteis.converterMinutosEmSegundos(descansoStr);
        }
        if (resetSpins) {
            resetSpins();
        }
        carga = new Double(getCargaSpin().getValorDouble());
        repeticao = new Integer(getRepeticaoSpin().getValorInteiro());
        quantidade = new Integer(getQuantidadeSpin().getValorInteiro());
        distancia = new Integer(getDistanciaSpin().getValorInteiro());
        velocidade = new Double(getVelocidadeSpin().getValorDouble());
    }
    
    public void resetSpins() {
        cargaSpin = null;
        repeticaoSpin = null;
        quantidadeSpin = null;
        distanciaSpin = null;
        velocidadeSpin = null;
        duracaoSpin = null;
    }

    public String getDuracaoStr() {
        if (duracaoStr == null) {
            duracaoStr = Uteis.converterSegundosEmMinutos(duracao);
        }
        return duracaoStr;
    }

    public String getDuracaoPrevistaStr() {
        if (duracaoPrevistaStr == null) {
            duracaoPrevistaStr = Uteis.converterSegundosEmMinutos(duracaoPrevista);
        }
        return duracaoPrevistaStr;
    }

    public static SerieTO concatenarSeries(SerieTO serie1,SerieTO serie2,SerieTO serie3){
        SerieTO serie = serie1 == null ? (serie2 == null ? new SerieTO() : serie2) : serie1;
        if(serie1.getAdicionarDescanso() && serie1.getDescanso() > 0) {
            serie.setDescanso(serie1.getDescanso());
        } else if(serie2 != null && serie2.getAdicionarDescanso() && serie2.getDescanso() > 0){
            serie.setDescanso(serie2.getDescanso());
        } else if(serie3 != null && serie3.getAdicionarDescanso() && serie3.getDescanso() > 0){
            serie.setDescanso(serie3.getDescanso());
        }
        serie.setCargaComp(concatenarString(serie == null ? null : serie.getCargaComp(),serie2 == null ? null : serie2.getCargaComp(),serie3 == null ? null : serie3.getCargaComp()));
        serie.setRepeticaoComp(concatenarString(serie == null ? null : serie.getRepeticaoComp(),serie2 == null ? null : serie2.getRepeticaoComp(),serie3 == null ? null : serie3.getRepeticaoComp()));
        serie.setCargaApp(concatenarString(serie == null ? null : serie.getCargaApp(),serie2 == null ? null : serie2.getCargaApp(),serie3 == null ? null : serie3.getCargaApp()));
        serie.setRepeticaoApp(concatenarString(serie == null ? null : serie.getRepeticaoApp(),serie2 == null ? null : serie2.getRepeticaoApp(),serie3 == null ? null : serie3.getRepeticaoApp()));
        serie.setCargaConcatenado(concatenarString(serie == null ? null : serie.getCarga(),serie2 == null ? null : serie2.getCarga(),serie3 == null ? null : serie3.getCarga()));

        serie.setRepeticaoConcatenado(concatenarString(
                serie == null ? null :
                        serie.getValor1(serie.getTipo()),
                serie2 == null ? null : serie2.getValor1(serie2.getTipo()),
                serie3 == null ? null : serie3.getValor1(serie3.getTipo())));

        serie.setVelocidadeContatenado(
                concatenarString(
                        serie == null ? null : serie.getValor2(serie.getTipo()),
                        serie2 == null ? null : serie2.getValor2(serie2.getTipo()),
                        serie3 == null ? null : serie3.getValor2(serie3.getTipo())));
        serie.setDistanciaConcatenado(concatenarString(
                serie == null ? null : (serie.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ? "" :  serie.getValor3(serie.getTipo())),
                serie2 == null ? null : (serie2.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ?  "0" :  serie2.getValor3(serie2.getTipo())),
                serie3 == null ? null : (serie3.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ?  "0" :  serie3.getValor3(serie3.getTipo()))));
        serie.setDuracaoStr(serie.getRepeticaoConcatenado());
        serie.setComplementoConcatenado(concatenarString(serie == null ? null : UteisValidacao.emptyString(serie.getComplemento()) ? "" : serie.getComplemento(),
                serie2 == null ? null :  UteisValidacao.emptyString(serie2.getComplemento()) ? "" : serie2.getComplemento(),
                serie3 == null ? null : UteisValidacao.emptyString(serie3.getComplemento()) ? "" : serie3.getComplemento()));
        return serie;
    }

    private static String concatenarString(Object valor1, Object valor2,Object valor3){
        StringBuilder i = new StringBuilder();
        String lig = "";
        if(valor1 != null &&  !valor1.equals("")){
            i.append(valor1);
            lig = "/";
        }
        if(valor2 != null && !valor2.equals("")){
            i.append(lig);
            i.append(valor2);
        }
        if(valor3 != null && !valor3.equals("")){
            i.append(lig);
            i.append(valor3);
        }
        return i.toString();
    }
    public void setDuracaoStr(String duracaoStr) {
        this.duracaoStr = duracaoStr;
    }

    public void setDescansoStr(String descansoStr) {
        this.descansoStr = descansoStr;
    }

    public Boolean getIniciada() {
        return iniciada;
    }

    public void setIniciada(Boolean iniciada) {
        this.iniciada = iniciada;
    }

    public Boolean getRealizada() {
        return realizada;
    }

    public void setRealizada(Boolean realizada) {
        this.realizada = realizada;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Serie() {
    }

    public Serie(Integer ordem, AtividadeFicha atvFic) {
        this.ordem = ordem;
        this.atividadeFicha = atvFic;
    }

    public Serie(AtividadeFicha atividadeFicha, Integer repeticao, Double carga, Integer descanso, Integer ordem, final String complemento) {
        this.atividadeFicha = atividadeFicha;
        this.repeticao = repeticao;
        this.carga = carga;
        this.descanso = descanso;
        this.ordem = ordem;
        this.complemento = complemento;
    }

    public Serie(AtividadeFicha f, Serie s) {
        this.atividadeFicha = f;
        this.repeticao = s.getRepeticao();
        this.carga = s.getCarga();
        this.descanso = s.getDescanso();
        this.ordem = s.getOrdem();
        this.complemento = s.getComplemento();
        this.duracao = s.getDuracao();
        this.distancia = s.getDistancia();
        this.velocidade = s.getVelocidade();
        this.repeticaoComp = s.getRepeticaoComp();
        this.cargaComp = s.getCargaComp();
        this.cadencia = s.getCadencia();
        this.repeticaoApp = s.getRepeticaoApp();
        this.cargaApp = s.getCargaApp();
        this.atualizadoApp = s.getAtualizadoApp();
    }

    public Serie(AtividadeFicha atividadeFicha, Integer duracao, Integer distancia, Double velocidade, Integer descanso,
            Integer ordem, Integer repeticao, Double carga, final String complemento, 
            final String cargaComp, final String repeticaoComp, final String cadencia) {
        this.atividadeFicha = atividadeFicha;
        this.duracao = duracao;
        this.distancia = distancia;
        this.velocidade = velocidade;
        this.descanso = descanso;
        this.ordem = ordem;
        this.complemento = complemento;
        this.repeticao = repeticao;
        this.carga = carga;
        
        this.cadencia = cadencia;
        this.repeticaoComp = repeticaoComp;
        this.cargaComp = cargaComp;
    }

    public void inicializarNulos() {
        this.duracao = duracao == null ? 0 : duracao;
        this.distancia = distancia == null ? 0 : distancia;
        this.velocidade = velocidade == null ? 0.0 : velocidade;
        this.descanso = descanso == null ? 0 : descanso;
        this.ordem = ordem == null ? 0 : ordem;
        this.repeticao = repeticao == null ? 0 : repeticao;
        this.carga = carga == null ? 0 : carga;
    }

    public void setarNulos() {
        this.duracao = duracao == 0 ? null : duracao;
        this.distancia = distancia == 0 ? null : distancia;
        this.velocidade = velocidade == 0.0 ? null : velocidade;
        this.descanso = descanso == 0 ? null : descanso;
        this.ordem = ordem == 0 ? null : ordem;
        this.repeticao = repeticao == 0 ? null : repeticao;
        this.carga = carga == 0 ? null : carga;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AtividadeFicha getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(AtividadeFicha atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public Integer getRepeticao() {
        if (repeticao == null) {
            repeticao = 0;
        }
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
        setRepeticaoSpin(new NumeroTO(repeticao));
    }

    public Double getCarga() {
        if (carga == null) {
            carga = 0.0;
        }
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
        setCargaSpin(new NumeroTO(carga));
    }

    public Integer getDuracao() {
        if (duracao == null) {
            duracao = 0;
        }
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        if (distancia == null) {
            distancia = 0;
        }
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
        setDistanciaSpin(new NumeroTO(distancia));
    }

    public Double getVelocidade() {
        if (velocidade == null) {
            velocidade = 0.0;
        }
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
        setVelocidadeSpin(new NumeroTO(velocidade));
    }

    public String getComplementoFormat() {
        return complemento;
    }

    public String getComplemento() {
        return complemento == null ? "" : complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Integer getDescanso() {
        if (descanso == null) {
            descanso = 0;
        }
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getRepeticaoPrevista() {
        return repeticaoPrevista;
    }

    public void setRepeticaoPrevista(Integer repeticaoPrevista) {
        this.repeticaoPrevista = repeticaoPrevista;
    }

    public Double getCargaPrevista() {
        return cargaPrevista;
    }

    public void setCargaPrevista(Double cargaPrevista) {
        this.cargaPrevista = cargaPrevista;
    }

    public Integer getDuracaoPrevista() {
        return duracaoPrevista;
    }

    public void setDuracaoPrevista(Integer duacaoPrevista) {
        this.duracaoPrevista = duacaoPrevista;
    }

    public Integer getDistanciaPrevista() {
        return distanciaPrevista;
    }

    public void setDistanciaPrevista(Integer distanciaPrevista) {
        this.distanciaPrevista = distanciaPrevista;
    }

    public Double getVelocidadePrevista() {
        return velocidadePrevista;
    }

    public void setVelocidadePrevista(Double velocidadePrevista) {
        this.velocidadePrevista = velocidadePrevista;
    }

    public Date getDataUltimaExecucao() {
        return dataUltimaExecucao;
    }

    public void setDataUltimaExecucao(Date dataUltimaExecucao) {
        this.dataUltimaExecucao = dataUltimaExecucao;
    }

    public String getRepeticaoComp() {
        if (repeticaoComp == null) {
            repeticaoComp = "";
        }
        return repeticaoComp;
    }

    public void setRepeticaoComp(String repeticaoComp) {
        this.repeticaoComp = repeticaoComp;
        if (!isNumeric(repeticaoComp)) {
            this.repeticao = 0; 
        }
    }

    public String getCargaComp() {
        if (cargaComp == null) {
            cargaComp = "";
        }
        return cargaComp;
    }

    public void setCargaComp(String cargaComp) {
        this.cargaComp = cargaComp;
    }

    public Long getIdTela() {
        if (idTela == null){
            idTela = new Date().getTime();
        }
        return idTela;
    }

    public void setIdTela(Long idTela) {
        this.idTela = idTela;
    }

    public String getRepeticoesApresentar() {
        if (realizada) {
            return String.format("%s / %s", repeticaoPrevista, repeticao);
        } else {
            return String.format("%s", repeticao);
        }
    }

    public String getCargaApresentar() {
        if (realizada) {
            return String.format("%s / %s", new Object[]{cargaPrevista, carga}).replaceAll("\\.", ",");
        } else {
            return String.format("%s", new Object[]{carga}).replaceAll("\\.", ",");
        }
    }

    public String getDuracaoApresentar() {
        if (realizada) {
            return String.format("%s / %s", getDuracaoPrevistaStr(), getDuracaoStr());
        } else {
            return String.format("%s", getDuracaoStr());
        }
    }

    public String getDistanciaApresentar() {
        if (realizada) {
            return String.format("%s / %s", distanciaPrevista, distancia);
        } else {
            return String.format("%s", distancia);
        }
    }

    public String getVelocidadeApresentar() {
        if (realizada) {
            return String.format("%s / %s", new Object[]{velocidadePrevista, velocidade}).replaceAll("\\.", ",");
        } else {
            return String.format("%s", new Object[]{velocidade}).replaceAll("\\.", ",");
        }
    }

    public String getName() {
        return "stc" + (codigo == null || codigo == 0 ? getIdTela().toString() : codigo.toString());
    }

    public String getValor1(final TipoAtividadeEnum tipo) {
        if (tipo == TipoAtividadeEnum.ANAEROBICO) {
            return (getRepeticaoConcatenado() != null ? getRepeticaoConcatenado().toString() : "");
        } else {
            return (getDuracaoStr() != null && !getDuracaoStr().equals("00:00")
                    ? getDuracaoStr() : getDistanciaConcatenado());
        }
    }

    public String getValor2(final TipoAtividadeEnum tipo) {
        if (tipo == TipoAtividadeEnum.ANAEROBICO) {
            return (getCargaConcatenado());
        } else {
            return (getVelocidadeContatenado());
        }
    }

    public String getValor3(final TipoAtividadeEnum tipo) {
        if (tipo == TipoAtividadeEnum.ANAEROBICO) {
            return "";
        } else {
            return (getDuracaoStr() != null && !getDuracaoStr().equals("00:00")
                    ? (getDistanciaConcatenado() == null ? "" : getDistanciaConcatenado()) : "00:00");
        }
    }

    public void ajustarDadosPorTipo(final String valor1, final String valor2) {
        if (atividadeFicha != null) {
            if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.AEROBICO)) {
                if (valor1.contains(":")) {
                    this.setDuracao(Uteis.converterMinutosEmSegundos(valor1));
                } else if (getDuracao() > 0) {
                    this.setDuracao(Integer.valueOf(valor1));
                } else {
                    this.setDistancia(Integer.valueOf(valor1));
                }
                this.setVelocidade(Double.valueOf(valor2));
            } else if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                this.setRepeticao(Integer.valueOf(valor1));
                this.setCarga(Double.valueOf(valor2));
            }
        }
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public boolean foiExecutadaHoje() {
        return getDataUltimaExecucao() != null
                && Calendario.igual(Calendario.hoje(), getDataUltimaExecucao());
    }

    public String getRepeticoesVetor() {
        return repeticoesVetor;
    }

    public void setRepeticoesVetor(String repeticoesVetor) {
        this.repeticoesVetor = repeticoesVetor;
    }

    public String getCargaVetor() {
        return cargaVetor;
    }

    public void setCargaVetor(String cargaVetor) {
        this.cargaVetor = cargaVetor;
    }

    public String getRepeticaoConcatenado() {
        return UteisValidacao.emptyString(repeticaoConcatenado) ? getRepeticao().toString() : repeticaoConcatenado;
    }

    public void setRepeticaoConcatenado(String repeticaoConcatenado) {
        this.repeticaoConcatenado = repeticaoConcatenado;
    }

    public String getCargaConcatenado() {
        return UteisValidacao.emptyString(cargaConcatenado) ? getCarga().toString() : cargaConcatenado;
    }

    public void setCargaConcatenado(String cargaConcatenado) {
        this.cargaConcatenado = cargaConcatenado;
    }

    public Boolean getAdicionarDescanso() {
        return adicionarDescanso;
    }

    public void setAdicionarDescanso(Boolean adicionarDescanso) {
        this.adicionarDescanso = adicionarDescanso;
    }

    public String getVelocidadeContatenado() {
        return UteisValidacao.emptyString(velocidadeContatenado) ? getVelocidade().toString() : velocidadeContatenado;
    }

    public void setVelocidadeContatenado(String velocidadeContatenado) {
        this.velocidadeContatenado = velocidadeContatenado;
    }

    public void setDuracaoPrevistaStr(String duracaoPrevistaStr) {
        this.duracaoPrevistaStr = duracaoPrevistaStr;
    }

    public String getDistanciaConcatenado() {
        return UteisValidacao.emptyString(distanciaConcatenado) ? getDistancia().toString() : distanciaConcatenado;
    }

    public void setDistanciaConcatenado(String distanciaConcatenado) {
        this.distanciaConcatenado = distanciaConcatenado;
    }
    public boolean isSerieBiSet(){
        return getAtividadeFicha().getAtividadeAssociada1() !=null || getAtividadeFicha().getAtividadeAssociada2() != null;
    }
    public String getComplementoResumido() {
        return UteisValidacao.emptyString(complemento) ? "..." : complemento.length() < 15 ? complemento : (complemento.substring(0, 14) + "...");
    }

    public String getComplementoConcatenado() {
        return complementoConcatenado;
    }

    public void setComplementoConcatenado(String complementoConcatenado) {
        this.complementoConcatenado = complementoConcatenado;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }

    public Boolean getAtualizadoApp() {
        if (atualizadoApp == null){
            atualizadoApp = false;
        }
        return atualizadoApp;
    }

    public void setAtualizadoApp(Boolean atualizadoApp) {
        this.atualizadoApp = atualizadoApp;
    }

    public Boolean getSerieRealizada() {
        if (serieRealizada == null){
            serieRealizada = false;
        }
        return serieRealizada;
    }

    public void setSerieRealizada(Boolean serieRealizada) { this.serieRealizada = serieRealizada; }

    public String getDescricaoParaLog(Serie serieAnterior) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, serieAnterior));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

}
