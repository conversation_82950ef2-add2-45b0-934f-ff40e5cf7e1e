package br.com.pacto.bean.serie;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 17/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SerieEndpointTO {

    private Integer id;
    private Integer atividadeFichaId;
    private Integer sequencia;
    private String repeticoes;
    private Integer repeticaoComp;
    private String carga;
    private Double cargaComp;
    private String cadencia;
    private Integer descanso;
    private String complemento;
    private Double velocidade;
    private Integer duracao;
    private Integer distancia;
    private String cargaApp;
    private String repeticaoApp;
    private Boolean serieRealizada;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAtividadeFichaId() {
        return atividadeFichaId;
    }

    public void setAtividadeFichaId(Integer atividadeFichaId) {
        this.atividadeFichaId = atividadeFichaId;
    }

    public Integer getSequencia() {
        return sequencia == null ? 0 : sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public String getRepeticoes() {
        return repeticoes == null ? "" : repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public String getCarga() {
        return carga == null ? "" : carga;
    }

    public void setCarga(String carga) {
        this.carga = carga;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getDescanso() {
        return descanso == null ? 0 : descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Double getVelocidade() {
        return velocidade == null ? 0.0 : velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDuracao() {
        return duracao == null ? 0 : duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia == null ? 0 : distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }

    public Integer getRepeticaoComp() { return repeticaoComp; }

    public void setRepeticaoComp(Integer repeticaoComp) { this.repeticaoComp = repeticaoComp; }

    public Double getCargaComp() { return cargaComp; }

    public void setCargaComp(Double cargaComp) { this.cargaComp = cargaComp; }

    public Boolean getSerieRealizada() { return serieRealizada; }

    public void setSerieRealizada(Boolean serieRealizada) { this.serieRealizada = serieRealizada; }
}
