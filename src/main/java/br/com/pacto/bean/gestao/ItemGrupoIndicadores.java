/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import br.com.pacto.bean.programa.TipoExecucaoEnum;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ItemGrupoIndicadores implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private String professores;
    @Enumerated(EnumType.ORDINAL)
    private IndicadorGraficoEnum indicador;
    @Enumerated(EnumType.ORDINAL)
    private TipoGraficoEnum tipo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public IndicadorGraficoEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorGraficoEnum indicador) {
        this.indicador = indicador;
    }

    public TipoGraficoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoGraficoEnum tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getProfessores() {
        return professores;
    }

    public void setProfessores(String professores) {
        this.professores = professores;
    }
    
    
    
}
