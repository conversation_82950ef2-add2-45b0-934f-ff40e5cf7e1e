/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

/**
 *
 * <AUTHOR>
 */
public class ItemGraficoTO {
    private IndicadorGraficoEnum indicador;
    private boolean selecionado;
    private boolean primeiro;
    private TipoGraficoEnum tipo;

    public ItemGraficoTO(IndicadorGraficoEnum indicador, boolean selecionado, TipoGraficoEnum tipo) {
        this.indicador = indicador;
        this.selecionado = selecionado;
        this.tipo = tipo;
    }

    public String getName(){
        return indicador.name();
    }

    public int getOrdinal(){
        return indicador.ordinal();
    }
    
    public IndicadorGraficoEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorGraficoEnum indicador) {
        this.indicador = indicador;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public TipoGraficoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoGraficoEnum tipo) {
        this.tipo = tipo;
    }

    public boolean isPrimeiro() {
        return primeiro;
    }

    public void setPrimeiro(boolean primeiro) {
        this.primeiro = primeiro;
    }
    
    
}
