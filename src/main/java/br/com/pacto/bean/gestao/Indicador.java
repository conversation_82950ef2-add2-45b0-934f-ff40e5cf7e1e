/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.gestao.GestaoService;
import br.com.pacto.util.UtilContext;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Indicador implements Serializable {
    
    

    private Integer codProfessor;
    private String nomeProfessor;
    private Number total = 0;
    private IndicadorEnum tipo;
    private List objetos;
    private Date dataInicio;
    private Date dataFim;

    public Indicador(final Integer codProfessor, final String nomeProfessor, final IndicadorEnum tipo, final Date dataInicio,
            final Date dataFim) {
        this.codProfessor = codProfessor;
        this.nomeProfessor = nomeProfessor;
        this.tipo = tipo;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
    }

    public Number getTotal() {
        return total;
    }

    public Integer getTotalOrd() {
        return total == null ? 0 : total.intValue();
    }
    
    public void setTotal(Number total) {
        this.total = total;
    }

    public IndicadorEnum getTipo() {
        return tipo;
    }

    public void setTipo(IndicadorEnum tipo) {
        this.tipo = tipo;
    }

    public List getObjetos() {
        if(objetos == null){
            return new ArrayList();
        }
        return objetos;
    }

    public void setObjetos(List objetos) {
        this.objetos = objetos;
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List obterLista(final String ctx, final Integer empresaZW, final FiltrosGestaoTO filtro) {
        GestaoService gestaoService = (GestaoService) UtilContext.getBean(GestaoService.class);
        try {
            Indicador ind = gestaoService.calcularIndicador(ctx, empresaZW, this.tipo, filtro, codProfessor, nomeProfessor, ResultEnum.LIST);
            return ind.getObjetos();
        } catch (ServiceException ex) {
            Logger.getLogger(Indicador.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

}
