/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public enum IndicadorGraficoEnum {

    IND_TOTAL_ALUNOS("total", "v3", "#08298A", "Total de alunos", "column", "0.5", "square"),
    IND_ATIVOS("ativos", "v3", "#228B22", "Ativos", "line", "0.5", "triangleUp"),
    IND_INATIVOS("inativos", "v3", "#D2691E", "Inativos", "line", "0.5", "diamond"),
    IND_ATIVOS_TREINO("ativostreino", "v3", "#DAA520", "Ativos com treino", "line", "0.5", "triangleDown"),
    IND_EM_DIA("emdia", "v3", "#8B7355", "Treinos em dia", "line", "0.5", "triangleDown"),
    IND_VENCIDOS("treinosvencidos", "v3", "#EE6363", "Treinos vencidos", "line", "0.5", "round"),
    IND_RENOVAR("treinosrenovar", "v3", "#8B008B", "Treinos a renovar", "line", "0.5", "triangleLeft"),
    IND_AGENDAMENTOS("agendamentos", "v3", "#008B8B", "Agendamentos", "line", "0.5", "triangleLeft"),
    IND_AGENDAMENTOS_EXECUTARAM("agexecutaram", "v3", "#FF4500", "Ag. executados", "line", "0.5", "triangleLeft"),
    IND_AGENDAMENTOS_FALTARAM("agfaltaram", "v3", "#EDCDE6", "Ag. faltaram", "line", "0.5", "triangleLeft"),
    IND_AGENDAMENTOS_CANCELARAM("agcancelaram", "v3", "#58B08E", "Cancelaram agendamento", "line", "0.5", "triangleRight"),
    IND_RENOVADOS("renovados", "v3", "#FFB8A1", "Renovados", "line", "0.5", "triangleLeft"),
    IND_NAO_RENOVADOS("naorenovados", "v3", "#878EB5", "Não renovados", "line", "0.5", "triangleLeft"),
    IND_A_VENCER("avencer", "v3", "#D7DCA5", "A vencer", "line", "0.5", "triangleLeft"),
    IND_NOVOS_CARTEIRA("novoscarteira", "v3", "#BBFF83", "Novos na carteira", "line", "0.5", "square"),
    IND_TROCARAM_CARTEIRA("trocaramcarteira", "v3", "#79706D", "Trocaram de carteira", "line", "0.5", "square"),
    IND_TEMPO_MEDIO_CARTEIRA("mediocarteira", "v3", "#13A4FF", "Tempo médio na carteira", "column", "0.5", "square"),
    IND_SEM_TREINO("semtreino", "v3", "#00EB60", "Sem treino", "line", "0.5", "triangleUp"),
    IND_PERC_TREINO_EM_DIA("percentualemdia", "v3", "#FFCC21", "Perc. treino em dia", "line", "0.5", "diamond"),
    IND_PERC_TREINO_VENCIDOS("percentualvencidos", "v3", "#EB0603", "Perc. treino vencidos", "line", "0.5", "triangleDown"),
    IND_TEMPO_MEDIO_PROGRAMA("mediorprograma", "v3", "#6C00C4", "Tempo médio programa", "line", "0.5", "round"),
    IND_NR_AVALIACOES("nravaliacoes", "v3", "#1CE8D4", "Nr. avaliações", "line", "0.5", "triangleLeft"),
    IND_MEDIA_AVALIACOES("avaliacoes", "v3", "#13A4FF", "Média de avaliações", "line", "0.5", "triangleLeft"),
    IND_ESTRELA_1("estrelaum", "v3", "#607D8B", "1 estrela", "line", "0.5", "triangleLeft"),
    IND_ESTRELA_2("estreladois", "v3", "#E91E63", "2 estrelas", "line", "0.5", "triangleLeft"),
    IND_ESTRELA_3("estrelatres", "v3", "#00B06D", "3 estrelas", "line", "0.5", "triangleRight"),
    IND_ESTRELA_4("estrelaquatro", "v3", "#FF5722", "4 estrelas", "line", "0.5", "triangleLeft"),
    IND_ESTRELA_5("estrelacinco", "v3", "#3F51B5", "5 estrelas", "line", "0.5", "triangleLeft"),
    IND_COM_AVALIACAO("comavaliacao", "v3", "#666600", "Alunos com avaliação", "line", "0.5", "triangleLeft"),
    IND_SEM_AVALIACAO("semavaliacao", "v3", "#CC0000", "Alunos sem avaliação", "line", "0.5", "square"),
    IND_AGENDAMENTO_PROFESSORES("agprofessores", "v3", "#300057", "Professores da agenda", "line", "0.5", "round"),
    IND_HORAS_DISPONIBILIDADE("horasdisponibilidade", "v3", "#0D685F", "Horas de disponibilidade", "line", "0.5", "triangleLeft"),
    IND_HORAS_EXECUTADAS("horasexecutadas", "v3", "#095280", "Horas executadas", "line", "0.5", "triangleLeft"),
    IND_PERC_OCUPACAO("percocupacao", "v3", "#990300", "% de ocupação da agenda", "line", "0.5", "triangleDown"),
    IND_AGENDAMENTO_NOVOS_TREINOS("agnovostreinos", "v3", "#806610", "Agendamentos de novos treinos", "line", "0.5", "diamond"),
    IND_AGENDAMENTO_TREINO_RENOVADOS("agtreinosrenovados", "v3", "#006C2C", "Agendamentos de renovação de treino", "line", "0.5", "triangleUp"),
    IND_AGENDAMENTO_TREINO_REVISADOS("agtreinosrevisados", "v3", "#095280", "Agendamentos de revisão de treino", "column", "0.5", "square"),
    IND_AGENDAMENTO_AVALIACOES_FISICAS("agavaliacaofisica", "v3", "#795548", "Agendamentos de avaliação física", "line", "0.5", "square"),
    IND_PERCENTUAL_RENOVACAO("percentualrenovacao", "v3", "#8A4B08", "Percentual de renovação", "line", "0.5", "square");

    private IndicadorGraficoEnum(String valueField, String valueAxis, String cor, String title, String tipo, String largura, String round) {
        this.valueField = valueField;
        this.valueAxis = valueAxis;
        this.cor = cor;
        this.title = title;
        this.tipo = tipo;
        this.round = round;
        this.largura = largura;
    }
    private static Map<String, String> simbols;
    private String valueField;
    private String valueAxis;
    private String cor;
    private String title;
    private String tipo;
    private String round;
    private String largura;

    public static Map<String, String> getSimbols() {
        if (simbols == null) {
            simbols = new HashMap<String, String>();
            simbols.put("triangleUp", "&#9650;");
            simbols.put("diamond", "&#9670;");
            simbols.put("triangleDown", "&#9660;");
            simbols.put("round", "&#9679;");
            simbols.put("triangleLeft", "&#9664;");
            simbols.put("triangleRight", "&#9654;");
            simbols.put("column", "&#9646;");
            simbols.put("square", "&#9632;");
        }
        return simbols;
    }

    public String getValueField() {
        return valueField;
    }

    public void setValueField(String valueField) {
        this.valueField = valueField;
    }

    public String getValueAxis() {
        return valueAxis;
    }

    public void setValueAxis(String valueAxis) {
        this.valueAxis = valueAxis;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getRound() {
        return round;
    }

    public void setRound(String round) {
        this.round = round;
    }

    public String getLargura() {
        return largura;
    }

    public void setLargura(String largura) {
        this.largura = largura;
    }
}
