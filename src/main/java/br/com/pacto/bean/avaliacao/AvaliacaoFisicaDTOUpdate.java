package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Siqueira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFisicaDTOUpdate implements Serializable {
    private Integer id;
    private Long dataAvaliacao;
    private Long dataProxima;
    private Integer anamneseSelecionadaId;
    private List<String> objetivos;
    private List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas;
    private List<AnamnesePerguntaRespostaDTOUpdate> parQRespostas;
    private AvaliacaoPesoAlturaDTO pesoAltura;
    private AvaliacaoDobrasDTO dobras;
    private AvaliacaoPerimetriaDTO perimetria;
    private AvaliacaoFlexibilidadeDTOUpdate flexibilidade;
    private AvaliacaoPosturaDTOUpdate postura;
    private AvaliacaoRMLDTOUpdate rml;
    private AvaliacaoVo2DTO vo2;
    private AvaliacaoSomatotipiaDTO somatotipia;
    private AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;
    private boolean temImportacaoBiosanny;
    private String origemAvaliacao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) { this.dataProxima = dataProxima;
    }

    public Integer getAnamneseSelecionadaId() {
        return anamneseSelecionadaId;
    }

    public void setAnamneseSelecionadaId(Integer anamneseSelecionadaId) {
        this.anamneseSelecionadaId = anamneseSelecionadaId;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getParQRespostas() {
        return parQRespostas;
    }

    public void setParQRespostas(List<AnamnesePerguntaRespostaDTOUpdate> parQRespostas) {
        this.parQRespostas = parQRespostas;
    }

    public AvaliacaoPesoAlturaDTO getPesoAltura() {
        return pesoAltura;
    }

    public void setPesoAltura(AvaliacaoPesoAlturaDTO pesoAltura) {
        this.pesoAltura = pesoAltura;
    }

    public AvaliacaoDobrasDTO getDobras() {
        return dobras;
    }

    public void setDobras(AvaliacaoDobrasDTO dobras) {
        this.dobras = dobras;
    }

    public AvaliacaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(AvaliacaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public AvaliacaoFlexibilidadeDTOUpdate getFlexibilidade() {
        return flexibilidade;
    }

    public void setFlexibilidade(AvaliacaoFlexibilidadeDTOUpdate flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public AvaliacaoPosturaDTOUpdate getPostura() {
        return postura;
    }

    public void setPostura(AvaliacaoPosturaDTOUpdate postura) {
        this.postura = postura;
    }

    public AvaliacaoRMLDTOUpdate getRml() {
        return rml;
    }

    public void setRml(AvaliacaoRMLDTOUpdate rml) {
        this.rml = rml;
    }

    public AvaliacaoVo2DTO getVo2() {
        return vo2;
    }

    public void setVo2(AvaliacaoVo2DTO vo2) {
        this.vo2 = vo2;
    }

    public AvaliacaoSomatotipiaDTO getSomatotipia() {
        return somatotipia;
    }

    public void setSomatotipia(AvaliacaoSomatotipiaDTO somatotipia) {
        this.somatotipia = somatotipia;
    }

    public AvaliacaoMetaRecomendacoesDTO getMetaRecomendacoes() {
        return metaRecomendacoes;
    }

    public void setMetaRecomendacoes(AvaliacaoMetaRecomendacoesDTO metaRecomendacoes) {
        this.metaRecomendacoes = metaRecomendacoes;
    }

    public boolean getTemImportacaoBiosanny() {
        return temImportacaoBiosanny;
    }

    public void setTemImportacaoBiosanny(boolean temImportacaoBiosanny) {
        this.temImportacaoBiosanny = temImportacaoBiosanny;
    }

    public String getOrigemAvaliacao() {
        return origemAvaliacao;
    }

    public void setOrigemAvaliacao(String origemAvaliacao) {
        this.origemAvaliacao = origemAvaliacao;
    }
}
