package br.com.pacto.bean.avaliacao;

/**
 * <AUTHOR> <PERSON> 29/01/2019
 */
public enum AerobicobancoColunaEnum {

    ENTREO_20_29("escolhido vo_1"),
    ENTREO_30_39("escolhido vo_2"),
    ENTREO_40_49("escolhido vo_3"),
    ENTREO_50_29("escolhido vo_4"),
    MAIOR_60("escolhido vo_5");

    private String descricao;

    AerobicobancoColunaEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static AerobicobancoColunaEnum getInstance(final String str) {
        for (AerobicobancoColunaEnum coluna : AerobicobancoColunaEnum.values()){
            if (str.equalsIgnoreCase(coluna.getDescricao())){
                return coluna;
            }
        }
        return ENTREO_20_29;
    }

}
