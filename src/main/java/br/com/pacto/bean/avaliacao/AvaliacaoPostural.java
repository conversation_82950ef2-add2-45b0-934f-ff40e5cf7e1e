/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import java.io.ByteArrayInputStream;
import java.util.List;
import javax.persistence.*;

import br.com.pacto.objeto.Aplicacao;
import org.hibernate.envers.Audited;
import br.com.pacto.controller.to.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AvaliacaoPostural {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    @Enumerated(EnumType.ORDINAL)
    private AssimetriaEnum quadril = AssimetriaEnum.NENHUMA_ELEVACAO;
    @Enumerated(EnumType.ORDINAL)
    private AssimetriaEnum ombros = AssimetriaEnum.NENHUMA_ELEVACAO;
    @OneToOne
    private AvaliacaoFisica avaliacao;
    @OneToMany(mappedBy = "avaliacaoPostural", cascade = CascadeType.REMOVE)
    private List<ItemAvaliacaoPostural> itemsAvaliacaoPostural;
    private String keyImgAnterior;
    private String keyImgPosterior;
    private String keyImgEsquerda;
    private String keyImgDireita;
    @Transient
    private UploadedFile imgAnterior;
    @Transient
    private UploadedFile imgPosterior;
    @Transient
    private UploadedFile imgEsquerda;
    @Transient
    private UploadedFile imgDireita;

    @Transient
    private byte[] imgAnteriorByte;
    @Transient
    private byte[] imgPosteriorByte;
    @Transient
    private byte[] imgEsquerdaByte;
    @Transient
    private byte[] imgDireitaByte;

    public AvaliacaoPostural() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public UploadedFile getImgAnterior() {
        return imgAnterior;
    }

    public void setImgAnterior(UploadedFile imgAnterior) {
        this.imgAnterior = imgAnterior;
    }

    public UploadedFile getImgPosterior() {
        return imgPosterior;
    }

    public void setImgPosterior(UploadedFile imgPosterior) {
        this.imgPosterior = imgPosterior;
    }

    public UploadedFile getImgEsquerda() {
        return imgEsquerda;
    }

    public void setImgEsquerda(UploadedFile imgEsquerda) {
        this.imgEsquerda = imgEsquerda;
    }

    public UploadedFile getImgDireita() {
        return imgDireita;
    }

    public void setImgDireita(UploadedFile imgDireita) {
        this.imgDireita = imgDireita;
    }


    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public AvaliacaoFisica getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(AvaliacaoFisica avaliacao) {
        this.avaliacao = avaliacao;
    }

    public AssimetriaEnum getQuadril() {
        return quadril;
    }

    public void setQuadril(AssimetriaEnum quadril) {
        this.quadril = quadril;
    }

    public AssimetriaEnum getOmbros() {
        return ombros;
    }

    public void setOmbros(AssimetriaEnum ombros) {
        this.ombros = ombros;
    }

    public String getKeyImgAnterior() {
        return keyImgAnterior;
    }

    public void setKeyImgAnterior(String keyImgAnterior) {
        this.keyImgAnterior = keyImgAnterior;
    }

    public String getKeyImgPosterior() {
        return keyImgPosterior;
    }

    public void setKeyImgPosterior(String keyImgPosterior) {
        this.keyImgPosterior = keyImgPosterior;
    }

    public String getKeyImgEsquerda() {
        return keyImgEsquerda;
    }

    public void setKeyImgEsquerda(String keyImgEsquerda) {
        this.keyImgEsquerda = keyImgEsquerda;
    }

    public String getKeyImgDireita() {
        return keyImgDireita;
    }

    public void setKeyImgDireita(String keyImgDireita) {
        this.keyImgDireita = keyImgDireita;
    }

    public String getUrlFotoCosta(){
        return keyImgAnterior == null ? "" : Aplicacao.obterUrlFotoDaNuvem(keyImgAnterior);
    }
    public String getUrlFotoDireita(){
        return keyImgDireita == null ? "" : Aplicacao.obterUrlFotoDaNuvem(keyImgDireita);
    }
    public String getUrlFotoEsquerda(){
        return keyImgEsquerda == null ? "" : Aplicacao.obterUrlFotoDaNuvem(keyImgEsquerda);
    }

    public String getUrlFotoFrente(){
        return keyImgPosterior == null ? "" : Aplicacao.obterUrlFotoDaNuvem(keyImgPosterior);
    }

    public byte[] getImgAnteriorByte() {
        return imgAnteriorByte;
    }

    public void setImgAnteriorByte(byte[] imgAnteriorByte) {
        this.imgAnteriorByte = imgAnteriorByte;
    }

    public byte[] getImgPosteriorByte() {
        return imgPosteriorByte;
    }

    public void setImgPosteriorByte(byte[] imgPosteriorByte) {
        this.imgPosteriorByte = imgPosteriorByte;
    }

    public byte[] getImgEsquerdaByte() {
        return imgEsquerdaByte;
    }

    public void setImgEsquerdaByte(byte[] imgEsquerdaByte) {
        this.imgEsquerdaByte = imgEsquerdaByte;
    }

    public byte[] getImgDireitaByte() {
        return imgDireitaByte;
    }

    public void setImgDireitaByte(byte[] imgDireitaByte) {
        this.imgDireitaByte = imgDireitaByte;
    }
}
