package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoDobraPontoDTO implements Serializable {

    private Long data;
    private Double valor;

    public EvolucaoDobraPontoDTO() {
    }

    public EvolucaoDobraPontoDTO(Long data, Double valor) {
        this.data = data;
        this.valor = valor;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
