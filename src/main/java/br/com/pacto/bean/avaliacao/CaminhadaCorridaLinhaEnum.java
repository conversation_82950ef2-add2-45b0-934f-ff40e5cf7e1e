package br.com.pacto.bean.avaliacao;

/**
 * <AUTHOR> 29/01/2019
 */
public enum CaminhadaCorridaLinhaEnum {

    MUITA_FRACA("muito.fraca"),
    FRACA,
    MEDIA,
    BOA,
    EXCELENTE,
    SUPERIOR("superior");

    private String descricao;

    CaminhadaCorridaLinhaEnum(String descricao) {
        this.descricao = descricao;
    }

    CaminhadaCorridaLinhaEnum() {

    }

    public String getDescricao() {
        return descricao;
    }

    public static CaminhadaCorridaLinhaEnum getInstance(final String str) throws Exception {
        for (CaminhadaCorridaLinhaEnum linha : CaminhadaCorridaLinhaEnum.values()){
            if (str.equalsIgnoreCase(linha.getDescricao())){
                return linha;
            }
        }
        return MUITA_FRACA;
    }
}
