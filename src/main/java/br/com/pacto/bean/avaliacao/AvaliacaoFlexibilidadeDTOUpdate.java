package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFlexibilidadeDTOUpdate implements Serializable {

    private Integer alcanceMaximo;
    private String observacao;
    private String mobilidadeOmbroEsquerdo;
    private String mobilidadeOmbroDireito;
    private String mobilidadeQuadrilEsquerdo;
    private String mobilidadeQuadrilDireito;
    private String mobilidadeJoelhoEsquerdo;
    private String mobilidadeJoelhoDireito;
    private Integer mobilidadeTornozeloEsquerdo;
    private Integer mobilidadeTornozeloDireito;
    private String observacaoOmbro;
    private String observacaoQuadril;
    private String observacaoJoelho;
    private String observacaoTornozelo;

    public Integer getAlcanceMaximo() {
        return alcanceMaximo;
    }

    public void setAlcanceMaximo(Integer alcanceMaximo) {
        this.alcanceMaximo = alcanceMaximo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getMobilidadeOmbroEsquerdo() {
        return mobilidadeOmbroEsquerdo;
    }

    public void setMobilidadeOmbroEsquerdo(String mobilidadeOmbroEsquerdo) {
        this.mobilidadeOmbroEsquerdo = mobilidadeOmbroEsquerdo;
    }

    public String getMobilidadeOmbroDireito() {
        return mobilidadeOmbroDireito;
    }

    public void setMobilidadeOmbroDireito(String mobilidadeOmbroDireito) {
        this.mobilidadeOmbroDireito = mobilidadeOmbroDireito;
    }

    public String getMobilidadeJoelhoEsquerdo() {
        return mobilidadeJoelhoEsquerdo;
    }

    public void setMobilidadeJoelhoEsquerdo(String mobilidadeJoelhoEsquerdo) {
        this.mobilidadeJoelhoEsquerdo = mobilidadeJoelhoEsquerdo;
    }

    public String getMobilidadeJoelhoDireito() {
        return mobilidadeJoelhoDireito;
    }

    public void setMobilidadeJoelhoDireito(String mobilidadeJoelhoDireito) {
        this.mobilidadeJoelhoDireito = mobilidadeJoelhoDireito;
    }

    public String getMobilidadeQuadrilDireito() {
        return mobilidadeQuadrilDireito;
    }

    public void setMobilidadeQuadrilDireito(String mobilidadeQuadrilDireito) {
        this.mobilidadeQuadrilDireito = mobilidadeQuadrilDireito;
    }

    public String getMobilidadeQuadrilEsquerdo() {
        return mobilidadeQuadrilEsquerdo;
    }
    public void setMobilidadeQuadrilEsquerdo(String mobilidadeQuadrilEsquerdo) {
        this.mobilidadeQuadrilEsquerdo = mobilidadeQuadrilEsquerdo;
    }
    public String getObservacaoOmbro() {
        return observacaoOmbro;
    }

    public void setObservacaoOmbro(String observacaoOmbro) {
        this.observacaoOmbro = observacaoOmbro;
    }

    public String getObservacaoJoelho() {
        return observacaoJoelho;
    }

    public void setObservacaoJoelho(String observacaoJoelho) {
        this.observacaoJoelho = observacaoJoelho;
    }

    public String getObservacaoQuadril() {
        return observacaoQuadril;
    }

    public void setObservacaoQuadril(String observacaoQuadril) {
        this.observacaoQuadril = observacaoQuadril;
    }

    public String getObservacaoTornozelo() {
        return observacaoTornozelo;
    }

    public void setObservacaoTornozelo(String observacaoTornozelo) {
        this.observacaoTornozelo = observacaoTornozelo;
    }


    public Integer getMobilidadeTornozeloEsquerdo() {
        return mobilidadeTornozeloEsquerdo;
    }

    public void setMobilidadeTornozeloEsquerdo(Integer mobilidadeTornozeloEsquerdo) {
        this.mobilidadeTornozeloEsquerdo = mobilidadeTornozeloEsquerdo;
    }

    public Integer getMobilidadeTornozeloDireito() {
        return mobilidadeTornozeloDireito;
    }

    public void setMobilidadeTornozeloDireito(Integer mobilidadeTornozeloDireito) {
        this.mobilidadeTornozeloDireito = mobilidadeTornozeloDireito;
    }

}

