package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoPerimetriaItemAtividade implements Serializable {

    private String nomeAtividade;
    private Double porcentual;

    public EvolucaoPerimetriaItemAtividade() {
    }

    public EvolucaoPerimetriaItemAtividade(String nomeAtividade, Double porcentual) {
        this.nomeAtividade = nomeAtividade;
        this.porcentual = porcentual;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public Double getPorcentual() {
        return porcentual;
    }

    public void setPorcentual(Double porcentual) {
        this.porcentual = porcentual;
    }
}
