package br.com.pacto.bean.avaliacao;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.GenericoTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by alcides on 03/10/2017.
 */
public class EvolucaoFisica {

    private List<Map<String, Object>> grupos = new ArrayList<Map<String, Object>>();
    private List<Map<String, Object>> gruposPrograma = new ArrayList<Map<String, Object>>();
    private List<Map<String, Object>> pesoXgordura = new ArrayList<Map<String, Object>>();
    private List<Map<String, Object>> dobras = new ArrayList<Map<String, Object>>();
    private Map<PerimetriaEnum, List<GenericoTO>> atividades = new HashMap<PerimetriaEnum, List<GenericoTO>>();
    private Integer nrAvaliacoes = 0;
    private Integer treinosPeriodo = 0;
    private String mediaSemanal = "";
    private String msgNrAvaliacoes = "";
    private String sitPercentual = "";
    private String sitPercentualMagra = "";
    private String msgPercentual = "";
    private String msgPercentualMagra = "";
    private Double percentualGordura = 0.0;
    private Double percentualMassaMagra = 0.0;
    private Double massaGorda = 0.0;
    private Double massaMagra = 0.0;
    private Double massaMagraInicial = 0.0;
    private Double massaGordaInicial = 0.0;
    private Double meta = 0.0;

    public List<Map<String, Object>> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<Map<String, Object>> grupos) {
        this.grupos = grupos;
    }

    public Map<PerimetriaEnum, List<GenericoTO>> getAtividades() {
        return atividades;
    }

    public void setAtividades(Map<PerimetriaEnum, List<GenericoTO>> atividades) {
        this.atividades = atividades;
    }

    public Integer getNrAvaliacoes() {
        return nrAvaliacoes;
    }

    public void setNrAvaliacoes(Integer nrAvaliacoes) {
        this.nrAvaliacoes = nrAvaliacoes;
    }

    public Integer getTreinosPeriodo() {
        return treinosPeriodo;
    }

    public void setTreinosPeriodo(Integer treinosPeriodo) {
        this.treinosPeriodo = treinosPeriodo;
    }

    public String getMediaSemanal() {
        return mediaSemanal;
    }

    public void setMediaSemanal(String mediaSemanal) {
        this.mediaSemanal = mediaSemanal;
    }

    public String getMsgNrAvaliacoes() {
        return msgNrAvaliacoes;
    }

    public void setMsgNrAvaliacoes(String msgNrAvaliacoes) {
        this.msgNrAvaliacoes = msgNrAvaliacoes;
    }

    public String getSitPercentual() {
        return sitPercentual;
    }

    public void setSitPercentual(String sitPercentual) {
        this.sitPercentual = sitPercentual;
    }

    public String getMsgPercentual() {
        return msgPercentual;
    }

    public void setMsgPercentual(String msgPercentual) {
        this.msgPercentual = msgPercentual;
    }

    public Double getPercentualGordura() {
        return percentualGordura;
    }

    public Integer getPercentualGorduraInt() {
        return percentualGordura == null ? 0 : percentualGordura.intValue();
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getMassaGordaInicial() {
        return massaGordaInicial;
    }

    public void setMassaGordaInicial(Double massaGordaInicial) {
        this.massaGordaInicial = massaGordaInicial;
    }

    public List<Map<String, Object>> getPesoXgordura() {
        return pesoXgordura;
    }

    public void setPesoXgordura(List<Map<String, Object>> pesoXgordura) {
        this.pesoXgordura = pesoXgordura;
    }

    public List<Map<String, Object>> getDobras() {
        return dobras;
    }

    public void setDobras(List<Map<String, Object>> dobras) {
        this.dobras = dobras;
    }

    public List<Map<String, Object>> getGruposPrograma() {
        return gruposPrograma;
    }

    public void setGruposPrograma(List<Map<String, Object>> gruposPrograma) {
        this.gruposPrograma = gruposPrograma;
    }

    public Double getMassaMagra() {
        return massaMagra;
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getMassaMagraInicial() {
        return massaMagraInicial;
    }

    public void setMassaMagraInicial(Double massaMagraInicial) {
        this.massaMagraInicial = massaMagraInicial;
    }

    public Double getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public String getSitPercentualMagra() {
        return sitPercentualMagra;
    }

    public void setSitPercentualMagra(String sitPercentualMagra) {
        this.sitPercentualMagra = sitPercentualMagra;
    }

    public String getMsgPercentualMagra() {
        return msgPercentualMagra;
    }

    public void setMsgPercentualMagra(String msgPercentualMagra) {
        this.msgPercentualMagra = msgPercentualMagra;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public String getMetaApresentar(){
        return UteisValidacao.emptyNumber(meta) ? " - " : Uteis.formatarValorNumerico(meta);
    }

}
