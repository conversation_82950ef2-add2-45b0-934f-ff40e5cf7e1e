/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;


/**
 *
 * <AUTHOR>
 */
public enum ItemPosturaEnum {
    ANTERVERSAO_QUADRIL("anterversaoQuadril", TipoItemPosturaEnum.LATERAL),
    RETROVERSAO_QUADRIL("retroversaoQuadril", TipoItemPosturaEnum.LATERAL),
    ROTACAO_INTERNA_OMBROS("rotacaoInternaOmbros", TipoItemPosturaEnum.LATERAL),
    RETIFICACAO_CERVICAL("retificacaoCervical", TipoItemPosturaEnum.LATERAL),
    RETIFICACAO_LOMBAR("retificacaoLombar", TipoItemPosturaEnum.LATERAL),
    PROTUSAO_ABDOMINAL("protusaoAbdominal", TipoItemPosturaEnum.LATERAL),
    HIPERLORDOSE_CERVICAL("hiperlordoseCervical", TipoItemPosturaEnum.LATERAL),
    HIPERLORDOSE_LOMBAR("hiperlordoseLombar", TipoItemPosturaEnum.LATERAL),
    HIPERCIFOSE_TORACICA("hipercifoseToracica", TipoItemPosturaEnum.LATERAL),
    PE_PLANO("pePlano", TipoItemPosturaEnum.LATERAL),
    PE_CAVO("peCavo", TipoItemPosturaEnum.LATERAL),
    PE_CALCANEO("peCalcaneo", TipoItemPosturaEnum.LATERAL),
    PE_EQUINO("peEquino", TipoItemPosturaEnum.LATERAL),
    GENU_FLEXO("joelhoFlexo", TipoItemPosturaEnum.LATERAL),
    GENU_RECURVADO("joelhoRecurvado", TipoItemPosturaEnum.LATERAL),

    ESCOLIOSE_CERVICAL("escolioseCervical", TipoItemPosturaEnum.POSTERIOR),
    ESCOLIOSE_TORACICA("escolioseToracica", TipoItemPosturaEnum.POSTERIOR),
    ESCOLIOSE_LOMBAR("escolioseLombar", TipoItemPosturaEnum.POSTERIOR),
    PROTACAO_ESCPULAR("protacaoEscapular", TipoItemPosturaEnum.POSTERIOR),
    RETRACAO_ESCAPULAR("retracaoEscapular", TipoItemPosturaEnum.POSTERIOR),
    DEPRESSAO_ESCAPULAR("depressaoEscapular", TipoItemPosturaEnum.POSTERIOR),
    PE_VAGO("peValgo", TipoItemPosturaEnum.POSTERIOR),
    PE_VARO("peVaro", TipoItemPosturaEnum.POSTERIOR),
    ENCURTAMENTO_TRAPEZIO("encurtamentoTrapezio", TipoItemPosturaEnum.POSTERIOR),

    GENU_VALGO("joelhoValgo", TipoItemPosturaEnum.ANTERIOR),
    GENU_VARO("joelhoVaro", TipoItemPosturaEnum.ANTERIOR),
    PE_ADUTO("peAduto" ,TipoItemPosturaEnum.ANTERIOR),
    PE_ABDUTO("peAbduto", TipoItemPosturaEnum.ANTERIOR),

    OMBROS_ASSIMETRICOS("", TipoItemPosturaEnum.ANTERIOR,
            AssimetriaEnum.NENHUMA_ELEVACAO, AssimetriaEnum.ELEVECAO_DIREITO, AssimetriaEnum.ELEVECAO_ESQUERDO),

    ASSIMETRIA_QUADRIL("", TipoItemPosturaEnum.ANTERIOR,
            AssimetriaEnum.NENHUMA_ELEVACAO, AssimetriaEnum.ELEVECAO_PELVE_DIREITA,
            AssimetriaEnum.ELEVECAO_PELVE_ESQUERDA),

    ;

    ItemPosturaEnum(String field, TipoItemPosturaEnum tipo, AssimetriaEnum ... as) {
        this.field = field;
        this.tipo = tipo;
        this.assimetrias = as;

    }

    private String field;
    private TipoItemPosturaEnum tipo;
    private AssimetriaEnum[] assimetrias;

    public String getField() {
        return field;
    }

    public TipoItemPosturaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoItemPosturaEnum tipo) {
        this.tipo = tipo;
    }

    public AssimetriaEnum[] getAssimetrias() {
        return assimetrias;
    }

    public void setAssimetrias(AssimetriaEnum[] assimetrias) {
        this.assimetrias = assimetrias;
    }

    public Integer getOrdem(){
        return ordinal();
    }


}
