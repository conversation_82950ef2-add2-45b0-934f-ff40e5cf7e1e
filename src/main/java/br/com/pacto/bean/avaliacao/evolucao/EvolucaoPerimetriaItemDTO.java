package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoPerimetriaItemDTO implements Serializable {

    private String nome;
    private Collection<EvolucaoPerimetriaItemPontoDTO> valores;
    private Collection<EvolucaoPerimetriaItemAtividade> atividades;

    public EvolucaoPerimetriaItemDTO(String nome, Collection<EvolucaoPerimetriaItemPontoDTO> valores, Collection<EvolucaoPerimetriaItemAtividade> atividades) {
        this.nome = nome;
        this.valores = valores;
        this.atividades = atividades;
    }

    public EvolucaoPerimetriaItemDTO() {
        this.nome = "";
        this.valores = new ArrayList<EvolucaoPerimetriaItemPontoDTO>();
        this.atividades = new ArrayList<EvolucaoPerimetriaItemAtividade>();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Collection<EvolucaoPerimetriaItemPontoDTO> getValores() {
        return valores;
    }

    public void setValores(Collection<EvolucaoPerimetriaItemPontoDTO> valores) {
        this.valores = valores;
    }

    public Collection<EvolucaoPerimetriaItemAtividade> getAtividades() {
        return atividades;
    }

    public void setAtividades(Collection<EvolucaoPerimetriaItemAtividade> atividades) {
        this.atividades = atividades;
    }
}
