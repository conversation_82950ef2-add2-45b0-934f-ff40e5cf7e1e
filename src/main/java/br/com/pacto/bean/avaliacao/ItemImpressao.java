package br.com.pacto.bean.avaliacao;

import br.com.pacto.objeto.ColumnModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Rafael on 08/02/2017.
 */
public class ItemImpressao {
    private List<Object> values;
    private List<ColumnModel> columns;
    private AvaliacaoFisica avaliacaoFisica = null;


    public List<Object> getValues() {
        return values;
    }

    public void setValues(List<Object> values) {
        this.values = values;
    }

    public List<ColumnModel> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnModel> columns) {
        this.columns = columns;
    }

    public AvaliacaoFisica getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(AvaliacaoFisica avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }
}
