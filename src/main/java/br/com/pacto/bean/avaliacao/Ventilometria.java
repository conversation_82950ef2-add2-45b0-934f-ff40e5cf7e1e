package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.cliente.ClienteSintetico;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 06/06/2017.
 */
@Entity
@Table
public class Ventilometria {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double vo2max= 0.0;
    private Double limiarVentilatorio= 0.0;
    private Double limiarVentilatorio2= 0.0;
    @ManyToOne
    private ClienteSintetico cliente;
    @OneToOne
    private AvaliacaoFisica avaliacao;
    @Transient
    private Boolean masculino = Boolean.TRUE;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getVo2max() {
        return vo2max;
    }

    public void setVo2max(Double vo2max) {
        this.vo2max = vo2max;
    }

    public Double getLimiarVentilatorio() {
        return limiarVentilatorio;
    }

    public void setLimiarVentilatorio(Double limiarVentilatorio) {
        this.limiarVentilatorio = limiarVentilatorio;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Boolean getMasculino() {
        return masculino;
    }

    public void setMasculino(Boolean masculino) {
        this.masculino = masculino;
    }

    public Double getLimiarVentilatorio2() {
        return limiarVentilatorio2;
    }

    public void setLimiarVentilatorio2(Double limiarVentilatorio2) {
        this.limiarVentilatorio2 = limiarVentilatorio2;
    }

    public AvaliacaoFisica getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(AvaliacaoFisica avaliacao) {
        this.avaliacao = avaliacao;
    }
    
    
}
