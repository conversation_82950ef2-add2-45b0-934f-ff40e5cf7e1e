package br.com.pacto.bean.avaliacao;

/**
 * <AUTHOR> <PERSON> 29/01/2019
 */
public enum Teste2400MetrosColunaEnum {

    MENOR_30("escolhido vo_1"),
    ENTREO_30_39("escolhido vo_2"),
    ENTREO_40_39("escolhido vo_3"),
    MAIOR_50("escolhido vo_4");

    private String descricao;

    Teste2400MetrosColunaEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static Teste2400MetrosColunaEnum getInstance(final String str) {
        for (Teste2400MetrosColunaEnum coluna : Teste2400MetrosColunaEnum.values()){
            if (str .equalsIgnoreCase(coluna.getDescricao())){
                return coluna;
            }
        }
        return MENOR_30;
    }
}
