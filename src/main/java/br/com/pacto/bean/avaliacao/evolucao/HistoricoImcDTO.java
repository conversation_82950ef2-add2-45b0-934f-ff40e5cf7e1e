package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 18/02/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HistoricoImcDTO implements Serializable {

    private Long data;
    private Double imc;

    public HistoricoImcDTO(Long data, Double imc) {
        this.data = data;
        this.imc = imc;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }
}
