package br.com.pacto.bean.avaliacao;

public class SomatotipiaTO {

    private Double triceps = 0.0;
    private Double subescapular = 0.0;
    private Double panturrilha = 0.0;
    private Double supraEspinhal = 0.0;
    private Double punho = 0.0;
    private Double joelho = 0.0;
    private Double cotovelo = 0.0;
    private Double tornozelo = 0.0;
    private Double bracoDireito = 0.0;
    private Double panturrilhaDireita = 0.0;

    public SomatotipiaTO() {

    }
    public SomatotipiaTO(AvaliacaoFisica avaliacao, PesoOsseo peso) {
        this.triceps = avaliacao.getTriceps();
        this.subescapular = avaliacao.getSubescapular();
        this.panturrilha = avaliacao.getPanturrilha();
        this.supraEspinhal = avaliacao.getSupraEspinhal();
        this.punho = peso.getDiametroPunho();
        this.joelho = peso.getDiametroFemur();
        this.cotovelo = peso.getDiametroCotovelo();
        this.tornozelo = peso.getDiametroTornozelo();
        this.bracoDireito = avaliacao.getBracoContraidoDir();
        this.panturrilhaDireita = avaliacao.getPanturrilhaDir();
    }

    public Double getTriceps() {
        return triceps;
    }

    public void setTriceps(Double triceps) {
        this.triceps = triceps;
    }

    public Double getSubescapular() {
        return subescapular;
    }

    public void setSubescapular(Double subescapular) {
        this.subescapular = subescapular;
    }

    public Double getPanturrilha() {
        return panturrilha;
    }

    public void setPanturrilha(Double panturrilha) {
        this.panturrilha = panturrilha;
    }

    public Double getSupraEspinhal() {
        return supraEspinhal;
    }

    public void setSupraEspinhal(Double supraEspinhal) {
        this.supraEspinhal = supraEspinhal;
    }

    public Double getPunho() {
        return punho;
    }

    public void setPunho(Double punho) {
        this.punho = punho;
    }

    public Double getJoelho() {
        return joelho;
    }

    public void setJoelho(Double joelho) {
        this.joelho = joelho;
    }

    public Double getCotovelo() {
        return cotovelo;
    }

    public void setCotovelo(Double cotovelo) {
        this.cotovelo = cotovelo;
    }

    public Double getTornozelo() {
        return tornozelo;
    }

    public void setTornozelo(Double tornozelo) {
        this.tornozelo = tornozelo;
    }

    public Double getBracoDireito() {
        return bracoDireito;
    }

    public void setBracoDireito(Double bracoDireito) {
        this.bracoDireito = bracoDireito;
    }

    public Double getPanturrilhaDireita() {
        return panturrilhaDireita;
    }

    public void setPanturrilhaDireita(Double panturrilhaDireita) {
        this.panturrilhaDireita = panturrilhaDireita;
    }
}
