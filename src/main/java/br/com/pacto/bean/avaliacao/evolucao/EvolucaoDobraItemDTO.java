package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoDobraItemDTO implements Serializable {

    private String nome;
    private List<EvolucaoDobraPontoDTO> pontos;

    public EvolucaoDobraItemDTO() {
    }

    public EvolucaoDobraItemDTO(String nome, EvolucaoDobraPontoDTO pontos) {
        this.pontos = new ArrayList<>();
        this.nome = nome;
        this.pontos.add(pontos);
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<EvolucaoDobraPontoDTO> getPontos() {
        return pontos;
    }

    public void setPontos(List<EvolucaoDobraPontoDTO> pontos) {
        this.pontos = pontos;
    }
}
