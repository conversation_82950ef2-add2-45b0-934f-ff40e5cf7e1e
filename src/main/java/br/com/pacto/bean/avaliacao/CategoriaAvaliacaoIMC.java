package br.com.pacto.bean.avaliacao;

/**
 * Created by <PERSON> on 08/10/2016.
 */
public enum CategoriaAvaliacaoIMC {
    NENHUM(0,"categoria.avaliacao.imc.nenhum"),
    BAIXO(1,"categoria.avaliacao.imc.baixo"),
    NORMAL(2,"categoria.avaliacao.imc.normal"),
    SOBREPESO(3,"categoria.avaliacao.imc.sobrepeso"),
    OBESIDADE_CLASSE_I(4,"categoria.avaliacao.imc.obesidade1"),
    OBESIDADE_CLASSE_II(5,"categoria.avaliacao.imc.obesidade2"),
    OBESIDADE_CLASSE_III(6,"categoria.avaliacao.imc.obesidade3");

    private int codigo;
    private String descricao;

    CategoriaAvaliacaoIMC(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
