/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.cliente.ClienteSintetico;

import java.util.Date;
import javax.persistence.*;

import br.com.pacto.objeto.Uteis;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class PesoOsseo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double diametroPunho = 0.0;
    private Double diametroFemur = 0.0;
    private Double diametroCotovelo = 0.0;
    private Double diametroTornozelo = 0.0;
    private Double altura = 0.0;
    private Double peso = 0.0;
    private Double percentualGordura = 0.0;
    private Double pesoOsseo = 0.0;
    private Double pesoResidual = 0.0;
    private Double pesoMuscular = 0.0;
    private Double pesoGordura = 0.0;
    @OneToOne
    private AvaliacaoFisica avaliacaoFisica;
    private Integer responsavelLancamento_codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataLancamentoApresentar(){
        return dataLancamento == null ? "" : Uteis.getDataAplicandoFormatacao(dataLancamento,"dd/MM/yyyy HH:mm");
    }

    public Double getDiametroPunho() {
        if (diametroPunho == null) {
            diametroPunho = 0.0;
        }
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = (diametroPunho == null || diametroPunho.isNaN()) ? 0.0 : diametroPunho;
    }

    public String getDiametroFemurApresentar() {
        return apresentar(getDiametroFemur());
    }

    public String getDiametroPunhoApresentar() {
        return apresentar(getDiametroPunho());
    }
    public String getDiametroCotoveloApresentar() {
        return apresentar(getDiametroCotovelo());
    }

    public String getDiametroTornozeloApresentar() {
        return apresentar(getDiametroTornozelo());
    }


    public Double getDiametroFemur() {
        if (diametroFemur == null) {
            diametroFemur = 0.0;
        }
        return diametroFemur;
    }

    public void setDiametroFemur(Double diametroFemur) {
        this.diametroFemur = (diametroFemur == null || diametroFemur.isNaN()) ? 0.0 : diametroFemur;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = (altura == null || altura.isNaN()) ? 0.0 : altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = (peso == null || peso.isNaN()) ? 0.0 : peso;
    }

    public Double getPercentualGordura() {
        return percentualGordura;
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = (percentualGordura == null || percentualGordura.isNaN()) ? 0.0 : percentualGordura;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = (pesoOsseo == null || pesoOsseo.isNaN()) ? 0.0 : pesoOsseo;
    }

    public String getPesoOsseoApresentar() {
        return apresentar(pesoOsseo);
    }

    public String getAlturaApresentar() {
        return apresentar(altura);
    }

    public String getPesoApresentar() {
        return apresentar(peso);
    }

    public String getPercentualGorduraApresentar() {
        return apresentar(percentualGordura);
    }

    public String getPesoMuscularApresentar() {
        return apresentar(pesoMuscular);
    }

    public String getPesoGorduraApresentar() {
        return apresentar(pesoGordura);
    }

    public String getPesoResidualApresentar() {
        return apresentar(pesoResidual);
    }

    public Double getPesoMuscularArredondado() {
        return arredondar(pesoMuscular);
    }

    public Double getPesoGorduraArredondado() {
        return arredondar(pesoGordura);
    }

    public Double getPesoResidualArredondado() {
        return arredondar(pesoResidual);
    }

    public Double getPesoOsseoArredondado() {
        return arredondar(pesoOsseo);
    }

    public String apresentar(Double valor) {
        return valor == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(valor));
    }

    public Double arredondar(Double valor) {
        return valor == null || valor < 0.0 ? 0.0 : Uteis.arredondarForcando2CasasDecimais(valor);
    }

    public Double percentPeso(Double value){
        return peso == null || peso == 0.0 || value == null || value == 0.0 ?
                0.0 : ((value/peso)*100.0);
    }

    public Double getPercMuscular() {
        return percentPeso(pesoMuscular);
    }

    public String getPercMuscularApresentar() {
        return apresentar(percentPeso(pesoMuscular));
    }

    public String getPercResidualApresentar() {
        return apresentar(percentPeso(pesoResidual));
    }

    public String getPercOsseoApresentar() { return apresentar(percentPeso(pesoOsseo));}


    public Double getPesoResidual() {
        return pesoResidual;
    }

    public void setPesoResidual(Double pesoResidual) {
        this.pesoResidual = (pesoResidual == null || pesoResidual.isNaN()) ? 0.0 : pesoResidual;
    }

    public Double getPesoMuscular() {
        return pesoMuscular;
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = (pesoMuscular == null || pesoMuscular.isNaN()) ? 0.0 : pesoMuscular;
    }

    public Double getPesoGordura() {
        return pesoGordura;
    }

    public void setPesoGordura(Double pesoGordura) {
        this.pesoGordura = (pesoGordura == null || pesoGordura.isNaN()) ? 0.0 : pesoGordura;
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public AvaliacaoFisica getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(AvaliacaoFisica avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = (diametroCotovelo == null || diametroCotovelo.isNaN()) ? 0.0 : diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = (diametroTornozelo == null || diametroTornozelo.isNaN()) ? 0.0 : diametroTornozelo;
    }
}
