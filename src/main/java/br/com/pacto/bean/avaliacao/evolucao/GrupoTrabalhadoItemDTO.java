package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.text.DecimalFormat;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GrupoTrabalhadoItemDTO implements Serializable {
    private String nome;
    private Double exercicios;
    private Double porcentagem;

    public GrupoTrabalhadoItemDTO(String nome, Double exercicios, Integer total) {
        this.nome = nome;
        this.exercicios = exercicios;
        DecimalFormat formato = new DecimalFormat("#");
        this.porcentagem = Double.valueOf(formato.format((exercicios / total) * 100));
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public Double getExercicios() {
        return exercicios;
    }

    public void setExercicios(Double exercicios) {
        this.exercicios = exercicios;
    }
}
