package br.com.pacto.bean.benchmark;

import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.enumerador.crossfit.MidiaBenchmarkEnum;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Aplicacao;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;

/**
 * Created by <PERSON> on 11/07/2016.
 */
@Entity
@Table
public class Benchmark implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @ManyToOne
    private TipoBenchmark tipoBenchmark;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String descricaoExercicios;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    @Enumerated(EnumType.ORDINAL)
    private TipoWodEnum tipoWod;
    @Enumerated(EnumType.ORDINAL)
    private MidiaBenchmarkEnum tipoMidia;
    private String urlMidia;
    @OneToOne
    private TipoWod tipoWodTabela;
    private Boolean preDefinido = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoBenchmark getTipoBenchmark() {
        if(tipoBenchmark == null){
            tipoBenchmark = new TipoBenchmark();
        }
        return tipoBenchmark;
    }

    public void setTipoBenchmark(TipoBenchmark tipoBenchmark) {
        this.tipoBenchmark = tipoBenchmark;
    }

    public String getDescricaoExercicios() {
        return descricaoExercicios;
    }

    public void setDescricaoExercicios(String descricaoExercicios) {
        this.descricaoExercicios = descricaoExercicios;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public MidiaBenchmarkEnum getTipoMidia() {
        return tipoMidia;
    }

    public boolean isMidiaImagem(){
        return getTipoMidia() == MidiaBenchmarkEnum.IMAGEM;
    }
    public boolean isMidiaVideo(){
        return getTipoMidia() == MidiaBenchmarkEnum.VIDEO;
    }
    public void setTipoMidia(MidiaBenchmarkEnum tipoMidia) {
        this.tipoMidia = tipoMidia;
    }

    public String getUrlMidia() {
        Calendar calendar = Calendar.getInstance();
        return urlMidia == null ? "" : Aplicacao.obterUrlFotoDaNuvem(urlMidia) +"?"+ calendar.getTimeInMillis() ;
    }

    public String getUrlMidiaDireta() {
        Calendar calendar = Calendar.getInstance();
        return urlMidia == null ? "" : urlMidia +"?"+ calendar.getTimeInMillis();
    }

    public void setUrlMidia(String urlMidia) {
        this.urlMidia = urlMidia;
    }

    public TipoWodEnum getTipoWod() {
        return tipoWod;
    }

    public void setTipoWod(TipoWodEnum tipoWod) {
        this.tipoWod = tipoWod;
    }

    public TipoWod getTipoWodTabela() {
        return tipoWodTabela;
    }

    public void setTipoWodTabela(TipoWod tipoWodTabela) {
        this.tipoWodTabela = tipoWodTabela;
    }

    public Boolean getPreDefinido() {
        return preDefinido;
    }

    public void setPreDefinido(Boolean preDefinido) {
        this.preDefinido = preDefinido;
    }
}
