package br.com.pacto.bean.benchmark;

import java.io.Serializable;

/**
 * Created by <PERSON> on 11/07/2016.
 */
public enum OrigemTipoBenchmark {
    SISTEMA("Sistema","SISTEMA"),
    BOX("Box","BOX");

    private String descricao;
    private String codigo;

    OrigemTipoBenchmark(String descricao, String codigo) {
        this.descricao = descricao;
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getCodigo() {
        return codigo;
    }
}
