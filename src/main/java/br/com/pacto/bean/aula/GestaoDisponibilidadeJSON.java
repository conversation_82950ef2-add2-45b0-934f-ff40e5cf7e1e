/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class GestaoDisponibilidadeJSON  extends SuperJSON {
    
    private String periodo;
    private Integer ocupadas;
    private Integer sobraram;
    private Integer pedidos;
    private Integer experimentais;
    private Integer confirmados;

    public GestaoDisponibilidadeJSON(String periodo) {
        this.periodo = periodo;
        ocupadas = 0;
        sobraram = 0;
        pedidos = 0;
        experimentais = 0;
    }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public Integer getOcupadas() {
        if (ocupadas == null) {
            ocupadas = 0;
        }
        return ocupadas;
    }

    public void setOcupadas(Integer ocupadas) {
        this.ocupadas = ocupadas;
    }

    public Integer getSobraram() {
        if (sobraram == null) {
            sobraram = 0;
        }
        return sobraram;
    }

    public void setSobraram(Integer sobraram) {
        this.sobraram = sobraram;
    }

    public Integer getPedidos() {
        return pedidos;
    }

    public void setPedidos(Integer pedidos) {
        this.pedidos = pedidos;
    }

    public Integer getExperimentais() {
        return experimentais;
    }

    public void setExperimentais(Integer experimentais) {
        this.experimentais = experimentais;
    }


    public Integer getConfirmados() {
        if (confirmados == null) {
            confirmados = 0;
        }
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }
}
