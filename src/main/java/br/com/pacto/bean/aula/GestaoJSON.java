/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class GestaoJSON extends SuperJSON {
    
    private Integer x = 0;
    private Integer y = 0;
    private Integer valor = 0;
    private String cor = "#FFFFFF";
    private String modalidade = "";
    private String professor = "";
    private String label = "";
    
    public GestaoJSON(){
    }
    private List<GestaoSubJSON> subs = new ArrayList<GestaoSubJSON>();
    
    public void montarOcupacao(GestaoSalaCheiaAnaliticoTO gestao){
        x = gestao.getHora();
        y = gestao.getCapacidade();
        valor = gestao.getOcupacao();
        cor = gestao.getModalidade().getCor().getCor();
        modalidade = gestao.getModalidade().getNome();
        label = gestao.getNome();
    }
    
    public void montarTaxaProfessores(String professor, Integer valor, String cor){
        this.valor = valor;
        this.professor = professor;
        this.cor = cor;
    }
    
    public void montarMetaOcupacao(String label, Integer valor, String cor, List<AulaDia> aulas){
        this.label = label;
        this.valor = valor;
        this.cor = cor;
        Map<String, GestaoSubJSON> mapa = new HashMap<String, GestaoSubJSON>();
        for(AulaDia aula : aulas){
            GestaoSubJSON subGestao = mapa.get(aula.getProfessor().getPrimeiroNome());
            mapa.put(aula.getProfessor().getPrimeiroNome(), 
                    subGestao == null ? new GestaoSubJSON(aula.getProfessor().getPrimeiroNome(), 1)
                    : new GestaoSubJSON(aula.getProfessor().getPrimeiroNome(), subGestao.getValor()+1));
        }
        subs = new ArrayList<GestaoSubJSON>(mapa.values());
    }

    public Integer getX() {
        return x;
    }

    public void setX(Integer x) {
        this.x = x;
    }

    public Integer getY() {
        return y;
    }

    public void setY(Integer y) {
        this.y = y;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<GestaoSubJSON> getSubs() {
        return subs;
    }

    public void setSubs(List<GestaoSubJSON> sub) {
        this.subs = sub;
    }
    
    
    
}
