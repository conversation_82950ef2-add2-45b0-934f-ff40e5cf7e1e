/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaDiaExcecao implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private AulaDia aula;
    @ManyToOne
    private ClienteSintetico cliente;
    private Boolean alunoRemovido = Boolean.FALSE;
    private Integer usuario_codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro;
    private String idHorarioTurmaReference;

    public Boolean getAlunoRemovido() {
        if(alunoRemovido == null){
            alunoRemovido = Boolean.FALSE;
        }
        return alunoRemovido;
    }

    public void setAlunoRemovido(Boolean alunoRemovido) {
        this.alunoRemovido = alunoRemovido;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario) {
        this.usuario_codigo = usuario;
    }
    
    
    public AulaDiaExcecao() {
    }

    public AulaDiaExcecao(Date dataRegistro, String idHorarioTurmaReference, ClienteSintetico cliente) {
        this.idHorarioTurmaReference = idHorarioTurmaReference;
        this.cliente = cliente;
        this.dataRegistro = dataRegistro;
    }

    public AulaDiaExcecao(Date dataRegistro, AulaDia aula, ClienteSintetico cliente) {
        this.aula = aula;
        this.cliente = cliente;
        this.dataRegistro = dataRegistro;
    }
    
    public AulaDiaExcecao(Date dataRegistro, AulaDia aula, ClienteSintetico cliente, Usuario usuario) {
        this.aula = aula;
        this.cliente = cliente;
        this.usuario_codigo = usuario.getCodigo();
        alunoRemovido = Boolean.TRUE;
        this.dataRegistro = dataRegistro;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AulaDia getAula() {
        return aula;
    }

    public void setAula(AulaDia aula) {
        this.aula = aula;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getIdHorarioTurmaReference() {
        return idHorarioTurmaReference;
    }

    public void setIdHorarioTurmaReference(String idHorarioTurmaReference) {
        this.idHorarioTurmaReference = idHorarioTurmaReference;
    }
}
