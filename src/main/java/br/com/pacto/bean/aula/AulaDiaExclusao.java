/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.usuario.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaDiaExclusao implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Aula aula;
    private Integer usuario_codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataExclusao;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataAulaDia;
    private String justificativa;
    private Integer codigoHorarioTurma;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Aula getAula() {
        return aula;
    }

    public void setAula(Aula aula) {
        this.aula = aula;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario) {
        this.usuario_codigo = usuario;
    }

    public Date getDataExclusao() {
        return dataExclusao;
    }

    public void setDataExclusao(Date dataExclusao) {
        this.dataExclusao = dataExclusao;
    }

    public Date getDataAulaDia() {
        return dataAulaDia;
    }

    public void setDataAulaDia(Date dataAulaDia) {
        this.dataAulaDia = dataAulaDia;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }
    
    
}
