/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaAluno implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @ManyToOne
    private ProfessorSintetico professor;
    @ManyToOne
    private AulaHorario horario;
    @Temporal(TemporalType.DATE)
    private Date dia;
    private Boolean aulaExperimental;
    private Boolean presencaConfirmada;
    private String modalidade;
    @Transient
    private AulaDia aula;
//alter table aulaaluno drop column aula_codigo
    public AulaAluno() {
    }


    public AulaAluno(ClienteSintetico cliente) {
        this.cliente = cliente;
    }
    
    public Integer getCodigoCliente(){
        return cliente == null ? null : cliente.getCodigo();
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }


    public String getPrimeiroNome() {
        if (cliente != null && cliente.getNome() != null) {
            return Uteis.getPrimeiroNome(cliente.getNome());
        }
        return "";

    }
    
    public String getPrimeiroNomeComLetraSobrenome() {
        if (cliente != null && cliente.getNome() != null) {
            return Uteis.obterPrimeiroNomeConcatenadoSobreNome(cliente.getNome(), true);
        }
        return "";

    }

    public Boolean getAulaExperimental() {
        if(aulaExperimental == null){
            aulaExperimental = Boolean.FALSE;
        }
        return aulaExperimental;
    }

    public void setAulaExperimental(Boolean aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public AulaHorario getHorario() {
        return horario;
    }

    public void setHorario(AulaHorario horario) {
        this.horario = horario;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Boolean getPresencaConfirmada() {
        return presencaConfirmada;
    }

    public void setPresencaConfirmada(Boolean presencaConfirmada) {
        this.presencaConfirmada = presencaConfirmada;
    }

    public Date getDia() {
        return dia;
    }

    public AulaDia getAula() {
        return aula;
    }

    public void setAula(AulaDia aula) {
        this.aula = aula;
    }

    public String getModalidade() { return modalidade; }

    public void setModalidade(String modalidade) { this.modalidade = modalidade; }
}
