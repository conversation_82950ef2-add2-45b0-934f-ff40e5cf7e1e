/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.aulaDia.AulaDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import java.io.Serializable;
import java.util.*;
import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Aula implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Modalidade modalidade;
    @ManyToOne
    private Ambiente ambiente;
    @ManyToOne
    private ProfessorSintetico professor;
    private FrequenciaEnum frequencia;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataFim;
    private Integer capacidade;
    private Integer meta;
    private Integer minutosTolerancia;
    private Integer bonificacao;
    private Integer pontosBonus;
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "aula", targetEntity = AulaHorario.class)
    private List<AulaHorario> horarios = new ArrayList<AulaHorario>();
    private String diasSemana;
    private String mensagem;
    private String urlVideoYoutube;
    private String nome;
    @Transient
    private Map<String, String> alunosConfirmados;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private String niveis;
    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    
    public Aula(){
        frequencia = FrequenciaEnum.ALTA_FREQUENCIA;
        dataInicio = Calendario.hoje();
}

    public Aula(AulaDTO a){
        this.modalidade = new Modalidade(Integer.valueOf(a.getModalidadeId()));
        this.ambiente = new Ambiente(Integer.valueOf(a.getAmbienteId()));
        this.professor = new ProfessorSintetico(Integer.valueOf(a.getProfessorId()));
        this.frequencia = a.getOcupacao() == null ? FrequenciaEnum.ALTA_FREQUENCIA : FrequenciaEnum.valueOf(a.getOcupacao());
        String capacidade = a.getCapacidade().replace(".", "");
        capacidade = capacidade.replace(",", "");
        this.capacidade = Integer.valueOf(capacidade);
        if(a.getMeta() != null){
            this.meta = Integer.valueOf(a.getMeta().replace("%", ""));
        }
        if(!UteisValidacao.emptyString(a.getToleranciaMin())){
            this.minutosTolerancia = Integer.valueOf(a.getToleranciaMin().replace(".", "").replace(",", ""));
        }
        if(a.getBonificacao() != null){
            this.bonificacao = Integer.valueOf(a.getBonificacao().replace("R$", ""));
        }
        if(a.getPontuacaoBonus() != null){
            this.pontosBonus = Integer.valueOf(a.getPontuacaoBonus());
        }
        this.diasSemana = "";
        this.diasSemana = this.diasSemana.replaceFirst(",", "");
        this.diasSemana = "";
        this.mensagem = a.getMensagem();
        this.urlVideoYoutube = a.getUrlVideoYoutube();
        this.nome = a.getNome();
        this.setDataInicio(new Date(a.getDataInicio()));
        this.setDataFim(new Date(a.getDataFinal()));
        this.produtoGymPass = a.getProdutoGymPass();
        this.urlTurmaVirtual = a.getUrlTurmaVirtual();
        this.idClasseGymPass = a.getIdClasseGymPass();

        this.idadeMaximaAnos = a.getIdadeMaximaAnos();
        this.idadeMaximaMeses = a.getIdadeMaximaMeses();
        this.idadeMinimaAnos = a.getIdadeMinimaAnos();
        this.idadeMinimaMeses = a.getIdadeMinimaMeses();
        this.niveis = "";
        if(a.getNiveis() != null){
            for (NivelTO nivel : a.getNiveis()) {
                this.niveis += "," + nivel.getId();
            }
            this.niveis = this.niveis.replaceFirst(",", "");
        }
    }

    public Aula(AgendaTotalTO agendamento) {
        this.modalidade = new Modalidade(agendamento.getCodigotipo(), agendamento.getTipo());
        this.ambiente = new Ambiente(agendamento.getCodigoLocal(), agendamento.getLocal(), null);
        this.professor = new ProfessorSintetico( agendamento.getResponsavel(), agendamento.getCodigoResponsavel());
        this.frequencia = agendamento.getOcupacao() == null ? FrequenciaEnum.ALTA_FREQUENCIA :
                FrequenciaEnum.getFromOrdinal(agendamento.getOcupacao());
        this.capacidade = agendamento.getNrVagas();
        this.meta = agendamento.getMeta() == null ? 0 : agendamento.getMeta().intValue();
        this.minutosTolerancia = agendamento.getTolerancia();
        this.bonificacao = agendamento.getBonificacao() == null ? 0 : agendamento.getBonificacao().intValue();
        this.pontosBonus = agendamento.getPontosBonus();
//        this.diasSemana = diasSemana;
        this.mensagem = agendamento.getMensagem();
        this.nome = agendamento.getTitulo();
        this.alunosConfirmados = agendamento.getAlunosConfirmados();
    }
    
    
    

    public Aula clone(){
        Aula clone = new Aula();
        clone.setModalidade(this.modalidade);
        clone.setAmbiente(this.ambiente);
        clone.setProfessor(this.professor);
        clone.setFrequencia(this.frequencia);
        clone.setDataInicio(this.dataInicio);
        clone.setCapacidade(this.capacidade);
        clone.setMeta(this.meta);
        clone.setBonificacao(this.bonificacao);
        clone.setPontosBonus(this.pontosBonus);
        clone.setDiasSemana(this.diasSemana);
        clone.setMensagem(this.mensagem);
        clone.setIdClasseGymPass(this.idClasseGymPass);
        clone.setProdutoGymPass(this.produtoGymPass);
        clone.setUrlTurmaVirtual(this.urlTurmaVirtual);
        clone.setUrlVideoYoutube(this.urlVideoYoutube);
        return clone;
    }
    
    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Modalidade getModalidade() {
        if(modalidade == null){
            modalidade = new Modalidade();
        }
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    public Ambiente getAmbiente() {
        if(ambiente == null){
            ambiente = new Ambiente();
        }
        return ambiente;
    }

    public void setAmbiente(Ambiente ambiente) {
        this.ambiente = ambiente;
    }

    public ProfessorSintetico getProfessor() {
        if(professor == null){
            professor = new ProfessorSintetico();
        }
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public FrequenciaEnum getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(FrequenciaEnum frequencia) {
        this.frequencia = frequencia;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getMeta() {
        return meta;
    }

    public void setMeta(Integer meta) {
        this.meta = meta;
    }

    public Integer getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Integer bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public List<AulaHorario> getHorarios() {
        if(horarios == null){
            horarios = new ArrayList<AulaHorario>();
        }
        return horarios;
    }

    public void setHorarios(List<AulaHorario> horarios) {
        this.horarios = horarios;
    }

    public void setarDias(List<String> diasSelecionados) {
        diasSemana = "";
        for (String str : diasSelecionados) {
            diasSemana += ","+str;
        }
        diasSemana = diasSemana.replaceFirst(",", "");
    }
    
    public List<String> getDiasSelecionados(){
        List<String> lista = new ArrayList<String>();
        if(diasSemana == null || diasSemana.isEmpty()){
            return lista;
        }
        String[] split = diasSemana.split(",");
        lista.addAll(Arrays.asList(split));
        return lista;
    }
    
    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getNome() {
        if((nome == null || nome.isEmpty()) && getModalidade() != null){
            nome = getModalidade().getNome();
        }
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getNomeApresentar(){
        try {
            String[] words = getNome().trim().split( " " );  
            String out = "";  
            for ( int i = 0; i < words.length; i++ ) {  
                if(words[i].length() > 2){
                    out += StringUtils.capitalize(words[i].toLowerCase()) + " ";      
                }  
            }    
            return out;
        } catch (Exception e) {
            return "";
        }
          
    }

    public Integer getMinutosTolerancia() {
        if(minutosTolerancia == null)
            return 0;
        return minutosTolerancia;
    }

    public void setMinutosTolerancia(Integer minutosTolerancia) {
        this.minutosTolerancia = minutosTolerancia;
    }

    public Map<String, String> getAlunosConfirmados() {
        if (alunosConfirmados == null) {
            alunosConfirmados = new HashMap<String, String>();
        }
        return alunosConfirmados;
    }

    public void setAlunosConfirmados(Map<String, String> alunosConfirmados) {
        this.alunosConfirmados = alunosConfirmados;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }
}
