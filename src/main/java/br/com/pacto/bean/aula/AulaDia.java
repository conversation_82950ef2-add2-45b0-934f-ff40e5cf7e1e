/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.bean.AgendaTotalTO;
import java.io.Serializable;
import java.util.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaDia  implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    @ManyToOne
    private Aula aula;
    @ManyToOne
    private AulaHorario aulaHorario = null;
    @Transient
    private List<AulaAluno> alunos = new ArrayList<AulaAluno>();
    private String justificativa;
    @ManyToOne
    private ProfessorSintetico professor;
    @ManyToOne
    private ProfessorSintetico professorSubstituido;
    private Boolean substituido = Boolean.FALSE;
    @Transient
    private List<String> horarios = new ArrayList<String>();
    @Transient
    private boolean aulaColetiva = true;
    @Transient
    private Integer nrVagasOcupadas;
    @Transient
    private Integer confirmados;
    @Transient
    private Map<String, String> alunosConfirmados;

    public AulaDia() {
    }

    public AulaDia(AgendaTotalTO agendamento) {
        this.inicio = agendamento.getStartDate();
        this.fim = agendamento.getEndDate();
        this.aula = new Aula(agendamento);
        if(agendamento.getSubstituido() != null){
            this.justificativa = agendamento.getSubstituido().getJustificativa();
            this.professorSubstituido = new ProfessorSintetico(agendamento.getSubstituido().getNomeProfessorOrigem(), 
                    agendamento.getSubstituido().getCodigoProfessorOriginal());
        }
        this.professor = aula.getProfessor();
        this.aulaColetiva = agendamento.getAulaColetiva();
        this.nrVagasOcupadas = agendamento.getNrVagasPreenchidas();
        this.confirmados = agendamento.getConfirmacoes();
        this.alunosConfirmados = agendamento.getAlunosConfirmados();
    }

    public boolean isAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }
    
    
    
    public String getNome(){
        return aula == null ? "" : aula.getNome();
    }
    
    public boolean getAulaCheia(){
        return getAula().getCapacidade() <= getAlunos().size();
    }
    
    public String getTitle(boolean mes){
        try {
            return aula == null ? "" : 
                mes ? Uteis.gethoraHHMMAjustado(inicio) + "-" + Uteis.gethoraHHMMAjustado(fim) :
                Integer.valueOf(getAula().getCapacidade()-getAlunos().size()).toString();
        } catch (Exception e) {
            return "";
        }
        
    } 
    
    public String getDia(){
        try {
            return Uteis.getData(inicio);
        } catch (Exception e) {
            return "";
        }
        
    } 

    public List<AulaAluno> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AulaAluno> alunos) {
        this.alunos = alunos;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Aula getAula() {
        return aula;
    }

    public void setAula(Aula aula) {
        this.aula = aula;
    }

    public List<String> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<String> horarios) {
        this.horarios = horarios;
    }

    public AulaHorario getAulaHorario() {
        return aulaHorario;
    }

    public void setAulaHorario(AulaHorario aulaHorario) {
        this.aulaHorario = aulaHorario;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public ProfessorSintetico getProfessor() {
        if(professor == null && getAula() != null){
            professor = getAula().getProfessor();
        }
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public ProfessorSintetico getProfessorSubstituido() {
        return professorSubstituido;
    }

    public void setProfessorSubstituido(ProfessorSintetico professorSubstituido) {
        this.professorSubstituido = professorSubstituido;
    }

    public Boolean getSubstituido() {
        if(substituido == null){
            substituido = Boolean.FALSE;
        }
        return substituido;
    }

    public void setSubstituido(Boolean substituido) {
        this.substituido = substituido;
    }

    public Integer getNrVagasOcupadas() {
        return nrVagasOcupadas;
    }

    public void setNrVagasOcupadas(Integer nrVagasOcupadas) {
        this.nrVagasOcupadas = nrVagasOcupadas;
    }

    public Integer getConfirmados() {
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }

    public Map<String, String> getAlunosConfirmados() {
        if (alunosConfirmados == null) {
            alunosConfirmados = new HashMap<String, String>();
        }
        return alunosConfirmados;
    }

    public void setAlunosConfirmados(Map<String, String> alunosConfirmados) {
        this.alunosConfirmados = alunosConfirmados;
    }
}
