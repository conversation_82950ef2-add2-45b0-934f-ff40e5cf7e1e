/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.util.enumeradores.PaletaCoresEnum;

/**
 *
 * <AUTHOR>
 */
public enum IndicadorGestaoSalaCheiaEnum {
    PEDIDOS_NEGADOS(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.AZUL_G),
    PEDIDOS_NEGADOS_MULTIPLOS(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.AMARELO_D),
    AULA_EXPERIMENTAL(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.AMARELO_D),
    AULAS_VERDES(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.VERDE_H),
    AULAS_VERMELHAS(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.VERMELHO_G),
    AULAS_AMARELAS(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.AMARELO_A),
    RANKING_PROFESSORES_VERDE(GestaoSalaCheiaEnum.RANKING_VERDE, PaletaCoresEnum.VERDE_H),
    RANKING_PROFESSORES_VERMELHO(GestaoSalaCheiaEnum.RANKING_VERMELHO, PaletaCoresEnum.VERMELHO_G),
    OCUPACAO(GestaoSalaCheiaEnum.OCUPACAO, PaletaCoresEnum.VERMELHO_G),
    SOBRESSALENCIA(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.VERMELHO_E),
    AULASMARCADAS(GestaoSalaCheiaEnum.EXCECAO, PaletaCoresEnum.VERMELHO_E),
    DEMANDA(GestaoSalaCheiaEnum.DEMANDA, PaletaCoresEnum.AZUL_G);
    
    private GestaoSalaCheiaEnum BI;
    private PaletaCoresEnum cor;
    
    IndicadorGestaoSalaCheiaEnum(GestaoSalaCheiaEnum bi, PaletaCoresEnum cor){
        BI = bi;
        this.cor = cor;
    }

    public GestaoSalaCheiaEnum getBI() {
        return BI;
    }

    public void setBI(GestaoSalaCheiaEnum BI) {
        this.BI = BI;
    }

    public PaletaCoresEnum getCor() {
        return cor;
    }
    
}
