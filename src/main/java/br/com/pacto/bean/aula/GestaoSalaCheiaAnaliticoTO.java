/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class GestaoSalaCheiaAnaliticoTO implements Serializable{
    private Modalidade modalidade;
    private AulaDia aula;
    private AulaAluno aluno;
    private AulaDiaExcecao excecao;
    private ClienteSintetico cliente;
    private Boolean verde;
    private Boolean vermelha;
    private Date dataProcura;
    private String ambiente;
    private String turma;
    private String horarioTurma;
    private Integer capacidade;
    private Integer ocupacao;
    private Integer hora;
    private Double bonificacao;
    private String nome;
    private Integer pontos;
    private Integer confirmados;

    public GestaoSalaCheiaAnaliticoTO() {
    }

    public GestaoSalaCheiaAnaliticoTO(Aula<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>, AulaDiaExcecao excecao, ClienteSintetico cliente, 
            Modalidade modalidade, Boolean verde, Boolean vermelha, Double bonificao) {
        this.aula = aula;
        this.aluno = aluno;
        this.excecao = excecao;
        this.cliente = cliente;
        this.modalidade = modalidade;
        this.verde = verde;
        this.vermelha = vermelha;
        this.bonificacao = bonificao;
    }
    
    public GestaoSalaCheiaAnaliticoTO(String nome, Modalidade modalidade, Integer capacidade, Integer ocupacao, Integer hora) {
        this.modalidade = modalidade;
        this.capacidade = capacidade;
        this.ocupacao = ocupacao;
        this.hora = hora;
        this.nome = nome;
    }
    
    public GestaoSalaCheiaAnaliticoTO(AulaDia aula) {
        this.aula = aula;
    }

    public Modalidade getModalidade() {
        return modalidade;
    }

    public void setModalidade(Modalidade modalidade) {
        this.modalidade = modalidade;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }
    
    
    public AulaDia getAula() {
        return aula;
    }

    public void setAula(AulaDia aula) {
        this.aula = aula;
    }

    public AulaAluno getAluno() {
        return aluno;
    }

    public AulaDiaExcecao getExcecao() {
        return excecao;
    }

    public void setExcecao(AulaDiaExcecao excecao) {
        this.excecao = excecao;
    }

    public void setAluno(AulaAluno aluno) {
        this.aluno = aluno;
    }
    
    public String getMatricula(){
        try{
            return getCliente() == null ? "" : getCliente().getMatriculaString();
        }catch(Exception e){
            return "";
        }
        
    }
    
    public String getNomeCliente(){
        return getCliente() == null ? "" : getCliente().getNomeAbreviado();
    }
    
    public Date getData(){
        return getAula() == null ? null : getAula().getInicio();
    }
    
    public String getDataAula(){
        return getAula() == null ? "" : Uteis.getData(getAula().getInicio());
    }
    public String getHoraAula(){
        return getAula() == null ? "" : Uteis.getDataAplicandoFormatacao(getAula().getInicio(),"HH:mm");
    }
    
    public String getTipo(){
        return excecao != null ? "Pedido negado" : aluno == null || !aluno.getAulaExperimental() ? "" : "Experimental";
    }
    
    public String getNomeModalidade(){
        return modalidade == null ? aula == null ? "" : aula.getAula().getModalidade().getNome() : modalidade.getNome();
    }
    
    public String getNomeAula(){
        return aula == null ? modalidade == null ? "" : modalidade.getNome() : aula.getNome();
    }
    
    public String getProfessor(){
        return aula == null || aula.getProfessor() == null ? "" : aula.getProfessor().getNomeAbreviado();
    }
    
    public String getJustificativa(){
        return aula == null ? "" : aula.getJustificativa();
    }
    
    public String getProfessorSub(){
        return aula == null ? "" : aula.getProfessorSubstituido().getNomeAbreviado();
    }

    public Boolean getVerde() {
        return verde;
    }

    public void setVerde(Boolean verde) {
        this.verde = verde;
    }

    public Boolean getVermelha() {
        return vermelha;
    }

    public void setVermelha(Boolean vermelha) {
        this.vermelha = vermelha;
    }
    
    public Integer getCapacidade(){
        return aula == null ? capacidade : aula.getAula().getCapacidade();
    }
    
    public Integer getOcupacao(){
        return aula == null || aula.getNrVagasOcupadas() == null ? ocupacao : aula.getNrVagasOcupadas();
    }
    
    public String getCor(){
        return verde ? IndicadorGestaoSalaCheiaEnum.AULAS_VERDES.getCor().getCor() 
                : vermelha ? IndicadorGestaoSalaCheiaEnum.AULAS_VERMELHAS.getCor().getCor() :  IndicadorGestaoSalaCheiaEnum.AULAS_AMARELAS.getCor().getCor();
    }

    public Integer getHora() {
        return hora;
    }
    
    public String getHoraApresentar() {
        return hora == null ? "" : hora > 9 ? hora.toString() : "0"+hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }

    public Double getBonificacao() {
        return verde ? bonificacao : 0.0;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }
    
    public String getBonificacaoApresentar(){
        return bonificacao == null ? "" : Uteis.arrendondarForcando2CadasDecimaisComVirgula(bonificacao);
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public Date getDataProcura() {
        return dataProcura;
    }

    public void setDataProcura(Date dataProcura) {
        this.dataProcura = dataProcura;
    }

    public String getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(String horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public Integer getConfirmados() {
        return aula == null || aula.getConfirmados() == null ? confirmados : aula.getConfirmados();
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }
}
