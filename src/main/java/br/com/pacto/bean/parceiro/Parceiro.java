package br.com.pacto.bean.parceiro;


import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@Entity
@Table
public class Parceiro implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private String urlImagem;
    private Boolean situacao;

    public Parceiro() {}

    public Parceiro(String nome, String urlImagem, Boolean situacao) {
        this.nome = nome;
        this.urlImagem = urlImagem;
        this.situacao = situacao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }
}
