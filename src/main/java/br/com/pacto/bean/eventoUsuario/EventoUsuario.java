package br.com.pacto.bean.eventoUsuario;


import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR> 19/02/2019
 */
@Entity
public class EventoUsuario implements Serializable {

    @Id
    private String codigo;
    private String evento;

    public EventoUsuario() {
    }

    public EventoUsuario(String codigo, String evento) {
        this.codigo = codigo;
        this.evento = evento;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EventoUsuario that = (EventoUsuario) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }
}
