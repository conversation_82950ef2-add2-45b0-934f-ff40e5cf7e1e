/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.sincronizacao;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.dao.intf.atividade.AtividadeAparelhoDao;
import br.com.pacto.dao.intf.atividade.AtividadeCategoriaAtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.ficha.CategoriaFichaDao;
import br.com.pacto.dao.intf.ficha.FichaDao;
import br.com.pacto.dao.intf.programa.ObjetivoPredefinidoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;

/**
 *
 * <AUTHOR>
 */
public enum TipoClassSincronizarEnum {

    Atividade(0, Atividade.class, "Atividade", AtividadeDao.class, null),
    AtividadeCategoriaAtividade(1, AtividadeCategoriaAtividade.class, "Atividade", AtividadeCategoriaAtividadeDao.class, null),
    AtividadeAparelho(2, AtividadeAparelho.class, "Atividade", AtividadeAparelhoDao.class, null),
    Ficha(3, Ficha.class, "Ficha", FichaDao.class, "usarComoPredefinida"),
    AtividadeFicha(4, AtividadeFicha.class, "Ficha", AtividadeFichaDao.class, null),
    ObjetivoPredefinido(5, ObjetivoPredefinido.class, "ObjetivoPredefinido", ObjetivoPredefinidoDao.class, null),
    CategoriaFicha(6, CategoriaFicha.class, "CategoriaFicha", CategoriaFichaDao.class, null),
    ProgramaTreino(7, ProgramaTreino.class, "ProgramaTreino", ProgramaTreinoDao.class, null);
    private Integer id;
    private Class classEntity;
    private String nameParent;
    private Class classOfService;
    private String attrFlag;

    private TipoClassSincronizarEnum(Integer id, Class classEntity,
            final String nameParent, Class classOfService,
            final String attrFlag) {
        this.id = id;
        this.classEntity = classEntity;
        this.nameParent = nameParent;
        this.classOfService = classOfService;
        this.attrFlag = attrFlag;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Class getClassEntity() {
        return classEntity;
    }

    public void setClassEntity(Class classEntity) {
        this.classEntity = classEntity;
    }

    public Class getClassOfService() {
        return classOfService;
    }

    public void setClassOfService(Class classOfService) {
        this.classOfService = classOfService;
    }

    public String getAttrFlag() {
        return attrFlag;
    }

    public void setAttrFlag(String attrFlag) {
        this.attrFlag = attrFlag;
    }

    public TipoClassSincronizarEnum getParent() {
        return TipoClassSincronizarEnum.valueOf(nameParent);
    }
}
