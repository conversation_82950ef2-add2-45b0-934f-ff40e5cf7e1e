/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.sincronizacao;

/**
 *
 * <AUTHOR>
 */
public enum TipoRevisaoEnum {

    INSERT(0, "Insert", "Adicionado"),
    UPDATE(1, "Update", "Alterado"),
    DELETE(2, "Delete", "Removido");
    private Integer id;
    private String descricao;
    private String descricaoLog;

    private TipoRevisaoEnum(Integer id, String descricao, String descricaoLog) {
        this.id = id;
        this.descricao = descricao;
        this.descricaoLog = descricaoLog;
    }

    public static TipoRevisaoEnum getFromId(Integer id){
        for(TipoRevisaoEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }

    public static TipoRevisaoEnum getFromAdm(String op){
        switch (op){
            case "ALTERAÇÃO":
                return UPDATE;
            case "EXCLUSÃO":
                return DELETE;
            case "INCLUSÃO":
                return INSERT;
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoLog() {
        return descricaoLog;
    }
}
