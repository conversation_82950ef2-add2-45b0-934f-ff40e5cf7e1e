/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.sincronizacao;

import br.com.pacto.objeto.Calendario;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ObjetoSincronizarTO implements Serializable {

    private TipoObjetoSincronizarEnum tipo;
    private String chave;
    private Date data;
    private Integer empresa;

    public ObjetoSincronizarTO(TipoObjetoSincronizarEnum tipo, String chave, Integer empresa) {
        this.tipo = tipo;
        this.chave = chave;
        this.data = Calendario.hoje();
        this.empresa = empresa;
    }

    public TipoObjetoSincronizarEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoObjetoSincronizarEnum tipo) {
        this.tipo = tipo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}
