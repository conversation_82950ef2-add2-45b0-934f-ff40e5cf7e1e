/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

/**
 *
 * <AUTHOR>
 */
public enum OrigemExecucaoEnum {

    FICHA_IMPRESSA(0, "Ficha Impressa"),
    ACOM<PERSON>_PROFESSOR(1, "Acomp. Professor"),
    SMARTPHONE(2, "Smartphone"),
    FICHA_CONCLUIDA(3, "Ficha concluída pelo RetiraFicha");
    private Integer id;
    private String nome;

    private OrigemExecucaoEnum(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public static OrigemExecucaoEnum getFromId(Integer i){
        for(OrigemExecucaoEnum o : values()){
            if(o.getId().equals(i)){
                return o;
            }
        }
        return  null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
