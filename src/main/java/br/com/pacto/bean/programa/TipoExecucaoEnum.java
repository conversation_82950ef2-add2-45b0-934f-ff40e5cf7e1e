/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.objeto.ColecaoUtils;
import java.util.Arrays;
import org.apache.commons.collections.Predicate;

/**
 *
 * <AUTHOR>
 */
public enum TipoExecucaoEnum {

    ALTERNADO(0, "Alternado", "ALT"),
    DIAS_SEMANA(1, "Dias da Semana", "DIA");
    private Integer id;
    private String idString;
    private String descricao;

    private TipoExecucaoEnum(Integer id, String descricao, String idStr) {
        this.id = id;
        this.idString = idStr;
        this.descricao = descricao;
    }

    public String getIdString() {
        return idString;
    }

    public void setIdString(String id_String) {
        this.idString = id_String;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoExecucaoEnum valueOf(final Integer id) {        
        return (TipoExecucaoEnum) ColecaoUtils.find(Arrays.asList(TipoExecucaoEnum.values()), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return ((TipoExecucaoEnum) o).getId().equals(id);
            }
        });
    }
}
