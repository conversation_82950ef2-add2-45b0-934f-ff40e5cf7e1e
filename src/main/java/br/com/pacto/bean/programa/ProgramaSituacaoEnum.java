/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

/**
 *
 * <AUTHOR>
 */
public enum ProgramaSituacaoEnum {

    ATIVO(0, "Ativo"),
    INATIVO(1, "Inativo");

    private ProgramaSituacaoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }
    private Integer id;
    private String descricao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static ProgramaSituacaoEnum valueOf(final Integer id) {
        ProgramaSituacaoEnum[] arr = ProgramaSituacaoEnum.values();
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].getId().equals(id)) {
                return arr[i];
            }
        }
        return null;
    }
}
