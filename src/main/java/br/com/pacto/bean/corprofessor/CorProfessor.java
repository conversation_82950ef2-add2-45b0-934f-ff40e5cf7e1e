package br.com.pacto.bean.corprofessor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by <PERSON> on 18/09/2015.
 */
@Entity
@Table
public class CorProfessor implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private PaletaCoresEnum cor;
    @OneToOne
    private ProfessorSintetico professor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PaletaCoresEnum getCor() {
        return cor;
    }

    public void setCor(PaletaCoresEnum cor) {
        this.cor = cor;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(Professor<PERSON><PERSON><PERSON><PERSON> professor) {
        this.professor = professor;
    }
}
