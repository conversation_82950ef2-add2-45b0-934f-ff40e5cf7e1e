package br.com.pacto.bean.usuario;

import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UtilContext;

/**
 * paulo 19/10/2018
 */
public class UsuarioBuilder {

    public static Usuario usuarioColaboradorToUsuario(String ctx, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId) throws Exception {
        Usuario usuario = new Usuario();
        ProfessorSintetico professorSintetico = ((ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class))
                .obterPorId(ctx, usuarioColaboradorTO.getColaboradorId());
        usuario.setProfessor(professorSintetico);
        usuario.setEmpresaZW(empresaId);
        usuario.setCodigo(usuarioColaboradorTO.getId());
        usuario.setTipo(usuarioColaboradorTO.getTipoUsuario().obterTipoUsuario());
        usuario.setFotoKeyApp(professorSintetico.getUriImagem());

        /**
         * 27/12/2018 - Ainda não foi criado o crud de perfil do usuario, quando estiver criado o crud, é para descomentando essa parte
         */
        Perfil perfil = null;
        if(usuarioColaboradorTO.getPerfilUsuarioPermissoes() != null) {
            perfil = ((PerfilDao) UtilContext.getBean(PerfilDao.class)).findById(ctx, usuarioColaboradorTO.getPerfilUsuarioPermissoes());
        }
        usuario.setPerfil(perfil);

        if(usuarioColaboradorTO.getSituacao() != null) {
            usuario.setStatus(StatusEnum.setStatus(usuarioColaboradorTO.getSituacao().name()));
        }else{
            usuario.setStatus(StatusEnum.ATIVO);
        }

        usuario.setNome(professorSintetico.getPessoa().getNome());
        usuario.setUserName(usuarioColaboradorTO.getAppUserName().toUpperCase());
        usuario.setEmail(usuarioColaboradorTO.getEmail());
        usuario.setCelular(usuarioColaboradorTO.getCelular());

        return usuario;
    }
}
