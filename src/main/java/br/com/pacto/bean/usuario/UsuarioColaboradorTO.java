package br.com.pacto.bean.usuario;

import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * paulo 19/10/2018
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioColaboradorTO {

    private Integer id;
    private Integer colaboradorId;
    private String appUserName;
    private String appPassword;
    private TipoUsuarioColaboradorEnum tipoUsuario;
    private Integer perfilUsuarioPermissoes;
    private String imagemData;
    private String extensaoImagem;
    private SituacaoColaboradorEnum situacao;
    private String email;
    private String celular;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public Integer getPerfilUsuarioPermissoes() {
        return perfilUsuarioPermissoes;
    }

    public void setPerfilUsuarioPermissoes(Integer perfilUsuarioPermissoes) {
        this.perfilUsuarioPermissoes = perfilUsuarioPermissoes;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }
}
