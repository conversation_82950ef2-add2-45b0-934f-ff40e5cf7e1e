/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.usuario;

import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;

/**
 *
 * <AUTHOR>
 */
public enum TipoUsuarioEnum {

    ALUNO(0, "<PERSON><PERSON>"),
    PROFESSOR(1, "Professor"),
    COORDENADOR(2, "<PERSON>ordenador"),
    ROOT(3, "Root"),
    CONSULTOR(4, "Consultor"),
    CONVIDADO(5, "Convidado");
    private int id;
    private String descricao;

    private TipoUsuarioEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }
    
    public static TipoUsuarioEnum getFromID(int id){
        for(TipoUsuarioEnum tipo : values()){
            if(tipo.getId() == id){
                return tipo;
            }
        }
        return null;
    }

    public TipoUsuarioColaboradorEnum obterTipoUsuarioColaborador() {
        switch (this) {
            case CONSULTOR: return TipoUsuarioColaboradorEnum.CONSULTOR;
            case COORDENADOR: return TipoUsuarioColaboradorEnum.COORDENADOR;
            default: return TipoUsuarioColaboradorEnum.PROFESSOR;
        }

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
