package br.com.pacto.bean.ficha;

import br.com.pacto.bean.serie.Serie;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Detalhes da série")
public class SerieResponseTO {

    @ApiModelProperty(value = "ID da série", example = "1")
    private Integer id;
    @ApiModelProperty(value = "ID da atividade ficha", example = "1")
    private Integer atividadeFichaId;
    @ApiModelProperty(value = "Sequencia", example = "1")
    private Integer sequencia;
    @ApiModelProperty(value = "Repetição", example = "1")
    private Integer repeticaoComp;
    @ApiModelProperty(value = "repetições")
    private String repeticoes;
    @ApiModelProperty(value = "Carga")
    private String carga;
    @ApiModelProperty(value = "Carga comp")
    private Double cargaComp;
    @ApiModelProperty(value = "Cadência")
    private String cadencia;
    @ApiModelProperty(value = "Descanso")
    private Integer descanso;
    @ApiModelProperty(value = "Complemento")
    private String complemento;
    @ApiModelProperty(value = "Velocidade")
    private Double velocidade;
    @ApiModelProperty(value = "Duração")
    private Integer duracao;
    @ApiModelProperty(value = "Distância")
    private Integer distancia;
    @ApiModelProperty(value = "Carga APP")
    private String cargaApp;
    @ApiModelProperty(value = "repetição APP")
    private String repeticaoApp;
    @ApiModelProperty(value = "Série realizada")
    private Boolean serieRealizada;

    public SerieResponseTO() {
    }

    public SerieResponseTO(Serie serie) {
        serie.obterCompValores();
        this.id = serie.getCodigo();
        this.atividadeFichaId = serie.getAtividadeFicha().getCodigo();
        this.sequencia = serie.getOrdem();
        this.repeticoes = serie.getRepeticaoComp();
        this.carga = serie.getCargaComp();
        this.cadencia = serie.getCadencia();
        this.descanso = serie.getDescanso();
        this.complemento = serie.getComplemento();
        this.velocidade = serie.getVelocidade();
        this.duracao = serie.getDuracao();
        this.distancia = serie.getDistancia();
        this.repeticaoComp = serie.getRepeticao();
        this.cargaComp = serie.getCarga();
        this.serieRealizada = serie.getSerieRealizada();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAtividadeFichaId() {
        return atividadeFichaId;
    }

    public void setAtividadeFichaId(Integer atividadeFichaId) {
        this.atividadeFichaId = atividadeFichaId;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public String getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public String getCarga() {
        return carga;
    }

    public void setCarga(String carga) {
        this.carga = carga;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }

    public Integer getRepeticaoComp() { return repeticaoComp; }

    public void setRepeticaoComp(Integer repeticaoComp) { this.repeticaoComp = repeticaoComp; }

    public Double getCargaComp() { return cargaComp; }

    public void setCargaComp(Double cargaComp) { this.cargaComp = cargaComp; }

    public Boolean getSerieRealizada() {
        if (serieRealizada == null){
            serieRealizada = false;
        }
        return serieRealizada;
    }

    public void setSerieRealizada(Boolean serieRealizada) { this.serieRealizada = serieRealizada; }
}
