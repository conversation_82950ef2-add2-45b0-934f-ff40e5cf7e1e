package br.com.pacto.bean.ficha;

import br.com.pacto.bean.programa.TipoExecucaoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 08/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FichaTO {

    private Integer id;
    private Integer programaId;
    private String nome;
    private Integer categoriaId;
    private TipoExecucaoEnum tipo_execucao;
    private List<String> dias_semana;
    private String mensagem;

    public Integer getProgramaId() {
        return programaId;
    }

    public Integer getId() { return id; }

    public void setId(Integer id) { this.id = id; }

    public void setProgramaId(Integer programaId) {
        this.programaId = programaId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCategoriaId() {
        return categoriaId;
    }

    public void setCategoriaId(Integer categoriaId) {
        this.categoriaId = categoriaId;
    }

    public TipoExecucaoEnum getTipo_execucao() {
        return tipo_execucao;
    }

    public void setTipo_execucao(TipoExecucaoEnum tipo_execucao) {
        this.tipo_execucao = tipo_execucao;
    }

    public List<String> getDias_semana() {
        return dias_semana;
    }

    public void setDias_semana(List<String> dias_semana) {
        this.dias_semana = dias_semana;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}
