package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.Ordenacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 08/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes da ficha")
public class FichaResponseTO {

    @ApiModelProperty(value = "ID da ficha", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Versão", example = "1")
    private Integer versao;
    @ApiModelProperty(value = "Nome da ficha", example = "Ficha 1")
    private String nome;
    @ApiModelProperty(value = "Categoria")
    private CategoriaFichaResponseTO categoria = null;
    @ApiModelProperty(value = "Tipo de execução", example = "DIA")
    private TipoExecucaoEnum tipo_execucao = TipoExecucaoEnum.ALTERNADO;
    @ApiModelProperty(value = "Mensagem")
    private String mensagem;
    @ApiModelProperty(value = "Atividades na ficha")
    private List<AtividadeFichaResponseTO> atividades = new ArrayList<AtividadeFichaResponseTO>();
    @ApiModelProperty(value = "Última execução")
    private Date ultimaExecucao;
    @ApiModelProperty(value = "Dias na semana")
    private List<String> dias_semana;
    @ApiModelProperty(value = "Ativo")
    private Boolean ativo;
    @ApiModelProperty(value = "Pré-definida")
    private Boolean predefinida;

    public FichaResponseTO() {
    }

    public FichaResponseTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }
    public FichaResponseTO(Ficha ficha, List<String> diasSemana) {
        this.id = ficha.getCodigo();
        this.versao = ficha.getVersao();
        if(ficha.getAtivo() != null){
            this.ativo = ficha.getAtivo();
        }
        this.predefinida = ficha.isUsarComoPredefinida();
        try{
            if (null != ficha.getNome() && ficha.getNome().contains("#@")){
                this.nome = Uteis.getTrimNomeFicha(ficha.getNome());
            }else{
                this.nome = ficha.getNome();
            }
            // if (ficha.getCategoria() != null)
            this.categoria = new CategoriaFichaResponseTO(ficha.getCategoria());
            if (ficha.getProgramas() != null && ficha.getProgramas().size() > 0) {
                this.tipo_execucao = ficha.getProgramas().get(0).getTipoExecucao();
            }
            this.mensagem = ficha.getMensagemAluno();
            if (ficha.getAtividades() != null) {
                List<AtividadeFicha> atividadesOrdenadas = Ordenacao.ordenarLista(ficha.getAtividades(), "ordem");
                for (AtividadeFicha af : atividadesOrdenadas) {
                    this.atividades.add(new AtividadeFichaResponseTO(af));
                }
            }
            this.ultimaExecucao = ficha.getUltimaExecucao();
            this.dias_semana = diasSemana;
        }catch (Exception e){
            Uteis.logar(e, FichaResponseTO.class);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CategoriaFichaResponseTO getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaFichaResponseTO categoria) {
        this.categoria = categoria;
    }

    public TipoExecucaoEnum getTipo_execucao() {
        return tipo_execucao;
    }

    public void setTipo_execucao(TipoExecucaoEnum tipo_execucao) {
        this.tipo_execucao = tipo_execucao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public List<AtividadeFichaResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeFichaResponseTO> atividades) {
        this.atividades = atividades;
    }

    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public List<String> getDias_semana() {
        return dias_semana;
    }

    public void setDias_semana(List<String> diasSemana) {
        this.dias_semana = diasSemana;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getPredefinida() {
        return predefinida;
    }

    public void setPredefinida(Boolean predefinida) {
        this.predefinida = predefinida;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }
}
