/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.objeto.Uteis;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ItemMediaBI implements Serializable{
    
    private String matricula = "";
    private String nome = "";
    private Integer duracao = 0;

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }
    
    public Integer getMes(){
        return duracao / 30;
    }
    public Integer getDia(){
        int dias = duracao - getMes()*30;
        return dias >= 0  ? dias : duracao;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getNomeAbreviado() {
        if (nome != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        } else {
            return "";
        }
    }
}
