/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class FiltrosDashboard implements Serializable{
    
    private Integer empresa;
    private Integer diasParaTras = 90;
    private Integer diasParaFrente = 90;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getDiasParaTras() {
        return diasParaTras;
    }

    public void setDiasParaTras(Integer diasParaTras) {
        this.diasParaTras = diasParaTras;
    }

    public Integer getDiasParaFrente() {
        return diasParaFrente;
    }

    public void setDiasParaFrente(Integer diasParaFrente) {
        this.diasParaFrente = diasParaFrente;
    }

    public FiltrosDashboard clone(){
        FiltrosDashboard filtro = new FiltrosDashboard();
        filtro.empresa = this.empresa;
        filtro.diasParaFrente = this.diasParaFrente;
        filtro.diasParaTras = this.diasParaTras;
        return filtro;
    }
    
}
