/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.util.enumeradores.DiasSemana;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class DiasSemanaDashboardBI implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private DiasSemana diaSemana;
    private Integer totalManha = 0;
    private Integer totalTarde = 0;
    private Integer totalNoite = 0;
    private Integer nrDias = 0;
    private Integer ano;
    private Integer mes;
    private Integer codigoProfessor;
    private Integer empresa;
    @Transient
    private List<Date> dias = new ArrayList<Date>();
        
    public Integer getMediaNoite(){
        try {
            return totalNoite/nrDias;
        } catch (Exception e) {
            return 0;
        }
    }
    public Integer getMediaTarde(){
        try {
            return totalTarde/nrDias;
        } catch (Exception e) {
            return 0;
        }
    }
    public Integer getMediaManha(){
        try {
            return totalManha/nrDias;
        } catch (Exception e) {
            return 0;
        }
    }
    
    
    public Integer getOrdem(){
        return diaSemana == null ? 0 : diaSemana.getNumeral();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public DiasSemana getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(DiasSemana diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Integer getTotalManha() {
        return totalManha;
    }

    public void setTotalManha(Integer totalManha) {
        this.totalManha = totalManha;
    }

    public Integer getTotalTarde() {
        return totalTarde;
    }

    public void setTotalTarde(Integer totalTarde) {
        this.totalTarde = totalTarde;
    }

    public Integer getTotalNoite() {
        return totalNoite;
    }

    public void setTotalNoite(Integer totalNoite) {
        this.totalNoite = totalNoite;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public List<Date> getDias() {
        return dias;
    }

    public void setDias(List<Date> dias) {
        this.dias = dias;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
}
