/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;

/**
 *
 * <AUTHOR>
 */

//   TODO: >>>>>>>>>>>>>  ESTA CLASSE TRABALHA COM O ORDINAL DO ENUM SALVO NO BANCO. DE MANEIRA ALGUMA UM NOVO INDICADOR PODE SER INSERIDO NO MEIO DOS JÁ EXISTENTES, SEMPRE DEVE SER INSERIDO NO FINAL..    <<<<<<<<<<<<<<<
public enum IndicadorDashboardEnum {
    TOTAL_ALUNOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"total.alunos", "TW", "listaAlunos", CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Total de alunos"),
    ATIVOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ativos","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos Ativos"),
    INATIVOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"inativos","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos inativos"),
    SEM_ACOMPANHAMENTO(null,"sem.acompanhamento","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_FOTOS_DASHBOARD,true, "sem acompanhamento"),
    EM_ACOMPANHAMENTO(null,"em.acompanhamento","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_FOTOS_DASHBOARD,true, "em acompanhamento"),
    ATIVOS_COM_TREINO(AgrupamentoIndicadorDashboardEnum.TREINO,"alunos.com.treino","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Com treino"),
    COM_AVALIACAO_FISICA(AgrupamentoIndicadorDashboardEnum.AGENDA,"com.avaliacao", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Com Avaliação Física"),
    SEM_AVALIACAO(AgrupamentoIndicadorDashboardEnum.AGENDA,"sem.avaliacao.fisica", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Sem Avaliação Física"),
    ATIVOS_SEM_TREINO(AgrupamentoIndicadorDashboardEnum.TREINO,"BI_SEM_TREINO", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Sem treino"),
    EM_DIA(AgrupamentoIndicadorDashboardEnum.TREINO,"em.dia", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_TREINO_DASHBOARD,true, "Programa em dia"),
    VENCIDOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"gestao.vencidos", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_TREINO_DASHBOARD,true, "Contratos vencidos"),
    TREINOS_A_VENCER(AgrupamentoIndicadorDashboardEnum.TREINO,"treinos.avencer", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_TREINO_DASHBOARD,true, "Treinos vencendo"),
    AVALIACOES(AgrupamentoIndicadorDashboardEnum.TREINO,"avaliacoes.alunos","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações pelo aplicativo"),
    ESTRELAS_5(AgrupamentoIndicadorDashboardEnum.TREINO,"ESTRELAS_5","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações com 5 estrela"),
    ESTRELAS_4(AgrupamentoIndicadorDashboardEnum.TREINO,"ESTRELAS_4","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações com 4 estrela"),
    ESTRELAS_3(AgrupamentoIndicadorDashboardEnum.TREINO,"ESTRELAS_3","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações com 3 estrela"),
    ESTRELAS_2(AgrupamentoIndicadorDashboardEnum.TREINO,"ESTRELAS_2","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações com 2 estrela"),
    ESTRELAS_1(AgrupamentoIndicadorDashboardEnum.TREINO,"ESTRELAS_1","TW", "listatreinos",CategoriaIndicadorEnum.COLUNAS_AVALIACAO_TREINO_DASHBOARD,true, "Avaliações com 1 estrela"),
    RENOVARAM(AgrupamentoIndicadorDashboardEnum.ALUNOS,"renovaram","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Renovaram contrato"),
    NAO_RENOVARAM(AgrupamentoIndicadorDashboardEnum.ALUNOS,"nao.renovaram","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos que não renovaram contrato"),
    ALUNOS_A_VENCER(AgrupamentoIndicadorDashboardEnum.ALUNOS,"alunos.a.vencer","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_VENCIMENTO_DASHBOARD,true, "Contratos a vencer"),
    TROCARAM_CARTEIRA(AgrupamentoIndicadorDashboardEnum.ALUNOS,"trocaram.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Trocaram de carteira"),
    NOVOS_CARTEIRA(AgrupamentoIndicadorDashboardEnum.ALUNOS,"novos.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Novos alunos na carteira"),
    BI_TEMPO_CARTEIRA(AgrupamentoIndicadorDashboardEnum.ALUNOS,"BI_TEMPO_CARTEIRA", "ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Média na carteira (meses)"),
    BI_TEMPO_PROGRAMA(AgrupamentoIndicadorDashboardEnum.TREINO,"BI_TEMPO_PROGRAMA", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Média do programa de treino (meses)"),
    ACESSOS_TREINO(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ACESSOS_TREINO", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ACESSO_DASHBOARD,true, "Nr. alunos do Treino que acessaram"),
    EXECUCOES_TREINO(AgrupamentoIndicadorDashboardEnum.TREINO,"EXECUCOES_TREINO", "TR", "listatreinos",CategoriaIndicadorEnum.COLUNAS_TREINO_REALIZADO_DASHBOARD,true, "Nr. de execuções de Treino"),
    SMARTPHONE(AgrupamentoIndicadorDashboardEnum.TREINO,"SMARTPHONE", "TR", "listatreinos",CategoriaIndicadorEnum.COLUNAS_TREINO_REALIZADO_DASHBOARD,true, "Smatrphone"),
    AGENDAMENTOS_DISPONIBILIDADE(AgrupamentoIndicadorDashboardEnum.AGENDA,"disponibilidade.agendamentos", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Disponibilidade"),
    AGENDAMENTOS_PROFESSORES(null,"professores.agendamentos", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,false, "agendamentos"),
    PROFESSORES(null,"professores", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_PROFESSORES_DASHBOARD,false, "professores"),
    HRS_DISPONIBILIDADE(AgrupamentoIndicadorDashboardEnum.AGENDA,"horas.disponibilidade", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Horas disponibilidade na agenda"),
    HRS_ATENDIMENTO(AgrupamentoIndicadorDashboardEnum.AGENDA,"horas.atendimento", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Horas atendimento na agenda"),
    OCUPACAO(AgrupamentoIndicadorDashboardEnum.AGENDA,"disponibilidade.ocupacao", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agenda: Ocupação"),
    NOVOS_TREINOS(AgrupamentoIndicadorDashboardEnum.AGENDA,"novos.treinos", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_PROGRAMA_DASHBOARD,true, "Agenda: Novos treinos"),
    TREINOS_RENOVADOS(AgrupamentoIndicadorDashboardEnum.AGENDA,"treinos.renovados", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_PROGRAMA_DASHBOARD,true, "Agenda: Treinos que foram renovados"),
    TREINOS_REVISADOS(AgrupamentoIndicadorDashboardEnum.AGENDA,"treinos.revisados", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_PROGRAMA_REVISAO_DASHBOARD,true, "Agenda: Treinos que foram revisados"),
    AVALIACOES_FISICAS(AgrupamentoIndicadorDashboardEnum.AGENDA,"avaliacoes.fisicas", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agenda: Av. físicas que foram realizadas"),
    FALTARAM(AgrupamentoIndicadorDashboardEnum.AGENDA,"FALTARAM", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agenda: Alunos que faltaram"),
    COMPARECERAM(AgrupamentoIndicadorDashboardEnum.AGENDA,"COMPARECERAM", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agenda: Compareceram"),
    CANCELARAM(AgrupamentoIndicadorDashboardEnum.AGENDA,"CANCELARAM", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true ,"Agenda: Alunos que cancelaram"),
    AG_CONFIRMACAO(AgrupamentoIndicadorDashboardEnum.AGENDA,"AG_CONFIRMACAO", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agenda: Alunos que confirmaram presença"),
    CONFIRMARAM(AgrupamentoIndicadorDashboardEnum.AGENDA,"CONFIRMARAM", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, ""),
    DISPONIBILIDADES(AgrupamentoIndicadorDashboardEnum.AGENDA,"DISPONIBILIDADES", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,false, "Disponibilidades"),
    AGENDAMENTOS(AgrupamentoIndicadorDashboardEnum.AGENDA,"AGENDAMENTOS", "TR", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD,true, "Agendamentos"),
    NOVOS_CARTEIRA_NOVOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"novos.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Novos alunos na carteira"),
    NOVOS_CARTEIRA_TROCARAM(AgrupamentoIndicadorDashboardEnum.ALUNOS,"novos.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Trocaram de carteira"),
    PERCENTUAL_CRESCIMENTO_CARTEIRA(AgrupamentoIndicadorDashboardEnum.ALUNOS,"perc.novos.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, true, "% crescimento carteira"),
    PERCENTUAL_RENOVACAO_CARTEIRA(AgrupamentoIndicadorDashboardEnum.ALUNOS,"perc.novos.carteira","ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, true, "% renovação de contrato"),
    PERC_TREINO_VENCIDOS(AgrupamentoIndicadorDashboardEnum.TREINO,"gestao.treino.perc.vencidos", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_TREINO_DASHBOARD,true, true, "% treinos vencidos"),
    PERC_TREINO_EM_DIA(AgrupamentoIndicadorDashboardEnum.TREINO,"gestao.treino.perc.vencidos", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_TREINO_DASHBOARD,true, true, "% treinos em dia"),
    ALUNOS_APP_INSTALADO(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ALUNOS_APP_INSTALADO", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos ativos que utilizam o App"),
    ALUNOS_APP_NAO_INSTALADO(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ALUNOS_APP_NAO_INSTALADO", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos ativos que não utilizam o App"),
    ALUNOS_APP_INSTALADO_ATIVOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ALUNOS_APP_INSTALADO_ATIVOS", "TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos ativos que utilizam o App"),
    ALUNOS_CANCELADOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ALUNOS_CANCELADOS","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,true, "Alunos cancelados"),
    REAGENDARAM(AgrupamentoIndicadorDashboardEnum.AGENDA,"REAGENDARAM", "TR", "listaAlunos", CategoriaIndicadorEnum.COLUNAS_AGENDA_DASHBOARD, true, "Agenda: Alunos que reagendaram"),
    VISITANTES(null,"visitantes","TW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD,false, "visitantes"),
    ACESSOS(AgrupamentoIndicadorDashboardEnum.ALUNOS,"ACESSOS", "ZW", "listaAlunos",CategoriaIndicadorEnum.COLUNAS_ACESSO_DASHBOARD,false, "acessos"),
    ;
    private AgrupamentoIndicadorDashboardEnum agrupamento;
    private String titulo;
    private String origem;
    private String idExport;
    private String colunas;
    private String labelSort;
    private Boolean vaiProRanking;
    private Boolean percentual = Boolean.FALSE;
    

    private IndicadorDashboardEnum(AgrupamentoIndicadorDashboardEnum agrupamento, String titulo, String origem, String idExport, String colunas, Boolean vaiProRanking, String labelSort) {
        this.titulo = titulo;
        this.origem = origem;
        this.idExport = idExport;
        this.colunas = colunas;
        this.vaiProRanking = vaiProRanking;
        this.labelSort = labelSort;
        this.agrupamento = agrupamento;
    }

    private IndicadorDashboardEnum(AgrupamentoIndicadorDashboardEnum agrupamento, String titulo, String origem, String idExport, String colunas, Boolean vaiProRanking, Boolean percentual, String labelSort) {
        this.titulo = titulo;
        this.origem = origem;
        this.idExport = idExport;
        this.colunas = colunas;
        this.vaiProRanking = vaiProRanking;
        this.percentual = percentual;
        this.labelSort = labelSort;
        this.agrupamento = agrupamento;
    }

    public static IndicadorDashboardEnum getFromOrdinal(int o){
        for(IndicadorDashboardEnum ind : values()){
            if(ind.ordinal() == o){
                return ind;
            }
        }
        return null;
    }

    public String getColunas() {
        return colunas;
    }

    public void setColunas(String colunas) {
        this.colunas = colunas;
    }
    
    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
    

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getIdExport() {
        return idExport;
    }

    public void setIdExport(String idExport) {
        this.idExport = idExport;
    }

    public Boolean getVaiProRanking() {
        return vaiProRanking;
    }

    public void setVaiProRanking(Boolean vaiProRanking) {
        this.vaiProRanking = vaiProRanking;
    }

    public Boolean getPercentual() {
        return percentual;
    }

    public void setPercentual(Boolean percentual) {
        this.percentual = percentual;
    }

    public String getLabelSort() {
        return labelSort;
    }

    public AgrupamentoIndicadorDashboardEnum getAgrupamento() {
        return agrupamento;
    }
}
