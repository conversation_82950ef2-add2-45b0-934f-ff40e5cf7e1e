/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import org.json.JSONArray;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class MicroCharts implements Serializable{
    
    private String vencer = "";
    private String novosCarteira = "";
    private String trocaram = "";
    private String tempoMedioCarteira = "";
    private String ativosSemTreino = "";
    private String treinosRenovar = "";
    private String tempoMedioPrograma = "";

    public String getVencer() {
        return vencer;
    }

    public void setVencer(String vencer) {
        this.vencer = vencer;
    }

    

    public String getNovosCarteira() {
        return novosCarteira;
    }

    public void setNovosCarteira(String novosCarteira) {
        this.novosCarteira = novosCarteira;
    }

    public String getTrocaram() {
        return trocaram;
    }

    public void setTrocaram(String trocaram) {
        this.trocaram = trocaram;
    }

    public String getTempoMedioCarteira() {
        return tempoMedioCarteira;
    }

    public void setTempoMedioCarteira(String tempoMedioCarteira) {
        this.tempoMedioCarteira = tempoMedioCarteira;
    }

    public String getAtivosSemTreino() {
        return ativosSemTreino;
    }

    public void setAtivosSemTreino(String ativosSemTreino) {
        this.ativosSemTreino = ativosSemTreino;
    }

    public String getTreinosRenovar() {
        return treinosRenovar;
    }

    public void setTreinosRenovar(String treinosRenovar) {
        this.treinosRenovar = treinosRenovar;
    }

    public String getTempoMedioPrograma() {
        return tempoMedioPrograma;
    }

    public void setTempoMedioPrograma(String tempoMedioPrograma) {
        this.tempoMedioPrograma = tempoMedioPrograma;
    }

    
    
}
