/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class TipoEventoDisponibilidadeJSON extends SuperJSON{
    
    private String tipo;
    private Long disponibilidade;
    private Long executaram;
    private Long cancelaram;
    private Long faltaram;
    private Long aguardandoConfirmacao;
    private Long confirmado;

    public TipoEventoDisponibilidadeJSON() {
        
    }
    public TipoEventoDisponibilidadeJSON(TipoEventoDisponibilidadeBI te) {
        this.tipo = te.getTipo().getDescricaoSimples();
        this.disponibilidade = te.getDisponibilidade();
        this.executaram = te.getExecutaram();
        this.cancelaram = te.getCancelaram();
        this.faltaram = te.getFaltaram();
        this.aguardandoConfirmacao = te.getAguardandoConfirmacao();
        this.confirmado = te.getConfirmado();
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Long getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Long disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public Long getExecutaram() {
        return executaram;
    }

    public void setExecutaram(Long executaram) {
        this.executaram = executaram;
    }

    public Long getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Long cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Long getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Long faltaram) {
        this.faltaram = faltaram;
    }

    public Long getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Long aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public Long getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Long confirmado) {
        this.confirmado = confirmado;
    }
}
