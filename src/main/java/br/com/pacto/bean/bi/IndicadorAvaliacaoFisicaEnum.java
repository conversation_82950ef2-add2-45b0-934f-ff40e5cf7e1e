package br.com.pacto.bean.bi;

public enum IndicadorAvaliacaoFisicaEnum {

    IND_AF_TODAS(true, false),
    IND_AF_NOVAS(true, false),
    IND_AF_REAVALIACOES(true, false),
    IND_AF_PREVISTAS(true, false),
    IND_AF_REALIZADAS(true, false),
    IND_AF_ATRASADAS(true, false),
    IND_AF_FUTURAS(true, false),
    IND_AF_ATIVOS_SEM(false, false),
    IND_AF_ATIVOS_AVALIACAO_ATRASADA(false, false),
    IND_AF_PERDERAM_PERCENTUAL(false, true),
    IND_AF_PERDERAM_PESO(false, true),
    IND_AF_GANHARAM_MASSA(false, true),
    IND_AF_PARQ_POSITIVO(false, false),
    IND_AF_OBJETIVOS(false, false);

    private Boolean camposAvaliacao;
    private Boolean camposDif;

    IndicadorAvaliacaoFisicaEnum(Boolean camposAvaliacao, Boolean camposDif) {
        this.camposAvaliacao = camposAvaliacao;
        this.camposDif = camposDif;
    }

    public Boolean getCamposAvaliacao() {
        return camposAvaliacao;
    }

    public void setCamposAvaliacao(Boolean camposAvaliacao) {
        this.camposAvaliacao = camposAvaliacao;
    }

    public Boolean getCamposDif() {
        return camposDif;
    }

    public void setCamposDif(Boolean camposDif) {
        this.camposDif = camposDif;
    }

    public String getNm(){
        return name();
    }

}
