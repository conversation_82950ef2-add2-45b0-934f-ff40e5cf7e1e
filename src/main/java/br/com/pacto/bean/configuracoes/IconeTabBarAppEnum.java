/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.configuracoes;

/**
 *
 * <AUTHOR>
 */
public enum IconeTabBarAppEnum {

    FEED(0, "FEED_APP", "tabBar-img-gray", "tabBar-img-red", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 21.72 18.56\"><defs><style>.cls-1{fill:gray;}</style></defs><title>icon_feed</title><g id=\"Camada_2\" data-name=\"Camada 2\"><g id=\"Camada_1-2\" data-name=\"Camada 1\"><rect class=\"cls-1\" x=\"3.42\" y=\"3.37\" width=\"15\" height=\"7.63\"/><path class=\"cls-1\" d=\"M21.25,0H.47A.49.49,0,0,0,0,.51V18a.5.5,0,0,0,.47.52H21.25a.5.5,0,0,0,.47-.52V.51A.49.49,0,0,0,21.25,0ZM18.89,16.32H2.83a.52.52,0,0,1,0-1H18.89a.52.52,0,0,1,0,1Zm0-2.09H2.83a.52.52,0,0,1,0-1H18.89a.52.52,0,0,1,0,1Zm.47-2.71a.49.49,0,0,1-.47.51H3a.5.5,0,0,1-.48-.51V2.86A.5.5,0,0,1,3,2.34H18.89a.49.49,0,0,1,.47.52Z\"/></g></g></svg>", (ConfiguracoesEnum[]) null),
    AULAS_TURMAS(1, "MODULO_AULAS, MODULO_AULAS_ABA_TURMAS", "tabBar-img-gray", "tabBar-img-red", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 25 20.32\"><defs><style>.cls-1{fill:gray;}.tabBar-img-gray{fill: gray !important;}.tabBar-img-red{fill: red !important;}</style></defs><title>aulas</title><g id=\"Camada_2\" data-name=\"Camada 2\"><g id=\"Camada_1-2\" data-name=\"Camada 1\"><path class=\"cls-1 STATUS_BTN\" d=\"M6.76,12.32l0-.12a.92.92,0,0,0,.29,0h.2l4-.76a1,1,0,0,0,.7-1.24,6.33,6.33,0,0,0-2.2-3.56A3.5,3.5,0,0,0,8.54,5.18,7,7,0,0,1,14.22,2c1.25-.11.88-2-.32-2h-.1A8.84,8.84,0,0,0,6.73,4.27a34.41,34.41,0,0,0-3.5,6.65A54.83,54.83,0,0,0,.07,18.56a1.36,1.36,0,0,0,1.31,1.76,1,1,0,0,0,1-.77c.64-1.88,1.4-3.91,2.26-5.79.86,1.91,1.73,3.82,2.66,5.7a1.05,1.05,0,0,0,1,.64,1.34,1.34,0,0,0,1.21-1.91C8.53,16.25,7.64,14.29,6.76,12.32ZM8.89,8.59a4.34,4.34,0,0,1,.83,1.13l-2,.37A11.7,11.7,0,0,1,8.89,8.59Z\"/><path class=\"cls-1\" d=\"M10,5.14A1.73,1.73,0,1,0,11.68,3.4,1.73,1.73,0,0,0,10,5.14Z\"/><path class=\"cls-1\" d=\"M23.9,0h-.1a8.82,8.82,0,0,0-7.06,4.27,33.87,33.87,0,0,0-3.5,6.65,52.51,52.51,0,0,0-3.16,7.64,1.35,1.35,0,0,0,1.3,1.76,1,1,0,0,0,1-.77,60,60,0,0,1,2.25-5.79c.86,1.91,1.73,3.82,2.66,5.7a1.06,1.06,0,0,0,1,.64,1.34,1.34,0,0,0,1.21-1.91c-1-1.94-1.85-3.9-2.73-5.87a.56.56,0,0,1,0-.12.82.82,0,0,0,.28,0h.2l4-.76a1,1,0,0,0,.7-1.24,6.33,6.33,0,0,0-2.2-3.56,3.5,3.5,0,0,0-1.28-1.49A7,7,0,0,1,24.22,2C25.48,1.87,25.1,0,23.9,0Zm-5,8.59a4.84,4.84,0,0,1,.83,1.13l-2,.37A10.91,10.91,0,0,1,18.9,8.59Z\"/><path class=\"cls-1\" d=\"M21.68,3.4a1.74,1.74,0,1,0,1.73,1.74A1.73,1.73,0,0,0,21.68,3.4Z\"/></g></g></svg>", ConfiguracoesEnum.MODULO_AULAS, ConfiguracoesEnum.MODULO_AULAS_ABA_TURMAS),
    CROSSFIT(2, "CROSSFIT(HABILITAR_CROSSFIT)", "tabBar-img-gray", "tabBar-img-red", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 18.74 25\"><defs><style>.cls-1{fill:gray;}.tabBar-img-gray{fill: gray !important;}.tabBar-img-red{fill: red !important;}</style></defs><title>icon_crosfit</title><g id=\"Camada_2\" data-name=\"Camada 2\"><g id=\"Camada_1-2\" data-name=\"Camada 1\"><path class=\"cls-1 STATUS_BTN\" d=\"M15.82,9.05h0c.23-.33,2.22-3.2,1.91-5A4.48,4.48,0,0,0,15.08.87h0A17.7,17.7,0,0,0,9.34,0,17.21,17.21,0,0,0,3.67.86h0A4.54,4.54,0,0,0,.93,4a0,0,0,0,1,0,0c-.22,1.78,1.76,4.64,2,5l-.19.18a9.25,9.25,0,0,0,0,13.1A9.35,9.35,0,0,0,9.33,25,9.5,9.5,0,0,0,16,22.32a9.07,9.07,0,0,0,2.76-6.54A9.26,9.26,0,0,0,15.82,9.05ZM4.9,19.61a.46.46,0,0,1-.24.08.45.45,0,0,1-.35-.19,8.92,8.92,0,0,1-1.48-4.91.42.42,0,0,1,.84,0A8,8,0,0,0,5,19,.42.42,0,0,1,4.9,19.61Zm.68,1.44A.42.42,0,1,1,6,20.63.42.42,0,0,1,5.58,21.05ZM13.23,7.32a9.52,9.52,0,0,0-7.73,0,3.81,3.81,0,0,1-.92-1.93A1.79,1.79,0,0,1,5.9,4.1h0a13.41,13.41,0,0,1,6.89,0h0a1.79,1.79,0,0,1,1.33,1.27s0,0,0,0C14.26,5.82,13.72,6.71,13.23,7.32Z\"/></g></g></svg>", ConfiguracoesEnum.HABILITAR_CROSSFIT),
    TREINO(3, "TREINO(MODULO_TREINAR)", "tabBar-img-gray", "tabBar-img-red", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 20.17\"><defs><style>.cls-1{fill:gray;}.tabBar-img-gray{fill: gray !important;}.tabBar-img-red{fill: red !important;}</style></defs><title>icon_treino</title><g id=\"Camada_2\" data-name=\"Camada 2\"><g id=\"Camada_1-2\" data-name=\"Camada 1\"><path class=\"cls-1 STATUS_BTN\" d=\"M21.61,1,20.06,2.09,18.92.47A1.07,1.07,0,0,0,17.41.2L16.36.93a1.37,1.37,0,0,0-.22.2L15.91.8A1.09,1.09,0,0,0,14.4.53L12.67,1.74a1.07,1.07,0,0,0-.27,1.51l2,2.82L7.42,10.92l-2-2.82a1.09,1.09,0,0,0-1.51-.27L2.2,9a1.1,1.1,0,0,0-.27,1.52l.23.32A1.3,1.3,0,0,0,1.9,11l-1,.73a1.1,1.1,0,0,0-.27,1.52l1.14,1.62L.17,16a.41.41,0,0,0-.1.56l1.77,2.53a.4.4,0,0,0,.55.1l1.55-1.08,1.14,1.63A1.09,1.09,0,0,0,6.59,20l1-.74a1,1,0,0,0,.22-.2l.23.33a1.08,1.08,0,0,0,1.51.27l1.73-1.21a1.07,1.07,0,0,0,.27-1.51l-2-2.82,6.94-4.85,2,2.82a1.09,1.09,0,0,0,1.51.27l1.74-1.21a1.09,1.09,0,0,0,.27-1.51l-.23-.33a1.3,1.3,0,0,0,.26-.14l1-.73a1.09,1.09,0,0,0,.27-1.51L22.28,5.28,23.83,4.2a.41.41,0,0,0,.1-.56L22.16,1.11A.4.4,0,0,0,21.61,1Z\"/></g></g></svg>", ConfiguracoesEnum.MODULO_TREINAR),
    NOTIFICACOES(4, "NOTIFICACOES", "tabBar-img-gray", "tabBar-img-red", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20.77 20.81\"><defs><style>.cls-1{fill:gray;}</style></defs><title>icon_notificacoes</title><g id=\"Camada_2\" data-name=\"Camada 2\"><g id=\"Camada_1-2\" data-name=\"Camada 1\"><path id=\"Notification\" class=\"cls-1\" d=\"M16.26,1.64l.29-.86A.55.55,0,0,0,16.2,0a.57.57,0,0,0-.74.35l-.33.84A7.77,7.77,0,0,0,5.83,6L3.9,11.23.34,12.87A.57.57,0,0,0,.4,13.94l6.41,2.34a3.22,3.22,0,0,0,2.08,3.57,3.29,3.29,0,0,0,3.92-1.4l6.48,2.32h0a.59.59,0,0,0,.77-.36.58.58,0,0,0-.06-.5l-1.63-3.48,1.93-5.29A7.55,7.55,0,0,0,16.26,1.64Z\"/></g></g></svg>", (ConfiguracoesEnum[]) null);
    private Integer ordem;
    private String configName;
    private String svg;
    private String classInactive;
    private String classActive;
    private ConfiguracoesEnum[] configEnable;

    private IconeTabBarAppEnum(Integer ordem, final String configName, final String classInactive,
            final String classActive, final String svg, ConfiguracoesEnum... configEnable) {
        this.ordem = ordem;
        this.configName = configName;
        this.svg = svg;
        this.configEnable = configEnable;
        this.classInactive = classInactive;
        this.classActive = classActive;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getSvg() {
        return svg;
    }

    public void setSvg(String svg) {
        this.svg = svg;
    }

    public ConfiguracoesEnum[] getConfigEnable() {
        return configEnable;
    }

    public void setConfigEnable(ConfiguracoesEnum[] configEnable) {
        this.configEnable = configEnable;
    }

    public String getClassInactive() {
        return classInactive;
    }

    public void setClassInactive(String classInactive) {
        this.classInactive = classInactive;
    }

    public String getClassActive() {
        return classActive;
    }

    public void setClassActive(String classActive) {
        this.classActive = classActive;
    }
    
}
