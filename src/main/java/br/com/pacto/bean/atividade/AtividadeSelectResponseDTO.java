package br.com.pacto.bean.atividade;

import java.util.List;

/**
 * <AUTHOR> 09/04/2019
 */
public class AtividadeSelectResponseDTO {

    private Integer totalElementos;
    private List<AtividadeCompletaResponseTO> items;

    public AtividadeSelectResponseDTO(Integer totalElementos, List<AtividadeCompletaResponseTO> atividades) {
        this.totalElementos = totalElementos;
        this.items = atividades;
    }

    public Integer getTotalElementos() {
        return totalElementos;
    }

    public void setTotalElementos(Integer totalElementos) {
        this.totalElementos = totalElementos;
    }

    public List<AtividadeCompletaResponseTO> getItems() {
        return items;
    }

    public void setItems(List<AtividadeCompletaResponseTO> items) {
        this.items = items;
    }
}
