/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

/**
 * <AUTHOR>
 */
public enum CategoriaAtividadeWodEnum {

    NENHUM          (0, "Nenhum"),
    BARBELL         (1, "Barbell"),
    GYMNASTIC       (2, "Gymnastic"),
    ENDURANCE       (3, "Endurance"),
    NOTABLES        (4, "Notables"),
    GIRLS           (5, "Girls"),
    OPEN            (6, "Open"),
    THEHEROES       (7, "The Heroes"),
    CAMPEONATOS     (8, "Campeonatos"),
    CROSSFIT_GAMES  (9, "Crossfit Games");

    private Integer id;
    private String descricao;

    private CategoriaAtividadeWodEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public static CategoriaAtividadeWodEnum getFromId(Integer codigo) {
        for (CategoriaAtividadeWodEnum tipo : CategoriaAtividadeWodEnum.values()) {
            if (tipo.getId().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
