package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeCategoriaAtividadeResponseTO {

    private Integer id;
    private String nome;

    public AtividadeCategoriaAtividadeResponseTO(){

    }

    public AtividadeCategoriaAtividadeResponseTO(AtividadeCategoriaAtividade aca) {
        this.id = aca.getCategoriaAtividade().getCodigo();
        this.nome = aca.getCategoriaAtividade().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
