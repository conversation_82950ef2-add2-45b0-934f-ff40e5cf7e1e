package br.com.pacto.bean.atividade;

import br.com.pacto.bean.empresa.Empresa;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Empresa")
public class EmpresaBasicaResponseTO {

    @ApiModelProperty(value = "ID da empresa", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da empresa", example = "Empresa 1")
    private String nome;
    @ApiModelProperty(value = "ID da empresa no ZW", example = "1")
    private Integer empresazw;

    public EmpresaBasicaResponseTO() {

    }
    public EmpresaBasicaResponseTO(Empresa empresa) {
        this.id = empresa.getCodigo();
        this.nome = empresa.getNome();
        this.empresazw = empresa.getCodZW();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getEmpresazw() {
        return empresazw;
    }

    public void setEmpresazw(Integer empresazw) {
        this.empresazw = empresazw;
    }
}
