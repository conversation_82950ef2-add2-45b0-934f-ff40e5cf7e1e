package br.com.pacto.bean.atividade;

/**
 * Created by ulisses on 22/08/2018.
 */
public enum TipoAtividadeEndpointEnum {

    NEUROMUSCULAR(0, "Neuromuscular"),
    CARDIOVASCULAR(1, "Cardiovascular");

    private Integer id;
    private String descricao;

    private TipoAtividadeEndpointEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public TipoAtividadeEnum getTipoAtividadeEnum() {
        switch (this) {
            case NEUROMUSCULAR: return TipoAtividadeEnum.ANAEROBICO;
            case CARDIOVASCULAR: return TipoAtividadeEnum.AEROBICO;
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
