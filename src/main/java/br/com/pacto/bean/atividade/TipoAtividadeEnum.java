/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

/**
 *
 * <AUTHOR>
 */
public enum TipoAtividadeEnum {

    ANAEROBICO(0, "Neuromuscular"),
    AEROBICO(1, "Cardiovascular");
    private Integer id;
    private String descricao;

    private TipoAtividadeEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }
    
    public static TipoAtividadeEnum getFromId(Integer codigo){
        for(TipoAtividadeEnum tipo : TipoAtividadeEnum.values()){
            if(tipo.getId().equals(codigo)){
                return tipo;
            }
        }
        return null;
    }

    public static TipoAtividadeEnum getFromDescricao(String descricao){
        for(TipoAtividadeEnum tipo : TipoAtividadeEnum.values()){
            if(tipo.getDescricao().equalsIgnoreCase(descricao)){
                return tipo;
            }
        }
        return null;
    }

    public TipoAtividadeEndpointEnum getTipoAtividadeEndpointEnum() {
        switch (this) {
            case ANAEROBICO: return TipoAtividadeEndpointEnum.NEUROMUSCULAR;
            case AEROBICO: return TipoAtividadeEndpointEnum.CARDIOVASCULAR;
        }
        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
