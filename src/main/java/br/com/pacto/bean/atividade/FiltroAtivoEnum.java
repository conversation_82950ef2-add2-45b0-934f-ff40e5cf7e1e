package br.com.pacto.bean.atividade;

/**
 * Created by ulisses on 27/08/2018.
 */
public enum FiltroAtivoEnum {

    ATIVO(true, "Ativo"),
    INATIVO(false, "Inativo");

    private Boolean ativo;
    private String descricao;

    private FiltroAtivoEnum(Boolean ativo, String descricao) {
        this.ativo = ativo;
        this.descricao = descricao;
    }

    public Boolean getAtivo() { return ativo; }

    public void setAtivo(Boolean ativo) { this.ativo = ativo; }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
