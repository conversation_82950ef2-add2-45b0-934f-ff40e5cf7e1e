package br.com.pacto.bean.colaborador;

/**
 * Created by ulisses on 27/08/2018.
 */
public enum SituacaoColaboradorEnum {

    ATIVO(true, "Ativo"),
    INATIVO(false, "Inativo");

    private Boolean ativo;
    private String descricao;

    private SituacaoColaboradorEnum(Boolean ativo, String descricao) {
        this.ativo = ativo;
        this.descricao = descricao;
    }

    public static SituacaoColaboradorEnum fromBoolean(Boolean valor) {
        if (valor) {
            return ATIVO;
        } else {
            return INATIVO;
        }
    }

    public Boolean getAtivo() { return ativo; }

    public void setAtivo(Boolean ativo) { this.ativo = ativo; }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
