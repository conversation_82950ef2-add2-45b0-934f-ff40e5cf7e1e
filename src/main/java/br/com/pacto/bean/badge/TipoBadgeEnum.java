/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.badge;

import br.com.pacto.controller.json.badge.BadgeJSON;
import br.com.pacto.objeto.Uteis;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum TipoBadgeEnum {

    CARGA(0, "Superman", "http://app.pactosolucoes.com.br/midias/badges/badge-carga.png", 
            "Levantou o equivalente a um ", "carro popular","", "27/01/2014", 1, "Peso (Kg)", null),
    DISTANCIA(1, "Maratonista", "http://app.pactosolucoes.com.br/midias/badges/badge-espaco.png", 
            "Percorreu a distância de ", " m","", "27/01/2014", 1, "Distância (m)", null),
    FREQUENCIA(2, "Incansável", "http://app.pactosolucoes.com.br/midias/badges/badge-freq.png", 
            "Teve uma frequência de mais de ", "% depois de atingir "," % de execuções", "27/01/2014", 2, "Frequência (%)", "Quando medir execuções (%)");
    private Integer id;
    private String nome;
    private String img;
    private String descricao1;
    private String descricao2;
    private String descricao3;
    private Date dataCriacao;
    private Integer numeroParametros;
    private String nomeParametro1;
    private String nomeParametro2;

    private TipoBadgeEnum(Integer id, final String nome, final String img, final String descricao1, final String descricao2,
            final String descricao3,final String dataCriacao, final Integer numeroParametros, final String nomeParam1, final String nomeParam2) {
        this.id = id;
        this.nome = nome;
        this.img = img;
        this.descricao1 = descricao1;
        this.descricao2 = descricao2;
        this.descricao3 = descricao3;
        try{
           this.dataCriacao = Uteis.getDate(dataCriacao); 
        }catch(Exception e){
            
        }
        this.numeroParametros = numeroParametros;
        this.nomeParametro1 = nomeParam1;
        this.nomeParametro2 = nomeParam2;
    }

    public String getDescricao3() {
        return descricao3;
    }

    public void setDescricao3(String descricao3) {
        this.descricao3 = descricao3;
    }
    

    public Integer getNumeroParametros() {
        return numeroParametros;
    }

    public void setNumeroParametros(Integer numeroParametros) {
        this.numeroParametros = numeroParametros;
    }
    

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getDescricao1() {
        return descricao1;
    }

    public void setDescricao1(String descricao1) {
        this.descricao1 = descricao1;
    }

    public String getDescricao2() {
        return descricao2;
    }

    public void setDescricao2(String descricao2) {
        this.descricao2 = descricao2;
    }

    public BadgeJSON toJSON() {
        return new BadgeJSON(id.toString(), nome, img, descricao1, descricao2);
    }

    public static List<BadgeJSON> toListJSON() {
        List<BadgeJSON> lista = new ArrayList<BadgeJSON>();
        for (TipoBadgeEnum b : TipoBadgeEnum.values()) {
            lista.add(new BadgeJSON(b));
        }
        return lista;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getNomeParametro1() {
        return nomeParametro1;
    }

    public void setNomeParametro1(String nomeParametro1) {
        this.nomeParametro1 = nomeParametro1;
    }

    public String getNomeParametro2() {
        return nomeParametro2;
    }

    public void setNomeParametro2(String nomeParametro2) {
        this.nomeParametro2 = nomeParametro2;
    }
    
    
    
    
}
