/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.animacao;

/**
 *
 * <AUTHOR>
 */
public enum TipoAnimacaoEnum {

    IMAGEM(0, "Imagem"),
    VIDEO(1, "Video");
    private Integer id;
    private String descricao;

    private TipoAnimacaoEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
