/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.animacao;

import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.escopo.Sujeito;
import br.com.pacto.objeto.Uteis;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class Animacao extends Sujeito {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String titulo;
    private String subTitulo;
    private String url;
    private TipoAnimacaoEnum tipo;
    @OneToMany(targetEntity = AtividadeAnimacao.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "animacao")
    private List<AtividadeAnimacao> atividades = new ArrayList<AtividadeAnimacao>();
    @Transient
    private boolean selecionado = false;

    public Animacao() {
    }
    
    public String getAnchor(){
        try {
            return Uteis.retirarAcentuacaoRegex(url).replaceAll(" ", "_").replaceAll("\\.", "");
        } catch (Exception e) {
            return "";
        }
        
    }

    public Animacao(Integer codigo) {
        this.codigo = codigo;
    }

    public Animacao(final String nome) {
        this.titulo = nome;
    }

    @Override
    public String toString() {
        return this.url;
    }

    public Animacao(final String titulo, final String subtitulo, final String url, TipoAnimacaoEnum tipo) {
        this.titulo = titulo;
        this.subTitulo = subtitulo;
        this.url = url;
        this.tipo = tipo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getSubTitulo() {
        return subTitulo;
    }

    public void setSubTitulo(String subTitulo) {
        this.subTitulo = subTitulo;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public TipoAnimacaoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAnimacaoEnum tipo) {
        this.tipo = tipo;
    }

    public List<AtividadeAnimacao> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAnimacao> atividades) {
        this.atividades = atividades;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}
