/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.tvgestor;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class TVGestorItemFavorito implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private TVGestorItem item;
    @ManyToOne
    private AlunoFavorito favorito;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TVGestorItem getItem() {
        return item;
    }

    public void setItem(TVGestorItem item) {
        this.item = item;
    }

    public AlunoFavorito getFavorito() {
        return favorito;
    }

    public void setFavorito(AlunoFavorito favorito) {
        this.favorito = favorito;
    }
    
    
}
