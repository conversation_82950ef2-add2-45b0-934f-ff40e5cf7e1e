/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.tvgestor;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.bean.AgendadoTO;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AlunoFavorito implements Serializable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoCliente;
    private Integer codigoPessoa;
    private Integer matricula;
    private String nome;
    private Integer usuario_codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date acesso;
    private Boolean ok = true;

    public AlunoFavorito() {
    }

    public AlunoFavorito(AgendadoTO agendado, Usuario usuario) {
        this.codigoCliente = agendado.getCodigoCliente();
        this.codigoPessoa = agendado.getCodigoPessoa();
        this.matricula = Integer.valueOf(agendado.getMatricula());
        this.nome = agendado.getNome();
        this.usuario_codigo = usuario.getCodigo();
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario) {
        this.usuario_codigo = usuario;
    }
    
    public String getNomeAbreviado() {
        if (nome != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        } else {
            return "";
        }
    }

    public String getPrimeiroNomeComLetraSobrenome() {
        if (nome != null) {
            return Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome, true);
        }
        return "";
    }

    public Date getAcesso() {
        return acesso;
    }

    public void setAcesso(Date acesso) {
        this.acesso = acesso;
    }

    public Boolean getOk() {
        return ok;
    }

    public void setOk(Boolean ok) {
        this.ok = ok;
    }
    
      
}
