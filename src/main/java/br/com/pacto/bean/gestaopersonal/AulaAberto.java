/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AulaAberto implements Serializable {

    private AulaPersonal aula;
    private Integer creditosGastos = 0;
    private Integer duracaoUmCredito = 0;
    private Integer percentualCredito = 0;
    private Integer minutos = 0;
    private boolean contarCreditoPorAluno = false;
    private Integer valorProgresso = 0;
    private Integer nrAlunosCobram = null;
    private Boolean algumCobra = null;
    private String terminoAtual = "";

    public boolean getRed() {
        return valorProgresso > 70;
    }
    
    public Boolean getAlgumCobra(){
        if(algumCobra == null){
            algumCobra = Boolean.FALSE;
            for (AlunoAulaPersonal aln : aula.getAlunos()) {
                if(aln.getCodigoCliente() != null || (aln.getCodigoColaborador() != null && aln.getColaborador().getCobrarCreditos())){
                    algumCobra = Boolean.TRUE;
                    break;
                }
            }
        }
        return algumCobra;
    }

    public Integer getNrAlunosCobram() {
        if (nrAlunosCobram == null) {
            nrAlunosCobram = aula.getAlunos().isEmpty() ? 1 : 0;
            for (AlunoAulaPersonal aln : aula.getAlunos()) {
                if(aln.getCodigoCliente() != null){
                    nrAlunosCobram++;
                } else if(aln.getCodigoColaborador() != null && aln.getColaborador().getCobrarCreditos()){
                    nrAlunosCobram++;
                }
            }
        }
        return nrAlunosCobram;
    }

    public String getTerminoAtual(){
        if(UteisValidacao.emptyString(terminoAtual)){
            getValorProgress();
        }
        return terminoAtual;
    }


    public int getValorProgress() {
        try {
            int minutosEntreDatas = (int) Uteis.minutosEntreDatas(aula.getCheckIn(), Calendario.hoje());
            creditosGastos = minutosEntreDatas / duracaoUmCredito;
            if (contarCreditoPorAluno) {
                int nrALunos = getNrAlunosCobram();
                creditosGastos = creditosGastos * (nrALunos == 0 ? 1 : nrALunos);
            }
            minutos = minutosEntreDatas % duracaoUmCredito;
            int valorProgress = (minutos * 100) / duracaoUmCredito;
            valorProgresso = valorProgress == 0 ? 1 : valorProgress;

            Date termino = Uteis.somarCampoData(aula.getCheckIn(), Calendar.MINUTE, (duracaoUmCredito * (creditosGastos+1)));
            terminoAtual = Uteis.getDataAplicandoFormatacao(termino, "HH:mm");

            return valorProgresso;
        } catch (Exception e) {
            return 0;
        }
    }

    public boolean isContarCreditoPorAluno() {
        return contarCreditoPorAluno;
    }

    public void setContarCreditoPorAluno(boolean contarCreditoPorAluno) {
        this.contarCreditoPorAluno = contarCreditoPorAluno;
    }

    public Integer getMinutos() {
        return minutos;
    }

    public void setMinutos(Integer minutos) {
        this.minutos = minutos;
    }

    public AulaPersonal getAula() {
        return aula;
    }

    public void setAula(AulaPersonal aula) {
        this.aula = aula;
    }

    public Integer getCreditosGastos() {
        return creditosGastos;
    }

    public void setCreditosGastos(Integer creditosGastos) {
        this.creditosGastos = creditosGastos;
    }

    public Integer getDuracaoUmCredito() {
        return duracaoUmCredito;
    }

    public void setDuracaoUmCredito(Integer duracaoUmCredito) {
        this.duracaoUmCredito = duracaoUmCredito;
    }

    public Integer getPercentualCredito() {
        return percentualCredito;
    }

    public void setPercentualCredito(Integer percentualCredito) {
        this.percentualCredito = percentualCredito;
    }
}
