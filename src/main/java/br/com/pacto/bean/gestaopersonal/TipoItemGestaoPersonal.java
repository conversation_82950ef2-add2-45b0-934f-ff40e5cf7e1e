/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

/**
 *
 * <AUTHOR>
 */
public enum TipoItemGestaoPersonal {

    PAGAMENTO("pagamento"),
    CHECKIN("checkInOut"),
//    CHECKOUT("checkInOut"),
    CONS_PRE_PAGO("consPrePago"),
    CONS_POS_PAGO("consPosPago"),
    LIBERACAO_CHECK_IN("liberacaoCheckIn");
    
    private String css;
    
    public String getNome(){
        return name();
    }
    
    private TipoItemGestaoPersonal(String css){
        this.css = css;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }
    
    
}
