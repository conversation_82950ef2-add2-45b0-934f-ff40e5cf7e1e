/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.professor.ProfessorSintetico;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AgrupamentoGestaoPersonal implements Serializable{
    
    private Date dia;
    private ProfessorSintetico personal;
    private boolean prePago;
    private List<ItemGestaoPersonal> itens = new ArrayList<ItemGestaoPersonal>();
    private Integer saldo;

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }
    
    public String getNome(){
        return getPersonal().getNome();
    }
    
    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public ProfessorSintetico getPersonal() {
        return personal;
    }

    public void setPersonal(ProfessorSintetic<PERSON> personal) {
        this.personal = personal;
    }

    public boolean isPrePago() {
        return prePago;
    }

    public void setPrePago(boolean prePago) {
        this.prePago = prePago;
    }

    public List<ItemGestaoPersonal> getItens() {
        return itens;
    }

    public void setItens(List<ItemGestaoPersonal> itens) {
        this.itens = itens;
    }
    
    
    
}
