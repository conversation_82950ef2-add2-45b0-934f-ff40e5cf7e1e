package br.com.pacto.bean.wod;

import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.crossfit.EquipeEvento;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by <PERSON><PERSON> Alcides on 14/02/2017.
 */
@Entity
@Table
public class ScoreTreino {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer posicao;
    @ManyToOne
    private Benchmark benchmark;
    @ManyToOne
    private Wod wod;
    private Integer tempo;
    private Double peso;
    private Integer repeticoes;
    private Integer rounds;
    @Temporal(TemporalType.TIMESTAMP)
    private Date lancamento;
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 200)
    private String comentario;
    private Boolean rx;
    @ManyToOne
    @JoinColumn(name = "nivelcrossfit")
    NivelWod nivelcrossfit;
    @JsonIgnore
    @ManyToOne
    private Usuario usuario;
    @JsonIgnore
    @ManyToOne
    private EquipeEvento equipe;
    private Integer pontos;
    @Transient
    private String tempoStr;

    @Override
    public boolean equals(Object obj) {
        try {
            ScoreTreino sc = (ScoreTreino) obj;
            if(sc.getPeso() != null && getPeso() != null && !getPeso().equals(sc.getPeso())){
                return false;
            }

            if(sc.getRepeticoes() != null && getRepeticoes() != null && !getRepeticoes().equals(sc.getRepeticoes())){
                return false;
            }

            if(sc.getTempo() != null && getTempo() != null && !getTempo().equals(sc.getTempo())){
                return false;
            }

            if(sc.getRounds() != null && getRounds() != null && !getRounds().equals(sc.getRounds())){
                return false;
            }

            return true;
        }catch (Exception e){

        }
        return super.equals(obj);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Benchmark getBenchmark() {
        return benchmark;
    }

    public void setBenchmark(Benchmark benchmark) {
        this.benchmark = benchmark;
    }

    public Wod getWod() {
        return wod;
    }

    public void setWod(Wod wod) {
        this.wod = wod;
    }

    public Integer getTempo() {
        return tempo;
    }

    public void setTempo(Integer tempo) {
        this.tempo = tempo;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Integer getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(Integer repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Integer getRounds() {
        return rounds;
    }

    public void setRounds(Integer rounds) {
        this.rounds = rounds;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public String getComentario() {
        if (comentario == null) {
            comentario = "";
        }
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Boolean getRx() {
        return rx;
    }

    public void setRx(Boolean rx) {
        this.rx = rx;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public NivelWod getNivelcrossfit() {
        return nivelcrossfit;
    }

    public void setNivelcrossfit(NivelWod nivelcrossfit) {
        this.nivelcrossfit = nivelcrossfit;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getNomeEquipe() {
        try {
            return equipe.getNome();
        }catch (Exception e){
            return "";
        }
    }

    public EquipeEvento getEquipe() {
        return equipe;
    }

    public void setEquipe(EquipeEvento equipe) {
        this.equipe = equipe;
    }

    public String getTempoStr() {
        if (tempoStr == null) {
            tempoStr = Uteis.converterSegundosEmMinutos(tempo == null ? 0 : tempo);
        }
        return tempoStr;
    }

    public void setTempoStr(String tempoStr) {
        this.tempoStr = tempoStr;
    }

    public void setarSegundos() {
        if (tempoStr != null) {
            tempo = Uteis.converterMinutosEmSegundos(tempoStr);
        }
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }
}
