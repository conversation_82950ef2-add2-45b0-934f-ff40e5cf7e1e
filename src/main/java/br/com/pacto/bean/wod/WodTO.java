package br.com.pacto.bean.wod;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WodTO {


    //        "nome":"kyjmjymk",
    private String nome;//OK
    //            "dia":1539054000000,
    private Long dia;//OK
    //            "tipoWod":"1",
    private String tipoWod;//OK
    //            "aquecimento":"vbvbv",
    private String aquecimento; //OK
    //            "alongamentoMobilidade":"vbvbv",
    private String alongamentoMobilidade; //OK
    //            "parteTecnicaSkill":"",
    private String parteTecnicaSkill;//OK
    //            "complexEmom":"vbvbv",
    private String complexEmom; //OK
    //            "wod":"Deadlift 10-10-5-5-3-3 reps\nScroll for scaling options. Post loads to comments.\nRelated: • The Deadlift • Common Flaws in the Deadlift\n",
    private String wod;//OK
    //            "atividade":[
    private Integer[] atividade;//OK
    //        "aparelho":[
    private Integer[] aparelho;//OK
    //        "imagemData":""
    private String imagemData;//OK

    private String extensaoImagem;//OK
    private Boolean compartilharRede = Boolean.FALSE;
    private String nivelWod;



    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getAlongamentoMobilidade() {
        return alongamentoMobilidade;
    }

    public void setAlongamentoMobilidade(String alongamentoMobilidade) {
        this.alongamentoMobilidade = alongamentoMobilidade;
    }

    public String getAquecimento() { return aquecimento; }

    public void setAquecimento(String aquecimento) {
        this.aquecimento = aquecimento;
    }

    public String getParteTecnicaSkill() {
        return parteTecnicaSkill;
    }

    public void setParteTecnicaSkill(String parteTecnicaSkill) {
        this.parteTecnicaSkill = parteTecnicaSkill;
    }

    public String getComplexEmom() {
        return complexEmom;
    }

    public void setComplexEmom(String complexEmom) {
        this.complexEmom = complexEmom;
    }

    public String getWod() {
        return wod;
    }

    public void setWod(String wod) {
        this.wod = wod;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getTipoWod() {
        return tipoWod;
    }

    public void setTipoWod(String tipoWod) {
        this.tipoWod = tipoWod;
    }

    public Integer[] getAtividade() {
        return atividade;
    }

    public void setAtividade(Integer[] atividade) {
        this.atividade = atividade;
    }

    public Integer[] getAparelho() {
        return aparelho;
    }

    public void setAparelhoIds(Integer[] aparelhoIds) {
        this.aparelho = aparelhoIds;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }

    public Boolean getCompartilharRede() {
        return compartilharRede;
    }

    public void setCompartilharRede(Boolean compartilharRede) {
        this.compartilharRede = compartilharRede;
    }

    public String getNivelWod() {
        return nivelWod;
    }

    public void setNivelWod(String nivelWOD) {
        this.nivelWod = nivelWOD;
    }

}
