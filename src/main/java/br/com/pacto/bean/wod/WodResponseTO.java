package br.com.pacto.bean.wod;

import br.com.pacto.bean.aparelho.AparelhoResponseTO;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.AtividadeCompletaResponseTO;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WodResponseTO {

    private List<AtividadeCompletaResponseTO> atividades;
    private List<AparelhoResponseTO> aparelhos;
    private TipoWodResponseTO tipoWod;
    private Integer id;
    private String nome;
    private Date dia;
    private String aquecimento;
    private String alongamentoMobilidade;
    private String parteTecnicaSkill;
    private String complexEmom;
    private String wod;
    private String imagemData;
    private String urlImagem;
    private boolean temResultados;
    private String idRede;
    private String usuarioLancouRede;
    private String chaveLancouRede;
    private String unidadeLancouRede;
    private Boolean compartilharRede = Boolean.FALSE;
    private Integer empresa;
    private String nomeEmpresa;
    private String nivelWod;



    public WodResponseTO(Wod wod, Boolean completo, boolean temResultados) {
        if (completo) {
            this.atividades = new ArrayList<>();
            if (wod.getAtividades() != null) {
                for (AtividadeWod atividade : wod.getAtividades()) {
                    this.atividades.add(new AtividadeCompletaResponseTO(atividade.getAtividade()));
                }
            }
            this.aparelhos = new ArrayList<>();
            if (wod.getAparelhos() != null) {
                for (AparelhoWod aparelho : wod.getAparelhos()) {
                    this.aparelhos.add(new AparelhoResponseTO(aparelho.getAparelho()));
                }
            }
            if (wod.getTipoWodTabela() != null) {
                this.tipoWod = new TipoWodResponseTO(wod.getTipoWodTabela());
            } else {
                this.tipoWod = new TipoWodResponseTO();
            }
        }

        this.id = wod.getCodigo();
        this.nome = wod.getNome();
        try{
            this.dia = Uteis.getDate(Uteis.getData(wod.getDia()));
        }catch (Exception ignore){
            this.dia = null;
        }
        this.compartilharRede = wod.getCompartilharRede();
        this.idRede = wod.getIdRede();
        this.usuarioLancouRede = wod.getUsuarioLancouRede();
        this.chaveLancouRede = wod.getChaveLancouRede();
        this.unidadeLancouRede = wod.getUnidadeLancou();
        this.aquecimento = wod.getAquecimento();
        this.alongamentoMobilidade = wod.getAlongamento();
        this.parteTecnicaSkill = wod.getParteTecnicaSkill();
        this.complexEmom = wod.getComplexEmom();
        this.wod = wod.getDescricaoExercicios();
        this.urlImagem = Aplicacao.obterUrlFotoDaNuvem(wod.getKeyImagem());
        this.temResultados = temResultados;
        this.empresa = wod.getEmpresa();
        this.nomeEmpresa = nomeEmpresa;
        this.nivelWod = wod.getNivelWod();


    }

    public List<AtividadeCompletaResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeCompletaResponseTO> atividades) {
        this.atividades = atividades;
    }

    public List<AparelhoResponseTO> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(List<AparelhoResponseTO> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public TipoWodResponseTO getTipoWod() {
        return tipoWod;
    }

    public void setTipoWod(TipoWodResponseTO tipoWod) {
        this.tipoWod = tipoWod;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getAlongamentoMobilidade() {
        return alongamentoMobilidade;
    }

    public void setAlongamentoMobilidade(String alongamentoMobilidade) {
        this.alongamentoMobilidade = alongamentoMobilidade;
    }

    public String getAquecimento() {
        return aquecimento;
    }

    public void setAquecimento(String aquecimento) {
        this.aquecimento = aquecimento;
    }

    public String getParteTecnicaSkill() {
        return parteTecnicaSkill;
    }

    public void setParteTecnicaSkill(String parteTecnicaSkill) {
        this.parteTecnicaSkill = parteTecnicaSkill;
    }

    public String getComplexEmom() {
        return complexEmom;
    }

    public void setComplexEmom(String complexEmom) {
        this.complexEmom = complexEmom;
    }

    public String getWod() {
        return wod;
    }

    public void setWod(String wod) {
        this.wod = wod;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public boolean isTemResultados() {
        return temResultados;
    }

    public void setTemResultados(boolean temResultados) {
        this.temResultados = temResultados;
    }

    public String getIdRede() {
        return idRede;
    }

    public void setIdRede(String idRede) {
        this.idRede = idRede;
    }

    public String getUsuarioLancouRede() {
        return usuarioLancouRede;
    }

    public void setUsuarioLancouRede(String usuarioLancouRede) {
        this.usuarioLancouRede = usuarioLancouRede;
    }

    public String getChaveLancouRede() {
        return chaveLancouRede;
    }

    public void setChaveLancouRede(String chaveLancouRede) {
        this.chaveLancouRede = chaveLancouRede;
    }

    public Boolean getCompartilharRede() {
        return compartilharRede;
    }

    public void setCompartilharRede(Boolean compartilharRede) {
        this.compartilharRede = compartilharRede;
    }

    public String getUnidadeLancouRede() {
        return unidadeLancouRede;
    }

    public void setUnidadeLancouRede(String unidadeLancouRede) {
        this.unidadeLancouRede = unidadeLancouRede;
    }

    public Integer getEmpresa() {return empresa;}

    public void setEmpresa(Integer empresa) {this.empresa = empresa;}

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getNivelWod() {
        return nivelWod;
    }

    public void setNivelWod(String nivelWod) {
        this.nivelWod = nivelWod;
    }


}
