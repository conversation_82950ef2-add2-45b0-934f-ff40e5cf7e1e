package br.com.pacto.bean.wod;

import br.com.pacto.bean.usuario.Usuario;

import javax.persistence.*;
import java.io.Serializable;

/*
 * Created by <PERSON><PERSON>
 */
@Entity
public class ComentarioWodLike implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Wod wod;
    @ManyToOne
    private ComentarioWod comentarioWod;
    @ManyToOne
    private Usuario usuario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Wod getWod() {
        return wod;
    }

    public void setWod(Wod wod) {
        this.wod = wod;
    }

    public ComentarioWod getComentarioWod() {
        return comentarioWod;
    }

    public void setComentarioWod(ComentarioWod comentarioWod) {
        this.comentarioWod = comentarioWod;
    }

    public Usuario getUsuario() {
        if (usuario == null) {
            usuario = new Usuario();
        }
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
