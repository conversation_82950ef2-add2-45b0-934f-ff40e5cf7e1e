/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.wod;

import br.com.pacto.objeto.ColecaoUtils;
import org.apache.commons.collections.Predicate;

import java.util.Arrays;

/*
 * <AUTHOR>
 */
public enum NivelCrossfitEnum {

    INICIANTE   (0, "IN", "Iniciante"),
    SCALED      (1, "SC", "Scaled"),
    AMADOR      (2, "AM", "Amador"),
    AVANCADO    (3, "RX", "Avançado");

    private Integer id;
    private String sigla;
    private String descricao;

    private NivelCrossfitEnum(Integer id, String sigla, String descricao) {
        this.id = id;
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static NivelCrossfitEnum valueOf(final Integer id) {
        return (NivelCrossfitEnum) ColecaoUtils.find(Arrays.asList(NivelCrossfitEnum.values()), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return ((NivelCrossfitEnum) o).getId().equals(id);
            }
        });
    }

    public String getNome(){
        return name();
    }
}
