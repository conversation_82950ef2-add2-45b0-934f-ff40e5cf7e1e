package br.com.pacto.bean.musculo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Grupo músculo")
public class MusculoGrupoResponseTO {

    @ApiModelProperty(value = "ID do grupo", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do grupo", example = "Grupo músculo 1")
    private String nome;

    public MusculoGrupoResponseTO(){

    }

    public MusculoGrupoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
