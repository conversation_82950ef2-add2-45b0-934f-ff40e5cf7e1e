/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.musculo;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class GrupoMuscular implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @OneToMany(targetEntity = MusculoGrupoMuscular.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "grupoMuscular")
    private List<MusculoGrupoMuscular> musculos = new ArrayList<MusculoGrupoMuscular>();
    @OneToMany(targetEntity = AtividadeGrupoMuscular.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE},orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "grupoMuscular")
    private List<AtividadeGrupoMuscular> atividades = new ArrayList<AtividadeGrupoMuscular>();
    @ElementCollection(fetch = FetchType.EAGER)
    private Set<PerimetriaEnum> perimetros;
    @ElementCollection(fetch = FetchType.EAGER)
    private Set<PontosMuscularEnum> pontosMuscular;

    public List<AtividadeGrupoMuscular> getAtividades() {
        return atividades;
    }
    
    public String getAnchor(){
        try {
            return Uteis.retirarAcentuacaoRegex(nome).replaceAll(" ", "_");
        } catch (Exception e) {
            return "";
        }
        
    }

    public void setAtividades(List<AtividadeGrupoMuscular> atividades) {
        this.atividades = atividades;
    }
    
    public GrupoMuscular() {
    }

    public GrupoMuscular(final String nome) {
        this.nome = nome;
    }
    public GrupoMuscular(final Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<MusculoGrupoMuscular> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<MusculoGrupoMuscular> musculos) {
        this.musculos = musculos;
    }
    
    @Override
    public int hashCode() {
        return getCodigo().hashCode();
    }
    @Override
    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (obj == this)
            return true;
        if (!(obj instanceof GrupoMuscular))
            return false;

        GrupoMuscular rhs = (GrupoMuscular) obj;
        return rhs.getCodigo().equals(this.getCodigo());
    }

    public Set<PerimetriaEnum> getPerimetros() {
        return perimetros;
    }

    public void setPerimetros(Set<PerimetriaEnum> perimetros) {
        this.perimetros = perimetros;
    }

    public Set<PontosMuscularEnum> getPontosMuscular() {
        return pontosMuscular;
    }

    public void setPontosMuscular(Set<PontosMuscularEnum> pontosMuscular) {
        this.pontosMuscular = pontosMuscular;
    }
}
