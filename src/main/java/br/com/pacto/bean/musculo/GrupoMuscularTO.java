package br.com.pacto.bean.musculo;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 07/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GrupoMuscularTO {

    private Integer id;
    private String nome;
    private List<Integer> atividadeIds;
    private List<PerimetriaEnum> perimetros;
    private List<Integer> musculoIds;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<PerimetriaEnum> getPerimetros() {
        return perimetros;
    }

    public void setPerimetros(List<PerimetriaEnum> perimetros) {
        this.perimetros = perimetros;
    }

    public List<Integer> getMusculoIds() {
        return musculoIds;
    }

    public void setMusculoIds(List<Integer> musculoIds) {
        this.musculoIds = musculoIds;
    }
}
