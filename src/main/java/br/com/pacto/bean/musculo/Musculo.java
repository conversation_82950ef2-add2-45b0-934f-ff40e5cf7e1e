/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.musculo;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.bean.escopo.Sujeito;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))

public class Musculo extends Sujeito {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @OneToMany(targetEntity = MusculoGrupoMuscular.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE},orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "musculo")
    private List<MusculoGrupoMuscular> grupos = new ArrayList<MusculoGrupoMuscular>();
    @OneToMany(targetEntity = AtividadeMusculo.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE},orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "musculo")
    private List<AtividadeMusculo> atividades = new ArrayList<AtividadeMusculo>();

    public Musculo(Integer codigo) {
        this.codigo = codigo;
    }
    
    public Musculo() {
    }

    public List<AtividadeMusculo> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeMusculo> atividades) {
        this.atividades = atividades;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<MusculoGrupoMuscular> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<MusculoGrupoMuscular> grupos) {
        this.grupos = grupos;
    }

    @Override
    public boolean evaluate(Object o) {
        Musculo m = (Musculo) o;
        return m.getCodigo() != null && this.getCodigo() != null && m.getCodigo().intValue() == this.getCodigo().intValue();
    }
    
//    public static void main(String...a){
//        Musculo m1= new Musculo();
//        m1.setCodigo(1);
//        m1.setNome("m1");
//        Musculo m2= new Musculo();
//        m2.setCodigo(2);
//        m2.setNome("m2");
//
//        List<Musculo> lista = new ArrayList();
//        lista.add(m1);
//        lista.add(m2);
//
//        System.out.println(((Musculo)ColecaoUtils.find(lista, m1)).getNome());
//    }
}
