/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeAlternativaDao;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;

@Repository
public class AtividadeAlternativaDaoImpl extends DaoGenericoImpl<AtividadeAlternativa, Integer> implements
        AtividadeAlternativaDao {

    @Override
    public boolean existsByAtividadeAndAtividadeAlternativa(String ctx, Integer atividadeId, Integer atividadeAlternativaId) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT COUNT(*) AS quantidade ")
                    .append("FROM atividadealternativa ")
                    .append("WHERE atividade_codigo = ").append(atividadeId)
                    .append(" AND atividadealternativa = ").append(atividadeAlternativaId);

            try (ResultSet rs = createStatement(ctx, sb.toString())) {
                if (rs.next()) {
                    return rs.getInt("quantidade") > 0;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao verificar existência da relação de atividade e alternativa", e);
        }
        return false;
    }


}
