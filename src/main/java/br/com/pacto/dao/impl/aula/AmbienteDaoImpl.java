/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.controller.json.ambiente.*;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.AmbienteDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AmbienteDaoImpl extends DaoGenericoImpl<Ambiente, Integer> implements AmbienteDao{

    private static final int MAXIMO_AMBIENTE_CONSULTAR = 30;

    @Autowired
    private final ConexaoZWService conexaoZWService;

    @Autowired
    public AmbienteDaoImpl(ConexaoZWService conexaoZWService) {
        this.conexaoZWService = conexaoZWService;
    }

    @Override
    public List<Ambiente> consultarPorNome(String ctx, FiltroAmbienteJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAXIMO_AMBIENTE_CONSULTAR;
        int indiceInicial=0;
        try {
            Map<String, Object > param = new HashMap<>();
            getCurrentSession(ctx).clear();

            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAXIMO_AMBIENTE_CONSULTAR : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            }

            StringBuilder hql = new StringBuilder("SELECT obj FROM Ambiente obj ");
            StringBuilder where = new StringBuilder();

            if(filtros.getParametro() != null && !filtros.getParametro().trim().isEmpty()) {
                where.append(" WHERE upper(obj.nome) like '%" + filtros.getParametro().toUpperCase() + "%'");
                hql.append(where);
//                params.put("nome", "'" + filtroNome + "%'");
            }
            hql.append(paginadorDTO.getSQLOrderByUse());

            List<Ambiente> lista = null;
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<TipoAmbienteResponseTO> obterTodosTiposAmbiente(String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<TipoAmbienteResponseTO> tipoAmbienteResponseTOS;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT * FROM TIPOAMBIENTE", conZW)) {
                tipoAmbienteResponseTOS = new ArrayList<>();
                while (rs.next()) {
                    tipoAmbienteResponseTOS.add(montarDadosTipoAmbiente(rs));
                }
            }
            return tipoAmbienteResponseTOS;
        }
    }

    public TipoAmbienteResponseTO montarDadosTipoAmbiente(ResultSet rs) throws SQLException {
        TipoAmbienteResponseTO tipoAmbienteResponseTO = new TipoAmbienteResponseTO();
        tipoAmbienteResponseTO.setId(rs.getInt("codigo"));
        tipoAmbienteResponseTO.setNome(rs.getString("descricao"));
        return tipoAmbienteResponseTO;
    }

    public List<AmbienteResponseTO> obterTodosAtivo(String ctx, FiltroAmbienteJSON filtros) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ambiente \n");
        sql.append("WHERE situacao = 1 \n");
        if (!UteisValidacao.emptyString(filtros.getParametro())) {
            sql.append("AND descricao ILIKE '%").append(filtros.getParametro()).append("%' \n");
        }
        sql.append("ORDER BY descricao");
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<AmbienteResponseTO> ambienteResponseTOS;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                ambienteResponseTOS = new ArrayList<>();
                while (rs.next()) {
                    ambienteResponseTOS.add(montarDadosAmbiente(rs));
                }
            }
            return ambienteResponseTOS;
        }
    }

    public List<NivelTurmaResponseTO> obterNiveisTurmaAmbiente(String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<NivelTurmaResponseTO> nivelTurmaResponseTOS;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT * FROM NIVELTURMA", conZW)) {
                nivelTurmaResponseTOS = new ArrayList<>();
                while (rs.next()) {
                    nivelTurmaResponseTOS.add(montarDadosNivelTurmaAmbiente(rs));
                }
            }
            return nivelTurmaResponseTOS;
        }
    }

    public List<ColetorResponseTO> obterColetoresAmbiente(String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<ColetorResponseTO> coletorResponseTOS;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT * FROM COLETOR", conZW)) {
                coletorResponseTOS = new ArrayList<>();
                while (rs.next()) {
                    coletorResponseTOS.add(montarDadosColetorAmbiente(rs));
                }
            }
            return coletorResponseTOS;
        }
    }

    public AmbienteResponseTO montarDadosAmbiente(ResultSet rs) throws SQLException {
        AmbienteResponseTO ambienteResponseTO = new AmbienteResponseTO();
        ambienteResponseTO.setId(rs.getInt("codigo"));
        ambienteResponseTO.setNome(rs.getString("descricao"));
        ambienteResponseTO.setCapacidade(rs.getInt("capacidade"));
        return ambienteResponseTO;
    }

    public NivelTurmaResponseTO montarDadosNivelTurmaAmbiente(ResultSet rs) throws SQLException {
        NivelTurmaResponseTO nivelTurmaResponseTO = new NivelTurmaResponseTO();
        nivelTurmaResponseTO.setId(rs.getInt("codigo"));
        nivelTurmaResponseTO.setNome(rs.getString("descricao"));
        nivelTurmaResponseTO.setCodigomgb(rs.getString("codigoMgb"));
        return nivelTurmaResponseTO;
    }

    public ColetorResponseTO montarDadosColetorAmbiente(ResultSet rs) throws SQLException {
        ColetorResponseTO coletorResponseTO = new ColetorResponseTO();
        coletorResponseTO.setId(rs.getInt("codigo"));
        coletorResponseTO.setNome(rs.getString("descricao"));
        return coletorResponseTO;
    }
}
