/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class AtividadeDaoImpl extends DaoGenericoImpl<Atividade, Integer> implements
        AtividadeDao {

    private static final int MAXIMO_ATIVIDADES_LISTAR = 50;

    @Override
    public List<Atividade> listarAtividades(String ctx, FiltroAtividadeJSON filtros, String tipo,
                                            PaginadorDTO paginadorDTO, boolean crossfit, boolean buscarTodas,
                                            List<AtividadeGrupoMuscular> atvGrupoMusc, Integer empresaId) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_ATIVIDADES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_ATIVIDADES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<>();
        hql.append("SELECT obj FROM Atividade obj ");

        if (filtros != null && filtros.getEmpresasIds().isEmpty()) {
            hql.append(" left join fetch obj.empresasHabilitadas ae ");
        }

        where.append("where 1=1 ");
        where.append(" AND obj.nome NOT LIKE ' %'");

        if (filtros != null && !filtros.getEmpresasIds().isEmpty()) {
            where.append(" and obj.todasEmpresas = :buscarTodas");
            param.put("buscarTodas", false);
        }

        if (filtros != null && filtros.getAtividades() != null && !filtros.getAtividades().isEmpty()) {
            if (filtros.getAtividades().contains("IA") && filtros.getAtividades().contains("CONVENCIONAL")) {
                where.append(" AND ((obj.idIA2 is not null OR obj.idIA is not null) OR (obj.idIA is null AND obj.idIA2 is null)) ");
            } else if (filtros.getAtividades().contains("IA")) {
                where.append(" AND (obj.idIA2 is not null OR obj.idIA is not null) ");
            } else if (filtros.getAtividades().contains("CONVENCIONAL")) {
                where.append(" AND obj.idIA is null AND obj.idIA2 is null ");
            }
        }

        where.append(" and obj.crossfit = :crossfit");
        param.put("crossfit", crossfit);

        if (filtros != null && (filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            where.append(" AND upper(unaccent(obj.nome)) like CONCAT('%',:nome,'%')");
            param.put("nome", Uteis.retirarAcentuacaoRegex(filtros.getParametro().toUpperCase()));
        }

        if (filtros != null && (filtros.getTipo()) != null && !filtros.getTipo().isEmpty()) {
            where.append(" AND obj.tipo in (");
            String tipoId = filtros.getTipo().stream()
                    .map(TipoAtividadeEnum::getId)
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(tipoId).append(")");
        }

        if (filtros != null && filtros.getAtivo() != null) {
            where.append(" AND obj.ativo = :ativo");
            param.put("ativo", Boolean.parseBoolean(filtros.getAtivo()));
        }

        if (!UteisValidacao.emptyList(atvGrupoMusc)) {
            where.append(" AND obj.codigo in (");
            String selecionados = atvGrupoMusc.stream()
                    .map(atividadeGrupoMuscular -> atividadeGrupoMuscular.getAtividade().getCodigo())
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append(")");
        }

        List<Integer> empresasIds = (filtros != null) ? filtros.getEmpresasIds() : null;
        if (filtros != null && !filtros.getEmpresasIds().isEmpty()) {
            where.append(" AND obj.codigo IN (SELECT atividade.codigo from AtividadeEmpresa atve WHERE atve.empresa.codigo in (");
            String selecionados = empresasIds.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append("))");
        } else if (filtros != null) {
            EmpresaService es = UtilContext.getBean(EmpresaService.class);
            Empresa e = es.obterPorIdZW(ctx, empresaId);
            where.append(" AND (ae.empresa.codigo IS NULL OR ae.empresa.codigo = ").append(e.getCodigo()).append(")");
        }

        List<Integer> aparelhosIds = (filtros != null) ? filtros.getAparelhosIds() : null;
        if (!UteisValidacao.emptyList(aparelhosIds)) {
            where.append(" AND obj.codigo IN (SELECT sub.atividade.codigo FROM AtividadeAparelho sub WHERE sub.aparelho.codigo IN (");
            String selecionados = aparelhosIds.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append("))");
        }

        hql.append(where.toString());

        List<Atividade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamHqlFullFromSizeList(ctx, hql.toString(), param).longValue());
            }
            if (paginadorDTO != null && paginadorDTO.getSort().contains("codigo")) {
                paginadorDTO.setSort(paginadorDTO.getSort().replace("codigo", "obj.codigo"));
            }
            if (paginadorDTO != null) {
                hql.append(paginadorDTO.getSQLOrderByUse());
            }
            if (filtros == null && paginadorDTO == null) {
                maxResults = 0;
                indiceInicial = 0;
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
        if (paginadorDTO != null) {
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }
        return lista;
    }

    @Override
    public List<Atividade> listarAtividadeCrossfit(String ctx, FiltroAtividadeCrossfitJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAXIMO_ATIVIDADES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_ATIVIDADES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        HashMap<String, Object> params = new HashMap<>();
        hql.append("SELECT obj FROM Atividade obj ");
        where.append("WHERE obj.crossfit = true ");
        if (filtros.getNome()) {
            where.append("and upper(obj.nome) like CONCAT('%',:nome,'%')");
            params.put("nome", filtros.getParametro().trim().toUpperCase());
        }
        hql.append(where);
        hql.append(paginadorDTO.getSQLOrderBy());
        List<Atividade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, params).longValue());
            }
            lista = findByParam(ctx, hql.toString(), params, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    @Override
    public Atividade obterPorId(String ctx, Integer codigoAtividade) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findById(ctx, codigoAtividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Atividade> listarAtividadeSelect(String ctx, FiltroAtividadeCrossfitJSON filtros) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.crossfit = :crossfit ");
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            hql.append("AND upper(obj.nome) like :nome ");
        }
        hql.append("ORDER BY nome ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("crossfit", filtros.isCrossfit());
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            params.put("nome", "%" + filtros.getParametro().toUpperCase().trim() + "%");
        }
        if (filtros.getIndex() != null && !UteisValidacao.emptyNumber(filtros.getMaxResults())) {
            listaReturn = findByParam(ctx, hql.toString(), params, filtros.getMaxResults(), filtros.getIndex());
        } else {
            listaReturn = findByParam(ctx, hql.toString(), params);
        }

        return listaReturn;
    }

    @Override
    public Atividade obterPorNomeExato(String ctx, String nome) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE trim(upper(obj.nome)) = trim(:nome) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome", nome.toUpperCase());
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade obterPorIDIA(String ctx, String idIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.idIA = :idIA ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("idIA", idIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade obterPorIdIAVersaoDois(String ctx, int idIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.idIA2 = :idIA ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("idIA", idIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade buscarPorNomeOriginalIA(String ctx, String nomeOriginalIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE lower(obj.nomeOriginalIA) = lower(:nome_ia) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome_ia", nomeOriginalIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade buscarPorNomeAtividade(String ctx, String nomeAtividade) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE lower(obj.nome) = lower(:nome_ia) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome_ia", nomeAtividade);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }


    @Override
    public Atividade obterAlgunsCamposPorCodigo(String ctx, Integer atividadeCodigo) throws Exception {
        String sql = "SELECT codigo, nome FROM Atividade WHERE codigo = " + atividadeCodigo;

        Atividade atividade;
        try (ResultSet rs = createStatement(ctx, sql)) {
            atividade = new Atividade();
            if (rs.next()) {
                atividade.setCodigo(rs.getInt("codigo"));
                atividade.setNome(rs.getString("nome"));
            }
        }

        return atividade;
    }

    @Override
    public boolean existeAtividadeIA(String ctx) {
        String sql = "SELECT EXISTS (SELECT * FROM atividade a WHERE a.idia2 IS NOT NULL)";
        try (ResultSet rs = createStatement(ctx, sql)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao verificar existência de Atividade IA", e);
        }
        return false;
    }

    @Override
    public List<Atividade> listarAtividadesComIdia2NaoNulo(String ctx) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Atividade obj WHERE obj.idIA2 IS NOT NULL";
            return findByParam(ctx, hql, new HashMap<>());
        } catch (Exception e) {
            throw new ServiceException("Erro ao buscar atividades com idIA2 não nulo.", e);
        }
    }

    @Override
    public void excluirRelacionamentosPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException {
        if (atividadeCodigos == null || atividadeCodigos.isEmpty()) {
            return;
        }
        try {

            String hqlDeleteGrupoMuscular = "DELETE FROM AtividadeGrupoMuscular WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteGrupoMuscular).setParameterList("codigos", atividadeCodigos).executeUpdate();

            String hqlDeleteAnimacao = "DELETE FROM AtividadeAnimacao WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteAnimacao).setParameterList("codigos", atividadeCodigos).executeUpdate();

            String hqlDeleteAlternativa = "DELETE FROM AtividadeAlternativa WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteAlternativa).setParameterList("codigos", atividadeCodigos).executeUpdate();

        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir relacionamentos de atividades.", e);
        }
    }

    @Override
    public void excluirAtividadesPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException {
        if (atividadeCodigos == null || atividadeCodigos.isEmpty()) {
            return;
        }
        try {
            String hql = "DELETE FROM Atividade WHERE codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hql).setParameterList("codigos", atividadeCodigos).executeUpdate();
        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir atividades.", e);
        }
    }
}
