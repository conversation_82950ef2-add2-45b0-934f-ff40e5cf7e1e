package br.com.pacto.dao.impl.base;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.sql.PreparedStatement;
import java.util.*;

import javax.persistence.*;
import javax.persistence.metamodel.EntityType;
import javax.persistence.metamodel.Metamodel;
import javax.persistence.metamodel.SingularAttribute;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UtilContext;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.persister.collection.AbstractCollectionPersister;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.util.MensagemUtils;
import br.com.pacto.util.impl.UtilReflection;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.hibernate.Session;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.impl.SessionFactoryImpl;
import org.springframework.beans.factory.annotation.Autowired;

public class DaoGenericoImpl<T, ID extends Serializable> implements
        DaoGenerico<T, ID> {

//	@PersistenceContext
//	private EntityManager entityManager;
    private final Class<T> objClass;
    @Autowired
    private EntityManagerService entityService;
    @Autowired
    private MensagemUtils mensagensUtil;
    @Autowired
    private Validacao validacao;

    public EntityManager getEntityManager(final String ctx) throws Exception {
        return entityService.getEntityManager(ctx);
    }

    @SuppressWarnings("unchecked")
    public DaoGenericoImpl() {
        this.objClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }

    private void logar(final String ctx, final Map<String, Object> params) {
        if (params != null) {
            Uteis.logar(null, String.format("Parâmetros: %s %s", new Object[]{ctx, params.toString()}));
        }
    }

    private void logar(final String ctx, final StringBuilder sb) {
        if (sb != null) {
            Uteis.logar(null, String.format("Parâmetros: %s %s", new Object[]{ctx, sb.toString()}));
        }
    }

    private void logar(final String ctx, final String[] attrs, final Object[] values) {
        try {
            boolean debug = Aplicacao.getProp(Aplicacao.logarDao).equals("true");
            if (debug) {
                StringBuilder aux = new StringBuilder("Parâmetros: ").append(ctx).append(" ");
                if (values != null && attrs != null) {
                    for (int i = 0; i < attrs.length; i++) {
                        aux.append(attrs[i]).append(" = ").append(values[i]).append(" ");
                    }
                    if (values.length > attrs.length) {
                        aux.append(" - ").append(values[values.length - 1]);
                    }
                    Uteis.logar(debug,null, aux.toString());
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<T> findAll(final String ctx) throws Exception {
        try {
            EntityManager em = getEntityManager(ctx);
            String queryS = "SELECT obj FROM " + objClass.getSimpleName() + " obj ";
            Query query = em.createQuery(queryS);
            return query.getResultList();
        } finally {
        }
    }

    @Override
    public T findById(final String ctx, ID id) throws Exception {
        return (T) getEntityManager(ctx).find(objClass, id);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<T> findByParam(final String ctx, String queryS, Map<String, Object> params, Integer... index)
            throws Exception {
        Query q = getEntityManager(ctx).createQuery(queryS);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }
        if (index.length > 0 && index[0] != 0)
            q.setMaxResults(index[0]);
        if (index.length > 1 && index[1] != 0)
            q.setFirstResult(index[1]);
        return q.getResultList();
    }

    @Override
    public List<T> findByParam(final String ctx, final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select obj  from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return query.getResultList();
    }

    @Override
    public List<T> findByParam(final String ctx, final StringBuilder whereClause, Map<String, Object> params,
            int max, int index) throws Exception {
        StringBuffer s = new StringBuffer("select obj  from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString()).
                setFirstResult(index).setMaxResults(max);
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return query.getResultList();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<T> findByParam(final String ctx, String queryS, Map<String, Object> params, int max, int index) throws Exception {
        Query q = getEntityManager(ctx).createQuery(queryS);

        if (max != 0)
            q.setMaxResults(max);
        if (index != 0)
            q.setFirstResult(index);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }

        return q.getResultList();
    }

    @SuppressWarnings("unchecked")
    @Override
    public T findObjectByParam(final String ctx, String queryS, Map<String, Object> params)
            throws Exception {
        Query q = getEntityManager(ctx).createQuery(queryS);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }

        try {
            return (T) q.getSingleResult();
        } catch (NoResultException nre) {
            return null;
        } catch (NonUniqueResultException nre) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "DUPLICADO: " + objClass.getSimpleName() + " - chave: "
                    + ctx + " - atributos: " + params.toString(), nre);
            List resultList = q.getResultList();
            return (T) resultList.get(0);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public T findFirstObjectByParam(final String ctx, String queryS, Map<String, Object> params)
            throws Exception {
        Query q = getEntityManager(ctx).createQuery(queryS);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }

        try {
            return (T) q.setMaxResults(1).getSingleResult();
        } catch (NoResultException nre) {
            return null;
        } catch (NonUniqueResultException nre) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "DUPLICADO: " + objClass.getSimpleName() + " - chave: "
                    + ctx + " - atributos: " + params.toString(), nre);
            List resultList = q.getResultList();
            return (T) resultList.get(0);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public T findObjectByAttribute(final String ctx, final String attribute, Object value)
            throws Exception {
        Query q = getQueryFindObjectByAttribute(ctx, new String[]{attribute}, new Object[]{value}, null, 0);
        try {
            return (T) q.getSingleResult();
        } catch (NoResultException nre) {
            return null;
        } catch (NonUniqueResultException nre) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "DUPLICADO: " + objClass.getSimpleName() + " - chave: "
                    + ctx + " - atributo: " + attribute + " - param: " + value.toString(), nre);
            List resultList = q.getResultList();
            return (T) resultList.get(0);
        }
    }

    @Override
    public List<T> findListByAttributes(final String ctx, final String[] atributos, Object[] valores, final String orderBY, final int maxResults, final Integer... index)
            throws Exception {
        Query q = getQueryFindObjectByAttribute(ctx, atributos, valores, orderBY, maxResults, index);
        try {
            return (List<T>) q.getResultList();
        } catch (NoResultException nre) {
            return null;
        }
    }

    public List<ProgramaTreino> findListByAttributesProgramTreino(final String ctx, final String[] atributos, Object[] valores, final String orderBY, final int maxResults, final Integer... index)
            throws Exception {
        Query q = getQueryFindObjectByAttribute(ctx, atributos, valores, orderBY, maxResults, index);
        try {
            List<ProgramaTreino> list = q.getResultList();

            for (ProgramaTreino programaTreino: list) {
                List<ProgramaTreinoFicha> listProgramasTreinoFicha = programaTreino.getProgramaFichas();
                Hibernate.initialize(listProgramasTreinoFicha);

                for (ProgramaTreinoFicha programaTreinoFicha: new ArrayList<>(listProgramasTreinoFicha)) {
                    Ficha ficha = programaTreinoFicha.getFicha();

                    if(ficha == null){
                        listProgramasTreinoFicha.remove(programaTreinoFicha);
                        continue;
                    }
                    Hibernate.initialize(ficha);

                    for (ProgramaTreinoFicha programaTreinoFichaInterno: ficha.getProgramas()) {
                        Hibernate.initialize(programaTreinoFichaInterno);
                    }

                    for (AtividadeFicha atividadeFicha: ficha.getAtividades()) {
                        Hibernate.initialize(atividadeFicha);
                        Hibernate.initialize(atividadeFicha.getMetodoExecucao());
                        List<Serie> series = atividadeFicha.getSeries();
                        for (Serie serie : series) {
                            Hibernate.initialize(serie);
                        }
                    }
                }
            }

            return list;
        } catch (NoResultException nre) {
            return null;
        }
    }

    private StringBuilder getWhereClause(final String[] atributos) {
        StringBuilder query = new StringBuilder();
        if (atributos == null) {
            return query;
        }
        if (atributos.length > 0) {
            query.append(" where ");
        }
        /*
         * 
         * "SELECT obj FROM ClienteSintetico obj WHERE LOWER(obj.nome) LIKE '"
         + filtro.toLowerCase() + "%'" + (empresaZW != null ? (" and empresa = " + empresaZW) : ""), new HashMap<String, Object>());
         **/
        for (int i = 0; i < atributos.length; i++) {
            String atributo = atributos[i];
            if (atributo.contains("like")) {
                atributo = atributo.replace(".like", "");
                query.append("LOWER(obj.").append(atributo).append(") LIKE :").append(atributo);
            } else if (atributo.contains("between")) {
                final String attr = atributo.replaceAll("\\.", "").replaceAll("between", "").replaceAll(" ", "");
                final String attr1 = attr + "1";
                final String attr2 = attr + "2";
                query.append(atributo).append(" :").append(attr1).append(" and :").append(attr2);
            } else if (atributo.contains("<>")) {
                query.append(atributo).append(" :").append(atributo.replaceAll("<>", "").replaceAll("\\.", "").trim());
            } else if (atributo.contains(">")) {
                query.append(atributo).append(" :").append(atributo.replaceAll(">", "").replaceAll("\\.", "").trim());
            } else if (atributo.contains("<")) {
                query.append(atributo).append(" :").append(atributo.replaceAll("<", "").replaceAll("\\.", "").trim());
            } else {
                query.append(atributo).append(" = :").append(atributo.replaceAll("\\.", ""));
            }
            if (i + 1 < atributos.length) {
                query.append(" and ");
            }
        }
        return query;
    }

    private void setWhereValues(String[] atributos, Object[] valores, Query q) {
        if (atributos == null || valores == null) {
            return;
        }
        List<String> l = new ArrayList(Arrays.asList(atributos));
        List<Object> lv = new ArrayList(Arrays.asList(valores));
        for (int i = 0; i < atributos.length; i++) {
            final String atributo = atributos[i];
            final String attr = atributo.replaceAll(".like", "").replaceAll("\\.", "").replaceAll("between", "").replaceAll(" ", "");
            if (atributo.contains("between")) {
                lv.add(i, null);
                final String attr1 = attr + "1";
                final String attr2 = attr + "2";

                l.add(attr1);
                l.add(attr2);
            }
        }
        Object[] attrs = l.toArray();
        Object[] val = lv.toArray();
        for (int i = 0; i < attrs.length; i++) {
            String atributo = (String) attrs[i];
            if (atributo.contains("between")) {
                continue;
            }
            atributo = atributo.replace(".like", "").replaceAll("\\.", "").replaceAll("<", "").replaceAll(">", "").trim();
            Object valor = val[i];


            if (valor != null) {
                if (valor.getClass() == Date.class) {
                    q.setParameter(atributo,
                            (Date) valor, TemporalType.DATE);
                } else if (attrs[i].toString().contains(".like")) {
                    q.setParameter(atributo, valor + "%");
                } else {
                    q.setParameter(atributo, valor);
                }
            } else {
                q.setParameter(atributo, valor);
            }
        }
    }

    private Query getQueryFindObjectByAttribute(final String ctx, final String[] atributos, Object[] valores,
            final String orderBY, final int maxResults, final Integer... index) throws Exception {
        return findObjectByAttributeWithQuery(ctx, "select obj from " + this.objClass.getSimpleName() + " obj ", atributos, valores, orderBY, maxResults, index);
    }

    @Override
    public List<T> findObjectsByAttributesSimple(final String ctx, final String[] colunas,
            final String[] atributos, Object[] valores,
            final String orderBY, final int maxResults, Integer... index) throws Exception {
        StringBuilder colunasQuery = new StringBuilder("new " + this.objClass.getName() + " (");
        for (int i = 0; i < colunas.length; i++) {
            colunasQuery.append(",").append(colunas[i]);
        }
        colunasQuery.append(")");
        String query = "select " + colunasQuery.toString().replaceFirst(",", "") + " FROM " + this.objClass.getSimpleName() + " ";
        return findObjectByAttributeWithQuery(ctx, query, atributos, valores, orderBY, maxResults, index).getResultList();
    }

    private Query findObjectByAttributeWithQuery(final String ctx, String queryS, final String[] atributos, Object[] valores,
            final String orderBY, final int maxResults, Integer... index) throws Exception {

        StringBuilder query = new StringBuilder(queryS);

        if (atributos != null) {
            query.append(getWhereClause(atributos));
        }

        if (orderBY != null) {
            query.append(" order by ").append(orderBY);
        }
        Query q = getEntityManager(ctx).createQuery(query.toString());
        if (maxResults != 0) {
            q.setMaxResults(maxResults);
        }
        if (index.length > 1 && index[1] != 0 ) {
            q.setFirstResult(index[1]);
        }
        if (atributos != null && valores != null) {
            setWhereValues(atributos, valores, q);
        }

        logar(ctx, atributos, valores);

        return q;
    }

    @SuppressWarnings("unchecked")
    @Override
    public T findObjectByAttributes(final String ctx, final String[] attributes, Object[] values, final String orderBY)
            throws Exception {
        Query q = getQueryFindObjectByAttribute(ctx, attributes, values, orderBY, 0);
        try {
            return (T) q.getSingleResult();
        } catch (NoResultException nre) {
            return null;
        } catch (NonUniqueResultException nre) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, "DUPLICADO: " + nre.getMessage(), nre);
            List resultList = q.getResultList();
            return (T) resultList.get(0);
        }
    }

    @Override
    public String health(final String ctx) throws Exception {
        StringBuilder s = new StringBuilder("select 'ok' as health from ").append(this.objClass.getSimpleName()).append(" obj ");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        logar(ctx, s);
        return (String) query.getSingleResult();
    }

    @Override
    public Number count(final String ctx, final String atributoCount, final String[] atributos, Object[] valores) throws Exception {
        StringBuilder s = new StringBuilder("select COUNT(").append(atributoCount).append(") from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(getWhereClause(atributos));
        Query query = getEntityManager(ctx).createQuery(s.toString());
        setWhereValues(atributos, valores, query);
        logar(ctx, atributos, valores);
        return (Number) query.getSingleResult();
    }

    @Override
    public Number countWithParam(final String ctx, final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select COUNT(").append(atributoCount).append(") from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return (Number) query.getSingleResult();
    }

    @Override
    public Number countWithParamHqlFull(final String ctx, final String hql, Map<String, Object> params) throws Exception {
        Query query = getEntityManager(ctx).createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return (Number) query.getSingleResult();
    }

    @Override
    public Number countWithParamHqlFullFromSizeList(final String ctx, final String hql, Map<String, Object> params) throws Exception {
        Query query = getEntityManager(ctx).createQuery(hql.toString());
        if (params != null) {
            for (String p : params.keySet()) {
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        List resultList = query.getResultList();
        return resultList.size();
    }

    @Override
    public Number sumWithParam(final String ctx, final String atributoCount, final String whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select SUM(").append(atributoCount).append(") from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return (Number) query.getSingleResult();
    }

    @Override
    public Number sum(final String ctx, final String atributoSum, final String[] atributos, Object[] valores) throws Exception {
        StringBuilder s = new StringBuilder("select SUM(").append(atributoSum).append(") from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(getWhereClause(atributos));
        Query query = getEntityManager(ctx).createQuery(s.toString());
        setWhereValues(atributos, valores, query);
        logar(ctx, atributos, valores);
        return (Number) query.getSingleResult();
    }

    @Override
    public List listOfObjects(final String ctx, final String sql) throws Exception {
        Query query = getEntityManager(ctx).createNativeQuery(sql);
        return query.getResultList();
    }

    @SuppressWarnings("unchecked")
    @Override
    public Class<T> getObjectClass() {
        return this.objClass;
    }

    @Override
    public T insert(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            em.persist(object);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
        return object;
    }

    public T insertOrMerge(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if (!em.getTransaction().isActive()) {
            em.getTransaction().begin();
        }
        try {
            object = em.merge(object);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
        return object;
    }


    @Override
    public T insertNoFlush(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        try {
            em.persist(object);
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
        return object;
    }

    @Override
    public T updateNoFlush(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        try {
            em.merge(object);
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
        return object;
    }

    @Override
    public T insertNoClear(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            em.persist(object);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        } finally {
        }
        return object;
    }

    @Override
    public T update(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        try {
            if(!em.getTransaction().isActive()){
                em.getTransaction().begin();
            }
            object = (T) em.merge(object);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
        return object;
    }

    @Override
    public T updateNoClear(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            object = (T) em.merge(object);
            em.getTransaction().commit();
        } catch (EntityExistsException e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        } finally {
        }
        return object;
    }

    @Override
    public void delete(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            em.clear();
            object = em.merge(object);
            em.remove(object);
            em.flush();
            em.getTransaction().commit();
        } catch (PersistenceException pe) {
            Uteis.logar(null, "DaoGenericoImpl.delete PersistenceException => " + pe.getMessage());
            pe.printStackTrace();
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemErroExclusao((ConstraintViolationException) pe.getCause()));
        } catch (Exception e1) {
            e1.printStackTrace();
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        } finally {
            em.clear();
        }
    }

    @Override
    public void deleteV2(final String ctx, T object) throws Exception {
        /*
        * Fluxo criado para contornar problema que estava ocorrendo no método acima "public void delete(final String ctx, T object)";
        * No método acima ao chamar o "em.merge(object)" o Hibernate retornava que encontrou mais de uma linha no banco de dados com o mesmo identificador sendo que não existia essa duplicação;
        * Após testes o fluxo a seguir funcionou conforme o esperado;
        */
        EntityManager em = getEntityManager(ctx);
        EntityTransaction transaction = em.getTransaction();
        if (!transaction.isActive()) {
            transaction.begin();
        }
        try {
            object = (T) em.find(object.getClass(), extractPrimaryKeyValue(object, em));
            em.remove(object);
            transaction.commit();
        } catch (HibernateException he) {
            Uteis.logar(null, "DaoGenericoImpl.delete HibernateException => " + he.getMessage());
            he.printStackTrace();
            if (transaction.isActive()) {
                transaction.rollback();
            }
            throw he;
        } catch (Exception e) {
            e.printStackTrace();
            if (transaction.isActive()) {
                transaction.rollback();
            }
            throw e;
        } finally {
            em.clear();
        }
    }

    private Object extractPrimaryKeyValue(T object, EntityManager em) {
        // responsável por extrair o valor da chave primária do objeto T de forma mais genérica, independentemente do nome da propriedade ou de como ela é representada
        Metamodel metamodel = em.getMetamodel();
        EntityType<T> entityType = (EntityType<T>) metamodel.entity(object.getClass());
        SingularAttribute<T, ?> primaryKey = (SingularAttribute<T, ?>) entityType.getId(entityType.getIdType().getJavaType());

        return em.getEntityManagerFactory().getPersistenceUnitUtil().getIdentifier(object);
    }


    @Override
    public void delete(final String contexto, final ID id) throws Exception {
        T byId = findById(contexto, id);
        if (byId != null) {
            delete(contexto, byId);
        }
    }

    @Override
    public void deleteNoClear(final String ctx, T object) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            object = em.merge(object);
            em.remove(object);
            em.flush();
            em.getTransaction().commit();
        } catch(PersistenceException pe){
            Uteis.logar(null, "DaoGenericoImpl.delete PersistenceException => " + pe.getMessage());
            pe.printStackTrace();
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw new Exception(mensagensUtil.tratarMensagemErroExclusao((ConstraintViolationException)pe.getCause()));
        }
    }

    @Override
    public void refresh(final String ctx, T object) throws Exception {
        getEntityManager(ctx).refresh(getEntityManager(ctx).merge(object));
    }

    @Override
    public void refresh(final String ctx, List<T> object) throws Exception {
        for (T obj : object) {
            getEntityManager(ctx).refresh(getEntityManager(ctx).merge(obj));
        }
    }

    @Override
    public T insertOrGetObjectForName(final String ctx, final String nome) throws Exception {
        Map<String, Object> p = new HashMap();
        p.put("nome", nome);
        T o = findObjectByParam(ctx, String.format("select obj from %s obj where nome = :nome",
                new Object[]{getObjectClass().getSimpleName()}), p);
        if (o == null) {
            o = getObjectClass().newInstance();
            UtilReflection.setValor(o, nome, "nome");
            return insert(ctx, o);
        } else {
            return o;
        }
    }

    @Override
    public T insertOrGetObjectForName(final String ctx, T object, final String attributeName) throws Exception {
        Map<String, Object> p = new HashMap();
        p.put(attributeName, UtilReflection.getValor(object, attributeName));

        T o = findObjectByParam(ctx, String.format("select obj from %s  obj where %s = :%s",
                new Object[]{getObjectClass().getSimpleName(), attributeName, attributeName}), p);

        if (o == null) {
            return insert(ctx, object);
        } else {
            return o;
        }
    }

    @Override
    public void deleteComParam(final String ctx, String[] atributos, Object[] valores) throws Exception {
        EntityManager em = getEntityManager(ctx);
        try {
            if(!em.getTransaction().isActive()){
                em.getTransaction().begin();
            }
            String hql = "delete from " + this.objClass.getSimpleName() + getWhereClause(atributos);
            Query query = em.createQuery(hql);
            setWhereValues(atributos, valores, query);
            logar(ctx, atributos, valores);
            query.executeUpdate();
            em.getTransaction().commit();
        } catch (Exception e1) {
            if(em.getTransaction().isActive()){
                em.getTransaction().rollback();
            }
            e1.printStackTrace();
        } finally {
            em.clear();
        }
    }

    @Override
    public void executeQuery(final String ctx, String hql) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            Query query = em.createQuery(hql);
            query.executeUpdate();
            em.flush();
            em.getTransaction().commit();
        } catch (Exception e){
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
        }finally {
        }
    }

    @Override
    public int executeNativeSQL(final String ctx, String sql) throws Exception {
        return executeNativeSQL(ctx, sql, true);
    }

    @Override
    public int executeNativeSQL(final String ctx, String sql, boolean rolback) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }

        try {
            Query query = em.createNativeQuery(sql);
            int retorno = query.executeUpdate();
            em.flush();
            em.getTransaction().commit();
            return retorno;
        } catch (Exception e){
            if (rolback && em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw e;
        } finally {
        }

    }

    @Override
    public void updateAlgunsCampos(final String ctx, String[] atributos, Object[] valores, String[] campos, Object[] valoresCampos) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            //UPDATE serierealizada set carga = :carga, tempoutil = :tempoutil where codigo = :codigo
            StringBuilder hql = new StringBuilder(" UPDATE " + this.objClass.getSimpleName() + " SET ");
            for (int i = 0; i < atributos.length; i++) {
                String atributo = atributos[i];
                hql.append(i > 0 ? "," : "").append(atributo).append(" = :").append(atributo.replaceAll("\\.", ""));
            }
            hql.append(" WHERE ");
            for (int i = 0; i < campos.length; i++) {
                String campo = campos[i];
                hql.append(i == 0 ? "" : " and ").append(campo).append(" = :").append(campo.replaceAll("\\.", ""));
            }

            Query query = em.createQuery(hql.toString());
            for (int i = 0; i < atributos.length; i++) {
                query.setParameter(atributos[i].replaceAll("\\.", ""), valores[i]);
            }
            for (int i = 0; i < valoresCampos.length; i++) {
                query.setParameter(campos[i].replaceAll("\\.", ""), valoresCampos[i]);
            }
            query.executeUpdate();
			em.flush();
        } catch (Exception e) {
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            e.printStackTrace();
            throw e;
        } finally {
            if (em.getTransaction().isActive()) {
                em.getTransaction().commit();
            }
        }
    }

    @Override
    public Integer insertAlgunsCampos(final String ctx, String[] atributos, Object[] valores) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            //INSERT INTO serierealizada (carga,tempoutil) VALUES (:carga, :tempoutil)
            Table a = this.objClass.getAnnotation(Table.class);
            final String tableName = a != null && a.name() != null ? a.name() : this.objClass.getSimpleName();
            StringBuilder hql = new StringBuilder(String.format("INSERT INTO %s (", tableName));
            for (int i = 0; i < atributos.length; i++) {
                String atributo = atributos[i];
                hql.append(i > 0 ? "," : "").append(atributo.replaceAll("\\.", ""));
            }
            hql.append(")");
            hql.append(" VALUES (");
            for (int i = 0; i < atributos.length; i++) {
                hql.append(i == 0 ? "" : ",").append("?");
            }
            hql.append(") RETURNING codigo");

            Query query = em.createNativeQuery(hql.toString());
            Uteis.logar(null, hql.toString());
            for (int i = 0; i < atributos.length; i++) {
                query.setParameter(i + 1, valores[i]);
                if (Uteis.debug) {
                    System.out.println(String.format("%s => %s", atributos[i], valores[i]));
                }
            }
            Integer id = (Integer) query.getSingleResult();
            return id;
        }catch (Exception e){
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw e;
        }finally {
            em.getTransaction().commit();
        }

    }

    @Override
    public void removeEnumValue(final String ctx, String atributo, Integer valor) throws Exception {
        //obtem os registros com id maior do que o que vai ser excluido
        List<Integer> idsEnumMaiores = listOfObjects(ctx, "SELECT distinct(" + atributo + ") FROM " + this.objClass.getSimpleName() + " WHERE " + atributo + " > " + valor);

        Map<String, Object> param = new HashMap<String, Object>();
        List<T> lista = findByParam(ctx, "SELECT obj FROM " + this.objClass.getSimpleName() + " obj WHERE " + atributo + " = " + valor, param);
        for (T obj : lista) {
            delete(ctx, obj);
        }
        //iterar nos registros a serem alterados
        for (Integer id : idsEnumMaiores) {
            Query queryUpdate = getEntityManager(ctx).createNativeQuery(
                    "UPDATE " + this.objClass.getSimpleName() + " SET " + atributo + " = " + (id - 1) + " WHERE "
                    + atributo + " = " + id);
            queryUpdate.executeUpdate();
        }
    }

    @Override
    public Number avgWithParam(final String ctx, final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder("select AVG(").append(atributoCount).append(") from ").append(this.objClass.getSimpleName()).append(" obj ");
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return (Number) query.getSingleResult();
    }

    @Override
    public Number numberWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder(queryStr);
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return (Number) query.getSingleResult();
    }

    @Override
    public List<Number> listNumberWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder(queryStr);
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return query.getResultList();
    }

    @Override
    public List<String> listStringWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception {
        StringBuilder s = new StringBuilder(queryStr);
        s.append(whereClause != null ? whereClause.toString() : "");
        Query query = getEntityManager(ctx).createNativeQuery(s.toString());
        if (params != null) {
            for (Iterator<String> it = params.keySet().iterator(); it.hasNext();) {
                String p = it.next();
                query.setParameter(p, params.get(p));
            }
        }
        logar(ctx, params);
        return query.getResultList();
    }

    @Override
    public boolean existsWithParam(final String ctx, final StringBuilder whereClause) throws Exception {
        StringBuilder s = new StringBuilder("SELECT EXISTS(SELECT CODIGO FROM ").
                append(objClass.getSimpleName()).
                append(" WHERE ").append(whereClause).append(")");
        Query query = getEntityManager(ctx).createNativeQuery(s.toString());
        logar(ctx, s);
        return (Boolean) query.getSingleResult();
    }

    @Override
    public boolean exists(final String ctx, final Object bean, final String attrName) {
        return exists(ctx, bean, attrName, "codigo");
    }
    public boolean exists(final String ctx, final Object bean, final String attrName, final String codName) {
        List<T> existentes = null;
        try {
            Map<String, Object> p = new HashMap<String, Object>();
            final String valor = (String) UtilReflection.getValor(bean, attrName);
            Integer codigo = UtilReflection.getValor(bean, codName) == null ? null : Integer.valueOf(UtilReflection.getValor(bean, codName).toString());
            codigo = codigo == null ? 0 : codigo;
            p.put(attrName, valor.toLowerCase());
            p.put("codigo", codigo);
            existentes = findByParam(ctx, new StringBuilder(
                    String.format("where obj.codigo <> :codigo and trim(lower(obj.%s)) = :%s",
                    new Object[]{attrName, attrName})), p);
        } catch (Exception ex) {
            Logger.getLogger(objClass.getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return !validacao.isEmpty(existentes);
    }

    @Override
    public Session getCurrentSession(final String ctx) {
        try {
            return (Session) getEntityManager(ctx).getDelegate();            
        } catch (Exception ex) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    @Override
    public ResultSet createStatement(final String ctx, final String sql) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            Statement stm = conn.createStatement();
            return stm.executeQuery(sql);
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }finally {
            conn.close();
        }
    }
    @Override
    public ResultSet createPreparedStatement(final String ctx, final String sql, Object... params) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            PreparedStatement pstmt = conn.prepareStatement(sql);
            // Defina os parâmetros do PreparedStatement
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            return pstmt.executeQuery();
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        } finally {
            conn.close();
        }
    }

    @Override
    public Connection getConnection(final String ctx) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            return conn;
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
    }

    @Override
    public void executeNative(final String ctx, final String sql) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            try (Statement stm = conn.createStatement()) {
                stm.execute(sql);
            }
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }finally {
            conn.close();
        }
    }

    @Override
    public void deleteAll(final String ctx) throws Exception {
        EntityManager ss = getEntityManager(ctx);
        try {
            ss.getTransaction().begin();
            String hql = "delete from " + this.objClass.getSimpleName();
            Query query = ss.createQuery(hql);
            query.executeUpdate();
            ss.getTransaction().commit();
        } catch (Exception e) {
            if (ss.getTransaction().isActive()) {
                ss.getTransaction().rollback();
            }
            e.printStackTrace();
        }

    }

    public void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List findByParamNative(final String ctx, String queryS, Map<String, Object> params, int max, int index) throws Exception {
        Query q = getEntityManager(ctx).createNativeQuery(queryS);

        if (max != 0)
            q.setMaxResults(max);
        if (index != 0)
            q.setFirstResult(index);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }

        return q.getResultList();
    }

    @Override
    public void updateSomeFildsNoFlush(final String ctx, String[] atributos, Object[] valores, String[] campos, Object[] valoresCampos) throws Exception {
        EntityManager em = getEntityManager(ctx);
        if(!em.getTransaction().isActive()){
            em.getTransaction().begin();
        }
        try {
            StringBuilder hql = new StringBuilder(" UPDATE " + this.objClass.getSimpleName() + " SET ");
            for (int i = 0; i < atributos.length; i++) {
                String atributo = atributos[i];
                hql.append(i > 0 ? "," : "").append(atributo).append(" = :").append(atributo.replaceAll("\\.", ""));
            }
            hql.append(" WHERE ");
            for (int i = 0; i < campos.length; i++) {
                String campo = campos[i];
                hql.append(i == 0 ? "" : " and ").append(campo).append(" = :").append(campo.replaceAll("\\.", ""));
            }

            Query query = em.createQuery(hql.toString());
            for (int i = 0; i < atributos.length; i++) {
                query.setParameter(atributos[i].replaceAll("\\.", ""), valores[i]);
            }
            for (int i = 0; i < valoresCampos.length; i++) {
                query.setParameter(campos[i].replaceAll("\\.", ""), valoresCampos[i]);
            }
            query.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
            if (em.getTransaction().isActive()) {
                em.getTransaction().rollback();
            }
            throw e;
        } finally {
            em.getTransaction().commit();
        }
    }

    @Override
    public void deleteList(final String ctx, List<T> objects) throws Exception {
        EntityManager em = getEntityManager(ctx);
        Transaction transaction = getCurrentSession(ctx).beginTransaction();
        try {
            for (T object : objects) {
                em.remove(em.contains(object) ? object : em.merge(object));
            }
            transaction.commit();
        } catch (Exception ex) {
            if (em.getTransaction().isActive()) {
                transaction.rollback();
            }
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public List<T> updateList(final String ctx, List<T> objects) throws Exception {
        EntityManager em = getEntityManager(ctx);
        Transaction transaction = getCurrentSession(ctx).beginTransaction();
        try {
            List<T> result = new ArrayList<>();
            for (T object : objects) {
                result.add(em.merge(object));
            }
            transaction.commit();
            return result;
        } catch (Exception ex) {
            if (em.getTransaction().isActive()) {
                transaction.rollback();
            }
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public T findByIdClearSession(final String ctx, ID id) throws Exception {
        getCurrentSession(ctx).clear();
        return getEntityManager(ctx).find(objClass, id);
    }

    public List findByParamList(final String ctx, String queryS, Map<String, Object> params, int max, int index) throws Exception {
        Query q = getEntityManager(ctx).createQuery(queryS);
        if (max != 0)
            q.setMaxResults(max);
        if (index != 0)
            q.setFirstResult(index);

        logar(ctx, params);
        for (String chave : params.keySet()) {
            q.setParameter(chave, params.get(chave));
        }
        return q.getResultList();
    }
}
