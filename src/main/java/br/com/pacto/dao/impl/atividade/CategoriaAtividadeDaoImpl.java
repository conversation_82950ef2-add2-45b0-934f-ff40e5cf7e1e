/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.CategoriaAtividadeDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class CategoriaAtividadeDaoImpl extends DaoGenericoImpl<CategoriaAtividade, Integer> implements
        CategoriaAtividadeDao {

    private static final int MAXIMO_CATEGORIA_ATIVIDADE_CONSULTAR = 50;

    public List<CategoriaAtividadeResponseTO> consultarCategoriaAtividades(final String ctx, FiltroCategoriaAtividadeJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_CATEGORIA_ATIVIDADE_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CATEGORIA_ATIVIDADE_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<CategoriaAtividadeResponseTO> listaRet = new ArrayList<CategoriaAtividadeResponseTO>();
        hql.append("SELECT obj FROM CategoriaAtividade obj ");
        if ((filtros.getParametro() != null) && (!filtros.getParametro().trim().equals(""))) {
            where.append("where upper(obj.nome) like '%").append(filtros.getParametro().toUpperCase()).append("%'");
        }
        if (where.length() > 0){
            hql.append(where.toString());
        }
        if(paginadorDTO.getSortMap() != null && paginadorDTO.getSortMap().isEmpty() == false) {
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        List<CategoriaAtividade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, null).longValue());
            }
            lista = findByParam(ctx, hql.toString(), new HashMap<String, Object>(),maxResults,indiceInicial);

        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.CATEGORIA_NAO_ENCONTRADA, e);
        }
        if (lista != null) {
            for (CategoriaAtividade categoriaAtividade : lista) {
                listaRet.add(new CategoriaAtividadeResponseTO(categoriaAtividade));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return listaRet;
    }
}