/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.AulaAlunoDao;
import br.com.pacto.objeto.Calendario;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AulaAlunoDaoImpl extends DaoGenericoImpl<AulaAluno, Integer> implements
        AulaAlunoDao {

    public List<AulaAluno> alunosHorarioDia(String ctx, Integer aulaHorarioId, Date dia) throws Exception {
        String hql = "select obj from AulaAluno obj where obj.dia between :inicio and :fim and obj.horario.codigo = :horario";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("horario", aulaHorarioId);
        params.put("inicio", Calendario.getDataComHoraZerada(dia));
        params.put("fim", Calendario.getDataComHora(dia, "23:59"));

        return findByParam(ctx, hql, params);
    }
}
