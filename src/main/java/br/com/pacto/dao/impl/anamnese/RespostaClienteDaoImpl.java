package br.com.pacto.dao.impl.anamnese;

import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON> Alcides on 04/05/2017.
 */
@Repository
public class RespostaClienteDaoImpl extends DaoGenericoImpl<RespostaCliente, Integer> implements RespostaClienteDao {

    public RespostaCliente consultarRespostaClientePorPergunta(String ctx, Integer codigoCliente, Integer codigoPergunta) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM RespostaCliente obj WHERE obj.perguntaAnamnese.pergunta.codigo = :codigoPergunta AND obj.cliente.codigo = :codigoCliente");
        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoPergunta", codigoPergunta);
        params.put("codigoCliente", codigoCliente);
        return findFirstObjectByParam(ctx, hql.toString(), params);
    }

    public RespostaCliente consultarPorCodigoRespostaClienteParQEPergunta(String ctx, Integer codigoRespostaClienteParQ, Integer codigoPergunta) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM RespostaCliente obj WHERE obj.respostaClienteParQ.codigo = :codigoRespostaClienteParQ AND obj.perguntaAnamnese.pergunta.codigo = :codigoPergunta");
        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoRespostaClienteParQ", codigoRespostaClienteParQ);
        params.put("codigoPergunta", codigoPergunta);
        return findFirstObjectByParam(ctx, hql.toString(), params);
    }

    public List<RespostaCliente> obterRespostasMaisRecentesPorCliente(String ctx, Integer codigoCliente) throws Exception {
        String sql = "SELECT rc.* " +
                "FROM respostacliente rc " +
                "INNER JOIN respostaclienteparq rcp ON rc.respostaclienteparq_codigo = rcp.codigo " +
                "WHERE rc.cliente_codigo = ? " +
                "ORDER BY rcp.dataresposta DESC";

        try (PreparedStatement stm = getConnection(ctx).prepareStatement(sql)) {
            stm.setInt(1, codigoCliente);
            List<RespostaCliente> respostas = new ArrayList<>();
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    RespostaCliente respostaCliente = new RespostaCliente();
                    respostaCliente.setCodigo(rs.getInt("codigo"));
                    respostaCliente.setResposta(rs.getString("resposta"));
                    respostas.add(respostaCliente);
                }
            }
            return respostas;
        }
    }
}
