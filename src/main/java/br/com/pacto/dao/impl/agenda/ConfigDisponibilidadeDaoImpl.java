package br.com.pacto.dao.impl.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.ConfigDisponibilidade;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.agenda.ConfigDisponibilidadeDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ConfigDisponibilidadeDaoImpl extends DaoGenericoImpl<ConfigDisponibilidade, Integer> implements
        ConfigDisponibilidadeDao {

    private static final int MAXIMO_CONFIG_DISPONIBILIDADE = 50;

    @Override
    public List<ConfigDisponibilidade> obterDisponibilidades(String ctx, Integer empresaId, FiltroDisponibilidadeDTO filtro, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        int maxResults = MAXIMO_CONFIG_DISPONIBILIDADE;
        int indiceInicial=0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CONFIG_DISPONIBILIDADE : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        Map<String,Object> params = new HashMap<String,Object>();
        hql.append("select obj from ConfigDisponibilidade obj ");
        where.append(" where 0 = 0 ");
        if(filtro!=null){
            if(!UteisValidacao.emptyList(filtro.getProfessorIds())){
                String professores = "";
                for (Integer cod : filtro.getProfessorIds()) {
                    professores += "," + cod;
                }
                if (SuperControle.independente(ctx)) {
                    where.append(" AND obj.professor.codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
                } else {
                    where.append(" AND obj.professor.codigoColaborador IN (").append(professores.replaceFirst(",", "")).append(") ");
                }
            }
            if (!UteisValidacao.emptyList(filtro.getTipoAgendamentoIds())) {
               String tiposAgendamentoId = "";
               for (Integer tipoAgendamentoId : filtro.getTipoAgendamentoIds()) {
                   tiposAgendamentoId += "," + tipoAgendamentoId;
               }
               where.append(" AND obj.tipoEvento.codigo IN (").append(tiposAgendamentoId.replaceFirst(",", "")).append(") ");
            }
        }
        if (SuperControle.independente(ctx)) {
            where.append(" AND obj.professor.empresa.codigo = ").append(empresaId);
        } else {
            where.append(" AND obj.professor.empresa.codZW = ").append(empresaId);
        }

        hql.append(where.toString());

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, params).longValue());
            hql.append(paginadorDTO.getSQLOrderBy());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }


        return findByParam(ctx, hql.toString(), params, maxResults, indiceInicial);
    }

    @Override
    public Integer countTotal(String ctx) throws Exception {
        String sql = "SELECT count(codigo) FROM ConfigDisponibilidade";

        Integer result;
        try (ResultSet rs = createStatement(ctx, sql)) {
            result = 0;
            if (rs.next()) {
                result = rs.getInt("count");
            }
        }

        return result;
    }


    @Override
    public ConfigDisponibilidade consultarConfiguracaoDaDisponibilidade(String ctx, Agendamento disponibilidade) throws Exception{
        StringBuilder hql = new StringBuilder(" where obj.professor.codigo = :professor \n");
        hql.append(" and obj.dia <= :dia and obj.dataLimite >= :dia ");
        hql.append(" and obj.tipoEvento.codigo = :tipoevento ");
        Map params = new HashMap();
        params.put("dia", Calendario.getDataComHoraZerada(disponibilidade.getInicio()).getTime());
        params.put("tipoevento", disponibilidade.getTipoEvento().getCodigo());
        params.put("professor", disponibilidade.getProfessor().getCodigo());
        List<ConfigDisponibilidade> list = findByParam(ctx, hql, params);
        for(ConfigDisponibilidade cfg: list){
            if(cfg.getCodigo().equals(disponibilidade.getNsu())){
                return cfg;
            }
        }
        return UteisValidacao.emptyList(list) ? null : list.get(0);
    }
}
