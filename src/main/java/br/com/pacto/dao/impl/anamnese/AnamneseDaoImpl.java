package br.com.pacto.dao.impl.anamnese;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.AnamneseResponseTO;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.anamnese.AnamneseDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AnamneseExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Repository
public class AnamneseDaoImpl extends DaoGenericoImpl<Anamnese, Integer> implements AnamneseDao {

    @Override
    public List<Anamnese> listaTodasNaoEParq(String ctx) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("WHERE (obj.parq IS FALSE or obj.parq is null)");
        return findByParam(ctx, hql, new HashMap<String, Object>());
    }

    @Override
    public List<Anamnese> listaTodasAtivasIntegradasNaoEParq(String ctx) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("WHERE (obj.parq IS FALSE or obj.parq is null) AND obj.ativa = TRUE AND obj.tipo = 1");
        return findByParam(ctx, hql, new HashMap<String, Object>());
    }

    @Override
    public List<Anamnese> listaTodasIntegradasNaoEParq(String ctx) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("WHERE (obj.parq IS FALSE or obj.parq is null) AND obj.tipo = 1");
        return findByParam(ctx, hql, new HashMap<String, Object>());
    }

    @Override
    public List<Anamnese> listaTodasAtivasEParq(String ctx) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("WHERE obj.ativa is true and (obj.parq is true or obj.parqpadraorj is true)");
        return findByParam(ctx, hql, new HashMap<String, Object>());
    }
}
