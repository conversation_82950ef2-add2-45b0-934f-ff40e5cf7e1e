package br.com.pacto.dao.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.MensagemUtils;
import org.hibernate.impl.SessionFactoryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityExistsException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/**
 * Created by Rafael on 09/10/2016.
 */
@Repository
public class AvaliacaoFisicaDaoImpl extends DaoGenericoImpl<AvaliacaoFisica, Integer> implements AvaliacaoFisicaDao {

    @Autowired
    private MensagemUtils mensagensUtil;

    public Connection getConnection(final String ctx) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            return conn;
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
    }

}