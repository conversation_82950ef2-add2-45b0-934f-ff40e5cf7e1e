package br.com.pacto.dao.impl.disponibilidade;

import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import org.springframework.stereotype.Repository;

@Repository
public class HorarioDisponibilidadeDaoImpl extends DaoGenericoImpl<HorarioDisponibilidade, Integer> implements HorarioDisponibilidadeDao {

    private static final int MAXIMO_DISPONIBILIDADE = 50;
}
