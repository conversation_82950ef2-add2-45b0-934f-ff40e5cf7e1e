/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.base;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.dao.intf.base.DaoGenericoJDBC;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.MensagemUtils;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.persistence.Column;
import javax.persistence.EntityExistsException;
import javax.persistence.EntityManager;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.Session;
import org.hibernate.impl.SessionFactoryImpl;
import org.hsqldb.Types;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class DaoGenericoJDBCImpl<T, ID extends Serializable> implements
        DaoGenericoJDBC<T, ID> {

    @Autowired
    private EntityManagerService entityService;
    @Autowired
    private MensagemUtils mensagensUtil;

    @Override
    public EntityManager getEntityManager(final String ctx) throws Exception {
        return entityService.getEntityManager(ctx);
    }

    @Override
    public Session getCurrentSession(final String ctx) {
        try {
            return (Session) getEntityManager(ctx).getDelegate();
        } catch (Exception ex) {
            Logger.getLogger(DaoGenericoImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    @Override
    public ResultSet createStatement(final String ctx, final String sql) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            Statement stm = conn.createStatement();
            return stm.executeQuery(sql);
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        } finally {
            conn.close();
        }
    }

    @Override
    public Connection getConnection(final String ctx) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            return conn;
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
    }

    @Override
    public int obterUltimoCodigoGeradoTabela(final String ctx,
            String nomeTabela) throws Exception {

        Connection con = getConnection(ctx);

        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela
                + "_codigo_seq";
        try (Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE,
                ResultSet.CONCUR_READ_ONLY)) {
            try (ResultSet resultado = stmt.executeQuery(csCodigoGerado)) {
                resultado.first();
                return (resultado.getInt(1));
            }
        }
    }

    @Override
    public void inserir(final String ctx, T obj) throws Exception {

        StringBuilder sql = new StringBuilder("INSERT INTO ").append(((Table) obj.getClass().getAnnotation(Table.class)).name());
        sql.append("(").append(getNomeColunas(obj)).append(") ");
        sql.append("VALUES (").append(getInterrogacoesColunas(obj)).append(")");
        try {
            Integer codigo = obterUltimoCodigoGeradoTabela(ctx,
                    ((Table) obj.getClass().getAnnotation(Table.class)).name());
            UtilReflection.setValor(obj, codigo, "codigo");
        } catch (Exception e) {
        }
        PreparedStatement sqlInserir = getConnection(ctx).prepareStatement(sql.toString());
        preencherStatement(sqlInserir, obj);
        sqlInserir.execute();

    }

    @Override
    public <T> T montarDados(ResultSet rs, Class<T> clazz)
            throws Exception {

        T obj = clazz.newInstance();

        Field[] vet = obj.getClass().getDeclaredFields();

        try {
            for (int i = 0; i < vet.length; i++) {
                Field field = vet[i];
//                final String fieldName = field.getAnnotation(Column.class) != null ? ((Column) field.getAnnotation(Column.class)).name() : field.getName();
                final String fieldName = field.getName();
                if (field.getAnnotation(Transient.class) != null) {
                    continue;
                }
                if (field.getType() == Double.class) {
                    Double valor = rs.getDouble(fieldName);
                    UtilReflection.setValor(obj, valor, field.getName());
                } else if (field.getType() == Integer.class) {
                    Integer valor = rs.getInt(fieldName);
                    UtilReflection.setValor(obj, valor, field.getName());
                } else if (field.getType() == String.class) {
                    String valor = rs.getString(fieldName);
                    UtilReflection.setValor(obj, valor, field.getName());
                } else if (field.getType() == Date.class) {
                    Date valor = rs.getTimestamp(fieldName);
                    if (valor != null) {
                        UtilReflection.setValor(obj, new Date(valor.getTime()), field.getName());
                    }
                } else if (field.getType() == Boolean.class) {
                    Boolean valor = rs.getBoolean(fieldName);
                    UtilReflection.setValor(obj, valor, field.getName());
                } else if (field.getType().isEnum()) {
                    Object valor = rs.getObject(fieldName);
                    if (valor != null) {
                        Object objEnum = getEnumField(field, valor);
                        UtilReflection.setValor(obj, objEnum, fieldName);
                    }
                }
            }
        } catch (Exception ex) {
            throw ex;
        }

        return obj;

    }

    public static Object getEnumField(Field field, Object valor) throws Exception {
        Enum<?>[] enums = (Enum[]) field.getType().getEnumConstants();
        Enumerated eAnnotation = field.getAnnotation(Enumerated.class);
        if (eAnnotation != null && EnumType.ORDINAL.equals(eAnnotation.value())) {
            for (Enum e : enums) {
                if (e.ordinal() == new Integer(valor.toString()).intValue()) {
                    return e;
                }
            }
        } else {
            for (Enum e : enums) {
                if (e.name().equals(valor)) {
                    return e;
                }
            }
        }
        return null;
    }

    private String resolveTableName(Class clazz) {
        String tableName = clazz.getSimpleName();
        final String annotationTableName = ((Table) clazz.getAnnotation(Table.class)).name();
        if (!UteisValidacao.emptyString(annotationTableName)) {
            tableName = annotationTableName;
        }
        return tableName;
    }

    @Override
    public <T> List<T> consultar(final String ctx, Class<T> clazz) throws Exception {
        List<T> lista;
        try (ResultSet rs = criarConsulta(ctx, String.format("SELECT * FROM %s ", resolveTableName(clazz)))) {
            lista = new ArrayList();
            while (rs.next()) {
                lista.add(montarDados(rs, clazz));
            }
        }

        return lista;

    }

    @Override
    public <T> List<T> consultar(final String ctx, Class<T> clazz, String condicao) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT * FROM ").append(resolveTableName(clazz));
        sql.append(" WHERE ").append(condicao);
        List<T> lista;
        try (ResultSet rs = criarConsulta(ctx, sql.toString())) {
            lista = new ArrayList();

            while (rs.next()) {
                lista.add(montarDados(rs, clazz));
            }
        }

        return lista;

    }

    @Override
    public <T> T consultarPorChavePrimaria(final String ctx, Class<T> clazz, int codigo) throws Exception {

        StringBuilder sql = new StringBuilder("SELECT * FROM ").
                append(resolveTableName(clazz));
        sql.append(" WHERE codigo = ?");
        try (PreparedStatement pstmt = getConnection(ctx).prepareStatement(sql.toString())) {
            pstmt.setInt(1, codigo);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, clazz);
                }
            }
        }
        return null;

    }

    @Override
    public ResultSet criarConsulta(final String ctx, final String sql) throws Exception {
        Statement stm = getConnection(ctx).createStatement();
        return stm.executeQuery(sql);

    }

    @Override
    public void excluir(final String ctx, T obj) throws Exception {

        StringBuilder sql = new StringBuilder("DELETE FROM ").append(((Table) obj.getClass().getAnnotation(Table.class)).name());
        sql.append(" WHERE codigo = ").append(UtilReflection.getValor(obj, "codigo"));

        PreparedStatement sqlExcluir = getConnection(ctx).prepareStatement(sql.toString());
        preencherStatement(sqlExcluir, obj);
        sqlExcluir.execute();
    }

    @Override
    public List<Field> getValidFields(final Class<?> cls) {
        Field[] vet = cls.getDeclaredFields();
        List<Field> l = new ArrayList<Field>();
        for (int i = 0; i < vet.length; i++) {
            Field field = vet[i];
            Transient anotTransient = field.getAnnotation(Transient.class);
            Transient anotTransientJPA = field.getAnnotation(Transient.class);
            if (anotTransient == null && anotTransientJPA == null) {//se é transiente nao faz parte do SQL para o banco de dados
                l.add(field);
            }
        }
        return l;
    }

    private String getNomeColunas(final T obj) {
        String nomeColunas = "";
        List<Field> vet = getValidFields(obj.getClass());
        for (int i = 0; i < vet.size(); i++) {
            Field field = vet.get(i);
            Column column = field.getAnnotation(Column.class);
            Column columnJPA = field.getAnnotation(Column.class);
            String columnName = column != null ? column.name() : field.getName();
            if (columnJPA != null && columnJPA.name() != null
                    && !columnJPA.name().isEmpty()) {
                columnName = columnJPA.name();
            }
            if (i + 1 == vet.size()) {
                nomeColunas += columnName;
            } else {
                nomeColunas += columnName + ",";
            }
        }
        return nomeColunas;
    }

    private String getInterrogacoesColunas(final T obj) {
        String interrogacoes = "";
        List<Field> vet = getValidFields(obj.getClass());
        for (int i = 0; i < vet.size(); i++) {
            if (i + 1 == vet.size()) {
                interrogacoes += "?";
            } else {
                interrogacoes += "?,";
            }
        }
        return interrogacoes;
    }

    private void preencherStatement(PreparedStatement pstm, final T obj) {
        try {
            List<Field> vet = getValidFields(obj.getClass());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < vet.size(); i++) {
                Field field = vet.get(i);
                Object valor = UtilReflection.getValor(obj, field.getName());
                if (valor == null) {
                    pstm.setNull(i + 1, Types.NULL);
                } else {
                    if (field.getType() == Double.class) {
                        pstm.setDouble(i + 1, (Double) UtilReflection.getValor(obj, field.getName()));
                    } else if (field.getType() == Integer.class) {
                        pstm.setInt(i + 1, (Integer) UtilReflection.getValor(obj, field.getName()));
                    } else if (field.getType() == String.class) {
                        pstm.setString(i + 1, (String) UtilReflection.getValor(obj, field.getName()));
                    } else if (field.getType() == Boolean.class) {
                        pstm.setBoolean(i + 1, Boolean.valueOf(UtilReflection.getValor(obj, field.getName()).toString()));
                    } else if (field.getType() == Date.class) {
                        pstm.setTimestamp(i + 1,
                                Uteis.getDataJDBCTimestamp(sdf.parse(
                                UtilReflection.getValor(obj, field.getName()).toString())));
                    } else {
                        pstm.setObject(i + 1, UtilReflection.getValor(obj, field.getName()));
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(DaoGenericoJDBCImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public StringBuffer splitInverse(final String prefixo, final String[] vet) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < vet.length; i++) {
            if (i == 0) {
                sb.append(prefixo).append("(");
            }
            if (i + 1 < vet.length) {
                sb.append("'").append(vet[i]).append("', ");
            } else {
                sb.append("'").append(vet[i]).append("'");
            }

            if (i + 1 == vet.length) {
                sb.append(")");
            }
        }
        return sb;
    }

    @Override
    public StringBuffer splitILike(final String prefixo, final String[] vet) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < vet.length; i++) {
            if (i == 0) {
                sb = new StringBuffer("(");
            }
            if (i + 1 < vet.length) {
                sb.append(prefixo).append(" ilike('%").append(vet[i]).append("%') or ");
            } else {
                sb.append(prefixo).append(" ilike('%").append(vet[i]).append("%')");
            }

            if (i + 1 == vet.length) {
                sb.append(")");
            }
        }
        return sb;
    }

    @Override
    public StringBuffer splitBetween(final String prefixo, final String[] vet) {
        StringBuffer sb = new StringBuffer();
        if (vet.length == 2) {
            sb = new StringBuffer();
            sb.append(prefixo).append(" between ").append(vet[0]).append(" and ").append(vet[1]);
            return sb;
        } else if (vet.length == 1) {
            sb = new StringBuffer();
            sb.append(prefixo).append(" = ").append(vet[0]);
            return sb;
        }
        return sb;
    }
}
