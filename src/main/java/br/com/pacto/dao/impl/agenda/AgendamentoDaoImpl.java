/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.agenda;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON><PERSON>;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AgendamentoDaoImpl extends DaoGenericoImpl<Agendamento, Integer> implements
        AgendamentoDao {

    @Override
    public List<Agendamento> obterDisponibilidadePorNSUApartirDia(String ctx, Integer nsu, Date dia, Date limite) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM Agendamento ");
        sql.append(" WHERE nsu = ").append(nsu);
        sql.append(" AND disponibilidade is true");
        sql.append(" AND inicio >= '").append(new Timestamp(dia.getTime())).append("' ");
        if(limite != null){
            sql.append(" AND fim < '").append(new Timestamp(limite.getTime())).append("' ");
        }

        List<Agendamento> result;
        try (ResultSet rs = createStatement(ctx, sql.toString())) {
            result = new ArrayList<>();

            while (rs.next()) {
                Agendamento agendamento = new Agendamento();
                agendamento.setCodigo(rs.getInt("codigo"));
                if (!UteisValidacao.emptyString(rs.getString("inicio"))) {
                    agendamento.setInicio(Calendario.getDate("yyyy-MM-dd hh:mm:ss", rs.getString("inicio")));
                }
                if (!UteisValidacao.emptyString(rs.getString("fim"))) {
                    agendamento.setFim(Calendario.getDate("yyyy-MM-dd hh:mm:ss", rs.getString("fim")));
                }
                agendamento.setProfessor(new ProfessorSintetico(rs.getInt("professor_codigo")));
                agendamento.setTipoEvento(new TipoEvento(rs.getInt("tipoevento_codigo")));
                agendamento.setCliente(new ClienteSintetico(rs.getInt("cliente_codigo")));

                result.add(agendamento);
            }
        }
        return result;
    }

    @Override
    public List<Agendamento> obterDisponibilidadePorDia(String ctx, Integer empresa, Date dia, Date limite) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT a.* FROM Agendamento a ");
        sql.append(" inner join professorsintetico p on p.codigo = a.professor_codigo ");
        sql.append(" WHERE a.disponibilidade is true ");
        sql.append(" AND a.inicio >= '").append(new Timestamp(dia.getTime())).append("' ");
        if(limite != null){
            sql.append(" AND a.fim < '").append(new Timestamp(limite.getTime())).append("' ");
        }

        List<Agendamento> result;
        try (ResultSet rs = createStatement(ctx, sql.toString())) {
            result = new ArrayList<>();

            while (rs.next()) {
                Agendamento agendamento = new Agendamento();
                agendamento.setCodigo(rs.getInt("codigo"));
                if (!UteisValidacao.emptyString(rs.getString("inicio"))) {
                    agendamento.setInicio(Calendario.getDate("yyyy-MM-dd hh:mm:ss", rs.getString("inicio")));
                }
                if (!UteisValidacao.emptyString(rs.getString("fim"))) {
                    agendamento.setFim(Calendario.getDate("yyyy-MM-dd hh:mm:ss", rs.getString("fim")));
                }
                agendamento.setProfessor(new ProfessorSintetico(rs.getInt("professor_codigo")));
                agendamento.setTipoEvento(new TipoEvento(rs.getInt("tipoevento_codigo")));
                agendamento.setCliente(new ClienteSintetico(rs.getInt("cliente_codigo")));

                result.add(agendamento);
            }
        }
        return result;
    }

    @Override
    public Set<Integer> obterNsuApartirDia(String ctx, Date dia) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT nsu FROM Agendamento ");
        sql.append("WHERE disponibilidade ");
        sql.append(" AND inicio >= '").append(new Timestamp(dia.getTime())).append("' ");

        Set<Integer> result;
        try (ResultSet rs = createStatement(ctx, sql.toString())) {
            result = new HashSet<>();
            while (rs.next()) {
                result.add(rs.getInt("nsu"));
            }
        }
        return result;
    }

    @Override
    public void atualizarAlgunsCamposLista(String ctx, List<Integer> agendamentosId, Integer nsu) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE Agendamento SET nsu = ").append(nsu);
        String agendamentosSelecionados = "";
        for (Integer agendamentoId : agendamentosId) {
            agendamentosSelecionados += "," + agendamentoId;
        }
        agendamentosSelecionados = agendamentosSelecionados.replaceFirst(",", "");
        sql.append(" WHERE codigo in (").append(agendamentosSelecionados).append(")");
        sql.append(" RETURNING true ");

        try (ResultSet statement = createStatement(ctx, sql.toString())) {
            statement.next();
        }

    }

    @Override
    public void alterarObservacaoAgendamento(String ctx, Integer agendamento, String observacao) throws ServiceException {
        AgendamentoDao agendamentoDao = UtilContext.getBean(AgendamentoDao.class);
        try {
            agendamentoDao.executeNativeSQL(ctx, " UPDATE Agendamento SET observacao = '" + observacao + "' " +
                    " WHERE codigo = " + agendamento);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public boolean verificarAlunoNaCarteira(String ctx, Integer professorCodigo, Integer clienteCodigo) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(1) FROM clientesintetico ");
        sql.append("WHERE codigo = ").append(clienteCodigo);
        sql.append(" AND professorsintetico_codigo = ").append(professorCodigo);

        int count = 0;
        try (ResultSet rs = createStatement(ctx, sql.toString())) {
            if (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erro ao verificar se o aluno está na carteira do professor", e);
        } catch (Exception e) {
            throw new RuntimeException("Erro inesperado na verificação da carteira", e);
        }
        return count > 0;
    }
}
