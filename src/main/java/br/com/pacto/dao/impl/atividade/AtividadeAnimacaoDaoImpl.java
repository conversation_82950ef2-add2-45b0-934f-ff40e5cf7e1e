/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeAnimacaoDaoImpl extends DaoGenericoImpl<AtividadeAnimacao, Integer> implements
        AtividadeAnimacaoDao {

    @Autowired
    private AtividadeDao atividadeDao;
    @Override
    public List<AtividadeAnimacao> obterImagens(String ctx, Integer codigoAtividade) throws ServiceException {

        List<AtividadeAnimacao> lista = new ArrayList<>();

        try (ResultSet rs = createStatement(ctx, "select aa.codigo as aatvcodigo, " +
                "aa.fotokey,  \n" +
                "aa.fotokeyminiatura,\n" +
                "aa.fotokeypequena," +
                "a.* from AtividadeAnimacao aa \n" +
                "left join animacao a on a.codigo = aa.animacao_codigo \n" +
                "where aa.atividade_codigo = " + codigoAtividade)) {
            while (rs.next()) {
                AtividadeAnimacao atividadeAnimacao = new AtividadeAnimacao();
                atividadeAnimacao.setCodigo(rs.getInt("aatvcodigo"));
                atividadeAnimacao.setFotoKey(rs.getString("fotokey"));
                atividadeAnimacao.setFotoKeyPequena(rs.getString("fotokeypequena"));
                atividadeAnimacao.setFotoKeyMiniatura(rs.getString("fotokeyminiatura"));
                Atividade atividade = atividadeDao.findById(ctx, codigoAtividade);
                atividadeAnimacao.setAtividade(atividade);
                if(rs.getInt("codigo")>0){
                    Animacao animacao = new Animacao();
                    animacao.setCodigo(rs.getInt("codigo"));
                    animacao.setTitulo(rs.getString("titulo"));
                    animacao.setSubTitulo(rs.getString("subtitulo"));
                    animacao.setTipo(rs.getInt("tipo") == 0 ? br.com.pacto.bean.animacao.TipoAnimacaoEnum.IMAGEM
                            : br.com.pacto.bean.animacao.TipoAnimacaoEnum.VIDEO);
                    animacao.setUrl(rs.getString("url"));
                    atividadeAnimacao.setAnimacao(animacao);
                }
                lista.add(atividadeAnimacao);
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao obter animacao da atividade", e);
        }
        return lista;
    }

    @Override
    public void deleteFotoKeyAtividade(String ctx, Integer codigoAtividade) throws ServiceException {
        try {
            String hql = "DELETE FROM AtividadeAnimacao obj WHERE obj.atividade_codigo = " + codigoAtividade;
            executeNativeSQL(ctx, hql);
        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir animações da atividade " + codigoAtividade, e);
        }
    }
}
