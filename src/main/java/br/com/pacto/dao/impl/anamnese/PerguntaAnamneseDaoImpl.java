package br.com.pacto.dao.impl.anamnese;

import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.anamnese.PerguntaAnamneseDao;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import org.springframework.stereotype.Repository;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Repository
public class PerguntaAnamneseDaoImpl extends DaoGenericoImpl<PerguntaAnamnese, Integer> implements PerguntaAnamneseDao {
}
