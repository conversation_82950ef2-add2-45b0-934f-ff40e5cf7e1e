package br.com.pacto.dao.impl.parceiro;

import br.com.pacto.bean.parceiro.Parceiro;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.parceiro.ParceiroDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ParceiroDaoImpl extends DaoGenericoImpl<Parceiro, Integer> implements ParceiroDao {


    @Override
    public void removerNotIn(String ctx, List<Parceiro> parceiros) throws Exception {
        StringBuilder hql = new StringBuilder();
        StringBuilder lista = new StringBuilder();
        for(int i = 0; i < parceiros.size(); i++) {
            if(i != 0) {
                lista.append(", ");
            }
            Parceiro parceiro = parceiros.get(i);
            lista.append(parceiro.getCodigo());
        }
        hql.append("DELETE FROM Parceiro obj WHERE obj.codigo NOT IN (")
            .append(lista)
            .append(")");
        executeQuery(ctx, hql.toString());
    }
}
