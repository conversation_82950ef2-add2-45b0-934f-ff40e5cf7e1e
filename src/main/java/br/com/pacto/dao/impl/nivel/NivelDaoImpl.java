/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.nivel;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.nivel.NivelDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.NivelExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class NivelDaoImpl extends DaoGenericoImpl<Nivel, Integer> implements
        NivelDao {

    private static final int MAXIMO_NIVEL_CONSULTAR = 30;

    public List<NivelResponseTO> consultar(String ctx, String nome, Integer ordem)throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<String, Object>();
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<NivelResponseTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM Nivel obj ");
        if ((nome != null) && (!nome.trim().equals(""))) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", nome.toUpperCase());
        }
        if (ordem != null){
            if (where.length() == 0){
                where.append("where ordem = ").append(ordem);
            }else{
                where.append(" and ordem = ").append(ordem);
            }
        }
        hql.append(where.toString()).append(" order by ordem");

        List<Nivel> lista;
        try {
            lista = findByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);
        }
        if (lista != null) {
            for (Nivel nivel : lista) {
                listaRet.add(new NivelResponseTO(nivel));
            }
        }
        return listaRet;
    }


    public List<Nivel> consultarNivel(String ctx, FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException {
        int maxResults = MAXIMO_NIVEL_CONSULTAR;
        int indiceInicial=0;
        Map<String, Object> param = new HashMap<String, Object>();
        getCurrentSession(ctx).clear();
        String nome = "";
        nome = filtros.getParametro();
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_NIVEL_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM Nivel obj ");
        if ((nome != null) && (!nome.trim().equals(""))) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", nome.toUpperCase());
        }
        String ordem = null;
        if (ordem != null){
            if (where.length() == 0){
                where.append("where ordem = ").append(ordem);
            }else{
                where.append(" and ordem = ").append(ordem);
            }
        }
        if (filtros.getSituacao() != null) {
            if (filtros.getSituacao()) {
                where.append(where.length() == 0 ? "where ativo = true" : " and ativo = true");
            } else {
                where.append(where.length() == 0 ? "where ativo = false" : " and ativo = false");
            }
        }
        hql.append(where.toString());
        hql.append(paginadorDTO.getSQLOrderByUse());

        List<Nivel> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);

        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    public Number consultarNivelMaiorOrdem(String ctx, Integer codigo)throws ServiceException{
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT MAX(ordem) FROM Nivel obj ");
        if (codigo != null) {
            hql.append("WHERE codigo <> :codigo");
        }

        Map<String,Object> params = new HashMap<String, Object>();

        if (codigo != null) {
            params.put("codigo", codigo);
        }

        Number maiorOrdem = null;
        try {
            maiorOrdem = numberWithParam(ctx, hql.toString(), new StringBuilder(), params);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEL_MAIOR_ORDEM, e);
        }
        return maiorOrdem;
    }
}