/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.programa;

import br.com.pacto.bean.programa.ObjetivoAluno;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.programa.ObjetivoAlunoDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ObjetivoAlunoDaoImpl extends DaoGenericoImpl<ObjetivoAluno, Integer> implements
        ObjetivoAlunoDao {
    @Override
    public List<ObjetivoAluno> findByMatriculaAlunoEStatus(String ctx, Integer matricula, Integer status, Boolean primario) throws Exception {
        HashMap<String, Object> p = new HashMap<String, Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM ObjetivoAluno obj ");
        sql.append("WHERE obj.clienteSintetico.matricula = :matricula ");

        if (status != null) {
            sql.append("AND obj.status = :status ");
            p.put("status", status);
        }

        if (primario != null) {
            sql.append("AND obj.primario = :primario ");
            p.put("primario", primario);
        }

        p.put("matricula", matricula);

        return findByParam(ctx, sql.toString(), p);
    }
}