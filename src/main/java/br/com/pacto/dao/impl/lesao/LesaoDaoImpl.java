package br.com.pacto.dao.impl.lesao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.Lesao;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.lesao.LesaoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class LesaoDaoImpl extends DaoGenericoImpl<Lesao,Integer> implements LesaoDao {
    private static final int MAXIMO_LESAO_LISTAR = 50;


    @Override
    public List<Lesao> listarLesao(String ctx, FiltroLesaoJSON filtroLesaoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_LESAO_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_LESAO_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("SELECT obj FROM Lesao obj ");
        if ((filtroLesaoJSON.getParametro() != null) && (!filtroLesaoJSON.getParametro().trim().equals(""))) {
            if (where.length() == 0) {
                where.append(" where upper(obj.cliente.codigo) like :parametro");
                param.put("parametro", filtroLesaoJSON.getParametro().toUpperCase() + "%");
            }
        }

        if (where.length() > 0) {
            hql.append(where.toString());
        }
        if (paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty()) {
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<Lesao> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    @Override
    public List<Lesao> consultarLesaoPorCliente(Integer codigoCliente, String ctx) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Lesao obj ");
        hql.append("WHERE obj.cliente.codigo = :codigoCliente");
        Map<String, Object> param = Collections.singletonMap("codigoCliente", codigoCliente);
        return findByParam(ctx, hql.toString(), param);
    }
}
