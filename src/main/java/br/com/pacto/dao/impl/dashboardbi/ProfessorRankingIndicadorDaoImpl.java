package br.com.pacto.dao.impl.dashboardbi;

import br.com.pacto.bean.bi.ProfessorRankingIndicador;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.dashboardbi.ProfessorRankingIndicadorDao;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;


@Repository
public class ProfessorRankingIndicadorDaoImpl extends DaoGenericoImpl<ProfessorRankingIndicador, Integer>
        implements ProfessorRankingIndicadorDao {

    @Override
    public List<ProfessorRankingIndicador> obterIndicadoresPorProfessorRanking(String chave, Integer professorRankingId) throws Exception {
        EntityManager entityManager = getEntityManager(chave);

        String hql = "FROM ProfessorRankingIndicador pri WHERE pri.professorRanking.codigo = :codigo";
        Query query = entityManager.createQuery(hql);
        query.setParameter("codigo", professorRankingId);

        return query.getResultList();
    }
}
