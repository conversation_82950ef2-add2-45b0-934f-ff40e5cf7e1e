/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.HistoricoNivelCliente;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.HistoricoNivelClienteDao;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public class HistoricoNivelClienteDaoImpl extends DaoGenericoImpl<HistoricoNivelCliente, Integer> implements HistoricoNivelClienteDao {

}