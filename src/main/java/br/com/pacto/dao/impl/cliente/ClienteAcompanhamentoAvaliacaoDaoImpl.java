package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteAcompanhamentoAvaliacao;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoAvaliacaoDao;
import org.springframework.stereotype.Repository;

import br.com.pacto.controller.json.gestao.AvaliacaoAgrupadaDTO;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ClienteAcompanhamentoAvaliacaoDaoImpl extends DaoGenericoImpl<ClienteAcompanhamentoAvaliacao, Integer> implements
        ClienteAcompanhamentoAvaliacaoDao {

    @Override
    public ClienteAcompanhamentoAvaliacao findByAcompanhamentoId(String ctx, Integer acompanhamentoId) throws Exception {
        return findObjectByAttribute(ctx, "clienteAcompanhamento.codigo", acompanhamentoId);
    }

    @Override
    public List<AvaliacaoAgrupadaDTO> contarAvaliacoesAgrupadasPorNota(String ctx, Integer idProfessor, Integer empresaId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT aval.nota, COUNT(aval.codigo) AS total ");
        sql.append("FROM clienteacompanhamentoavaliacao aval ");
        sql.append("JOIN clienteacompanhamento ca ON aval.cliente_acompanhamento_codigo = ca.codigo ");
        sql.append("JOIN clientesintetico cs ON ca.cliente_codigo = cs.codigo ");
        sql.append("WHERE cs.empresa = ").append(empresaId).append(" ");

        if (idProfessor != null && idProfessor > 0) {
            sql.append("AND cs.professorsintetico_codigo = ").append(idProfessor).append(" ");
        }

        sql.append("GROUP BY aval.nota");

        List<AvaliacaoAgrupadaDTO> resultados = new ArrayList<>();
        try (ResultSet rs = createStatement(ctx, sql.toString())) {

            while (rs.next()) {
                Integer nota = rs.getInt("nota");
                Long total = rs.getLong("total");
                resultados.add(new AvaliacaoAgrupadaDTO(nota, total));
            }
        }

        return resultados;
    }


}
