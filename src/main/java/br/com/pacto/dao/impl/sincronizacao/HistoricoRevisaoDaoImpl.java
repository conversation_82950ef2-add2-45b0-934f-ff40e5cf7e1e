/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.sincronizacao;

import br.com.pacto.bean.sincronizacao.HistoricoRevisao;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.sincronizacao.HistoricoRevisaoDao;
import br.com.pacto.objeto.Calendario;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Repository
public class HistoricoRevisaoDaoImpl extends DaoGenericoImpl<HistoricoRevisao, Integer> implements
        HistoricoRevisaoDao {

    public void saveHistoricoRevisao(TipoRevisaoEnum tipoRevisao, TipoClassSincronizarEnum tipoClassEnum, String key, Usuario u, Integer pk) {
        try {
            List<HistoricoRevisao> lista = findByParam(key, new StringBuilder(
                    String.format("WHERE chaveprimaria = %s and entidade = %s ",
                            pk, tipoClassEnum.getParent().getId())), null);
            if (lista != null && !lista.isEmpty()) {
                HistoricoRevisao h = lista.get(0);
                h.setDataRegistro(Calendario.hoje());
                h.setTipoRevisao(tipoRevisao);
                h.setUserName(u != null ? u.getUserName() : null);
                updateNoFlush(key, h);
            } else {
                HistoricoRevisao h = new HistoricoRevisao();
                h.setChavePrimaria(pk);
                h.setDataRegistro(Calendario.hoje());
                h.setEntidade(tipoClassEnum.getParent());
                h.setTipoRevisao(tipoRevisao);
                h.setUserName(u != null ? u.getUserName() : null);
                insertNoFlush(key, h);
            }
        } catch (Exception ex) {
            Logger.getLogger(HistoricoRevisaoDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}