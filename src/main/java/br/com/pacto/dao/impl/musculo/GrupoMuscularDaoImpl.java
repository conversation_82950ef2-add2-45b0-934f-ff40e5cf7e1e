/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.GrupoMuscularExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class GrupoMuscularDaoImpl extends DaoGenericoImpl<GrupoMuscular, Integer> implements
        GrupoMuscularDao {

    private static final int MAXIMO_GRUPO_MUSCULAR_CONSULTAR = 50;

    public List<GrupoMuscularResumidoResponseTO> consultarGruposMusculares(final String ctx, FiltroGrupoMuscularJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException{
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_GRUPO_MUSCULAR_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_GRUPO_MUSCULAR_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<GrupoMuscularResumidoResponseTO> listaRet = new ArrayList<GrupoMuscularResumidoResponseTO>();
        hql.append("SELECT obj FROM GrupoMuscular obj ");
        Map<String, Object> param = new HashMap<>();
        if ((filtros.getParametro() != null) && (!filtros.getParametro().trim().equals(""))) {
            where.append("where upper(obj.nome) like :parametro");
            param.put("parametro", "%" + filtros.getParametro().toUpperCase() + "%");
        }
        if (where.length() > 0){
            hql.append(where.toString());
        }
        if(paginadorDTO.getSortMap() != null && paginadorDTO.getSortMap().isEmpty() == false) {
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        List<GrupoMuscular> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPOS_MUSCULARES, e);
        }
        if (lista != null) {
            for (GrupoMuscular grupoMuscular : lista) {
                listaRet.add(new GrupoMuscularResumidoResponseTO(grupoMuscular));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return listaRet;
    }


}
