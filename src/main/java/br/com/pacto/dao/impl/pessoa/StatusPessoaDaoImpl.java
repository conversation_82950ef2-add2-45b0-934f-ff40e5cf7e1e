/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.pessoa;

import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.pessoa.StatusPessoaDao;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public class StatusPessoaDaoImpl extends DaoGenericoImpl<StatusPessoa, Integer> implements StatusPessoaDao {

    @Override
    public void removerPorUsuario(String ctx, Usuario usuario) throws Exception {
        String hql = "DELETE FROM StatusPessoa s WHERE s.usuario.codigo = ?;";
        getEntityManager(ctx).createQuery(hql).setParameter(0, usuario.getCodigo()).executeUpdate();
    }
}