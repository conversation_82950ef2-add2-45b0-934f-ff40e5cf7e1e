/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.dashboardbi;

import br.com.pacto.bean.bi.TipoEventoDisponibilidadeBI;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.dashboardbi.TipoEventoDisponibilidadeBIDao;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public class TipoEventoDisponibilidadeBIDaoImpl extends DaoGenericoImpl<TipoEventoDisponibilidadeBI, Integer> implements
        TipoEventoDisponibilidadeBIDao {
    
}
