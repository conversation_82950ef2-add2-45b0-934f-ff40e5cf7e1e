package br.com.pacto.dao.impl.avaliacao;

import br.com.pacto.bean.avaliacao.RespostaClienteParQ;
import br.com.pacto.bean.log.Log;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class ParQDaoImpl extends DaoGenericoImpl<RespostaClienteParQ, Integer> implements ParQDao {

    @Autowired
    private FotoService fotoService;
    @Autowired
    private AnamneseService anamneseService;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private SessaoService sessaoService;

    public ParQDaoImpl() {
    }

    public Boolean consultarParQPositivoAssinaturaDigital(String ctx, Integer codigoCliente) throws ServiceException {
        try {
            RespostaClienteParQ rcp = consultarRespostaParQPorCliente(ctx, codigoCliente);
            if (rcp == null) {
                return false;
            }
            return anamneseService.obterRespostaParQAssinaturaDigital(ctx, codigoCliente);
            } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Boolean consultarResultadoParqVigente(String ctx, Integer codigoCliente) throws Exception {
        try {
            getCurrentSession(ctx).clear();
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM RespostaClienteParQ obj WHERE obj.cliente.codigo = :codigoCliente AND ativo = TRUE");
            HashMap<String, Object> params = new HashMap<>();
            params.put("codigoCliente", codigoCliente);
            RespostaClienteParQ rcp = findFirstObjectByParam(ctx, hql.toString(), params);
            if (rcp != null) {
                return rcp.getParqPositivo();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao consultar resultado do parq vigente: " + e.getMessage());
        }
        return false;
    }

    public JSONArray consultarClientesParQPositivo(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception {
        // Consulta clientes com parq positivo que assinaram o formulário da assinatura digital
        StringBuilder sql = new StringBuilder();
        sql.append("WITH MaxCodigoPorCliente AS ( \n");
        sql.append("    SELECT cliente_codigo, MAX(codigo) AS max_codigo \n");
        sql.append("    FROM respostaclienteparq \n");
        sql.append("    GROUP BY cliente_codigo \n");
        sql.append(") \n");
        sql.append("SELECT DISTINCT(cs.codigo), rcp.codigo AS codigorespostaclienteparq, cs.nome, cs.matricula, cs.codigopessoa, p.fotokey, rcp.dataresposta, rcp.urlassinatura \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("INNER JOIN MaxCodigoPorCliente mcp ON mcp.cliente_codigo = cs.codigo \n");
        sql.append("INNER JOIN respostaclienteparq rcp ON rcp.codigo = mcp.max_codigo \n");
        sql.append("INNER JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rc.resposta = 'SIM' \n");
        sql.append("AND rcp.urlassinatura IS NOT NULL \n");
        sql.append("AND rcp.urlassinatura <> '' \n");

        if (!nomeOuMatricula.equals("") && nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ").append(nomeOuMatricula).append(" \n");
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
        }

        sql.append("ORDER BY cs.nome \n");
        sql.append("LIMIT 10");
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        JSONArray jsonArray;
        try (ResultSet rs = stm.executeQuery()) {
            jsonArray = new JSONArray();
            while (rs.next()) {
                JSONObject jsonCliente = new JSONObject();
                jsonCliente.put("codigorespostaclienteparq", rs.getInt("codigorespostaclienteparq"));
                jsonCliente.put("assinado", true);
                jsonCliente.put("assinadoem", rs.getString("dataresposta"));
                jsonCliente.put("nome", rs.getString("nome"));
                jsonCliente.put("matricula", rs.getInt("matricula"));
                jsonCliente.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                jsonCliente.put("urlassinatura", rs.getString("urlassinatura"));
                jsonArray.put(jsonCliente);
            }
        }

        return jsonArray;
    }

    public JSONArray consultarClientesParQPositivoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT(cs.codigo), rcp.codigo AS codigorespostaclienteparq, cs.nome, cs.matricula, cs.codigopessoa, p.fotokey, rcp.dataresposta, rcp.urlassinatura \n");
        sql.append("FROM respostaclienteparq rcp \n");
        sql.append("INNER JOIN clientesintetico cs ON cs.codigo = rcp.cliente_codigo \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rcp.ativo = TRUE \n");
        sql.append("AND rcp.parqpositivo = TRUE \n");

        if (!nomeOuMatricula.equals("") && nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ").append(nomeOuMatricula).append(" \n");
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
        }

        sql.append("ORDER BY cs.nome \n");
        sql.append("LIMIT 10");
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        JSONArray jsonArray;
        try (ResultSet rs = stm.executeQuery()) {
            jsonArray = new JSONArray();
            while (rs.next()) {
                JSONObject jsonCliente = new JSONObject();
                jsonCliente.put("codigorespostaclienteparq", rs.getInt("codigorespostaclienteparq"));
                jsonCliente.put("assinado", true);
                jsonCliente.put("assinadoem", rs.getString("dataresposta"));
                jsonCliente.put("nome", rs.getString("nome"));
                jsonCliente.put("matricula", rs.getInt("matricula"));
                jsonCliente.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                jsonCliente.put("urlassinatura", rs.getString("urlassinatura"));
                jsonArray.put(jsonCliente);
            }
        }

        return jsonArray;
    }

    public RespostaClienteParQ consultarRespostaParQPorCliente(String ctx, Integer codigoCliente) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM RespostaClienteParQ obj WHERE obj.cliente.codigo = :codigoCliente AND obj.ativo = TRUE");
        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoCliente", codigoCliente);
        return findFirstObjectByParam(ctx, hql.toString(), params);
    }

    public List<RespostaClienteParQ> consultarTodosPorCliente(String ctx, Integer codigoCliente) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM RespostaClienteParQ obj WHERE obj.cliente.codigo = :codigoCliente");
        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoCliente", codigoCliente);
        return findByParam(ctx, hql.toString(), params);
    }

    public RespostaClienteParQ consultarRespostaParQPorCodigo(String ctx, Integer codigoRespostaParq) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM RespostaClienteParQ obj WHERE obj.codigo = :codigoRespostaParq");
        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoRespostaParq", codigoRespostaParq);
        return findFirstObjectByParam(ctx, hql.toString(), params);
    }

    public JSONArray consultarClientesParQAssinado(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT rc.codigo AS codigorespostaclienteparq, rc.dataresposta, cs.codigo AS codigocliente, cs.nome, cs.matricula, p.fotokey, cs.codigopessoa \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN respostaclienteparq rc ON rc.cliente_codigo = cs.codigo \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("WHERE cs.empresa = ? \n");
        sql.append("AND rc.urlassinatura IS NOT NULL \n");
        sql.append("AND rc.urlassinatura <> '' \n");
        sql.append("AND rc.dataresposta = (SELECT MAX(rc2.dataresposta) \n");
        sql.append("FROM respostaclienteparq rc2 \n");
        sql.append("WHERE rc2.cliente_codigo = rc.cliente_codigo) \n");

        // Prepare parameter list
        List<Object> parameters = new ArrayList<>();
        parameters.add(empresaZw);

        if (!nomeOuMatricula.equals("") & nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ? \n");
            parameters.add(Integer.parseInt(nomeOuMatricula));
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER(?) \n");
            parameters.add("%" + nomeOuMatricula + "%");
        }
        if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
            sql.append("AND rc.dataresposta + INTERVAL '1 day' * ? < current_date \n");
            parameters.add(diasParaVencimentoParq);
        }
        sql.append("ORDER BY cs.nome \n");
        sql.append("LIMIT 10");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        // Set parameters
        for (int i = 0; i < parameters.size(); i++) {
            stm.setObject(i + 1, parameters.get(i));
        }

        JSONArray parqAssinado;
        try (ResultSet rs = stm.executeQuery()) {
            parqAssinado = new JSONArray();
            while (rs.next()) {
                JSONObject jsonClienteAssinado = new JSONObject();
                jsonClienteAssinado.put("codigorespostaclienteparq", rs.getInt("codigorespostaclienteparq"));
                jsonClienteAssinado.put("assinadoem", rs.getString("dataresposta"));
                jsonClienteAssinado.put("codigocliente", rs.getInt("codigocliente"));
                jsonClienteAssinado.put("nome", rs.getString("nome"));
                jsonClienteAssinado.put("matricula", rs.getInt("matricula"));
                jsonClienteAssinado.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
                    jsonClienteAssinado.put("vencidoem", Calendario.getData(Uteis.somarDias(Uteis.getDate(rs.getString("dataresposta"), "yyyy-MM-dd HH:mm:ss"), diasParaVencimentoParq), "yyyy-MM-dd HH:mm:ss"));
                }
                parqAssinado.put(jsonClienteAssinado);
            }
        }
        return parqAssinado;
    }

    public JSONArray consultarClientesParQAssinadoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT rc.codigo AS codigorespostaclienteparq, rc.dataresposta, cs.codigo AS codigocliente, cs.nome, cs.matricula, p.fotokey, cs.codigopessoa \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN respostaclienteparq rc ON rc.cliente_codigo = cs.codigo \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rc.ativo = TRUE \n");

        if (!nomeOuMatricula.equals("") & nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ").append(nomeOuMatricula).append(" \n");
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
        }
        if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
            sql.append("AND rc.dataresposta + INTERVAL '").append(diasParaVencimentoParq).append(" days' < current_date \n");
        }
        sql.append("ORDER BY cs.nome \n");
        sql.append("LIMIT 10");
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        JSONArray parqAssinado;
        try (ResultSet rs = stm.executeQuery()) {
            parqAssinado = new JSONArray();
            while (rs.next()) {
                JSONObject jsonClienteAssinado = new JSONObject();
                jsonClienteAssinado.put("codigorespostaclienteparq", rs.getInt("codigorespostaclienteparq"));
                jsonClienteAssinado.put("assinadoem", rs.getString("dataresposta"));
                jsonClienteAssinado.put("codigocliente", rs.getInt("codigocliente"));
                jsonClienteAssinado.put("nome", rs.getString("nome"));
                jsonClienteAssinado.put("matricula", rs.getInt("matricula"));
                jsonClienteAssinado.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
                    jsonClienteAssinado.put("vencidoem", Calendario.getData(Uteis.somarDias(Uteis.getDate(rs.getString("dataresposta"), "yyyy-MM-dd HH:mm:ss"), diasParaVencimentoParq), "yyyy-MM-dd HH:mm:ss"));
                }
                parqAssinado.put(jsonClienteAssinado);
            }
        }
        return parqAssinado;
    }

    public Integer consultarNrTotalClientesParQAssinado(String ctx, Integer empresaZw, Integer diasParaVencimentoParq) throws Exception {
        Integer nrTotalAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(distinct (cs.codigo)) AS nrtotal \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN respostaclienteparq rc ON rc.cliente_codigo = cs.codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rc.urlassinatura IS NOT NULL \n");
        sql.append("AND rc.urlassinatura <> '' \n");
        if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
            sql.append("AND rc.dataresposta + INTERVAL '").append(diasParaVencimentoParq).append(" days' < current_date \n");
        }
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        try (ResultSet rs = stm.executeQuery()) {
            if (rs.next()) {
                nrTotalAssinados = rs.getInt("nrtotal");
            }
        }
        return nrTotalAssinados;
    }

    public Integer consultarNrTotalClientesParQAssinadoV2(String ctx, Integer empresaZw, Integer diasParaVencimentoParq) throws Exception {
        Integer nrTotalAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(distinct (cs.codigo)) AS nrtotal \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN respostaclienteparq rc ON rc.cliente_codigo = cs.codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rc.ativo = TRUE \n");
        if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
            sql.append("AND rc.dataresposta + INTERVAL '").append(diasParaVencimentoParq).append(" days' < current_date \n");
        }
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        try (ResultSet rs = stm.executeQuery()) {
            if (rs.next()) {
                nrTotalAssinados = rs.getInt("nrtotal");
            }
        }
        return nrTotalAssinados;
    }

    public JSONArray consultarClientesParQNaoAssinado(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cs.codigo AS codigocliente, cs.nome, cs.matricula, p.fotokey, cs.codigopessoa, rc.codigo AS codigorespostaclienteparq \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("LEFT JOIN LATERAL ( \n");
        sql.append("    SELECT rc.codigo, rc.dataresposta, rc.urlassinatura \n");
        sql.append("    FROM respostaclienteparq rc \n");
        sql.append("    WHERE rc.cliente_codigo = cs.codigo \n");
        sql.append("    ORDER BY rc.dataresposta DESC \n");
        sql.append("    LIMIT 1 \n");
        sql.append(") rc ON true \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND (rc.urlassinatura IS NULL OR TRIM(rc.urlassinatura) = '') \n");

        if (!nomeOuMatricula.equals("") & nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ").append(nomeOuMatricula).append(" \n");
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
        }
        sql.append("ORDER BY cs.nome \n");
        sql.append("LIMIT 10");
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        JSONArray parqAssinado;
        try (ResultSet rs = stm.executeQuery()) {
            parqAssinado = new JSONArray();
            while (rs.next()) {
                JSONObject jsonClienteAssinado = new JSONObject();
                jsonClienteAssinado.put("codigocliente", rs.getInt("codigocliente"));
                jsonClienteAssinado.put("nome", rs.getString("nome"));
                jsonClienteAssinado.put("matricula", rs.getInt("matricula"));
                jsonClienteAssinado.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                parqAssinado.put(jsonClienteAssinado);
            }
        }
        return parqAssinado;
    }

    public JSONArray consultarClientesParQNaoAssinadoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, String todos) throws Exception {
        boolean todosFlag = "naoassinadosParQ".equalsIgnoreCase(todos);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT cs.codigo AS codigocliente, cs.nome, cs.matricula, p.fotokey, cs.codigopessoa, MAX(rcp.codigo) AS codigorespostaclienteparq \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("LEFT JOIN respostaclienteparq rcp ON rcp.cliente_codigo = cs.codigo  \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");

        if (!nomeOuMatricula.equals("") & nomeOuMatricula.matches("[0-9]*")) {
            sql.append("AND cs.matricula = ").append(nomeOuMatricula).append(" \n");
        } else if (!nomeOuMatricula.equals("")) {
            sql.append("AND UPPER(cs.nome) LIKE UPPER('%").append(nomeOuMatricula).append("%') \n");
        }

        sql.append("GROUP BY cs.codigo, cs.nome, cs.matricula, p.fotokey, cs.codigopessoa \n");
        sql.append("HAVING COUNT(rcp.codigo) = 0 OR (SUM(CASE WHEN rcp.ativo = TRUE THEN 1 ELSE 0 END) = 0) \n");

        sql.append("ORDER BY cs.nome \n");
        if (!todosFlag) {
            sql.append("LIMIT 10");
        }
        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        JSONArray parqAssinado;
        try (ResultSet rs = stm.executeQuery()) {
            parqAssinado = new JSONArray();
            while (rs.next()) {
                JSONObject jsonClienteAssinado = new JSONObject();
                jsonClienteAssinado.put("codigocliente", rs.getInt("codigocliente"));
                jsonClienteAssinado.put("nome", rs.getString("nome"));
                jsonClienteAssinado.put("matricula", rs.getInt("matricula"));
                jsonClienteAssinado.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
                parqAssinado.put(jsonClienteAssinado);
            }
        }
        return parqAssinado;
    }

    public Integer consultarNrTotalClientesParQNaoAssinado(String ctx, Integer empresaZw) throws Exception {
        Integer nrTotalNaoAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT cs.codigo) AS nrtotal \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("LEFT JOIN LATERAL ( \n");
        sql.append("    SELECT rc.urlassinatura \n");
        sql.append("    FROM respostaclienteparq rc \n");
        sql.append("    WHERE rc.cliente_codigo = cs.codigo \n");
        sql.append("    ORDER BY rc.dataresposta DESC \n");
        sql.append("    LIMIT 1 \n");
        sql.append(") rc ON true \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND (rc.urlassinatura IS NULL OR TRIM(rc.urlassinatura) = '');");

        try (PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    nrTotalNaoAssinados = rs.getInt("nrtotal");
                }
            }
        }
        return nrTotalNaoAssinados;
    }

    public Integer consultarNrTotalClientesParQNaoAssinadoV2(String ctx, Integer empresaZw) throws Exception {
        Integer nrTotalNaoAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) AS nrtotal \n");
        sql.append("FROM ( \n");
        sql.append("    SELECT cs.codigo \n");
        sql.append("    FROM clientesintetico cs \n");
        sql.append("    INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("    LEFT JOIN respostaclienteparq rcp ON rcp.cliente_codigo = cs.codigo \n");
        sql.append("    WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("    GROUP BY cs.codigo \n");
        sql.append("    HAVING COUNT(rcp.codigo) = 0 OR (SUM(CASE WHEN rcp.ativo = TRUE THEN 1 ELSE 0 END) = 0) \n");
        sql.append(") AS subquery \n");

        try (PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    nrTotalNaoAssinados = rs.getInt("nrtotal");
                }
            }
        }
        return nrTotalNaoAssinados;
    }


    public Integer consultarNrTotalClientesParQPositivos(String ctx, Integer empresaZw) throws Exception {
        Integer nrTotalNaoAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("WITH MaxCodigoPorCliente AS ( \n");
        sql.append("    SELECT cliente_codigo, MAX(codigo) AS max_codigo \n");
        sql.append("    FROM respostaclienteparq \n");
        sql.append("    GROUP BY cliente_codigo \n");
        sql.append(") \n");
        sql.append("SELECT COUNT(DISTINCT cs.codigo) AS nrtotal \n");
        sql.append("FROM clientesintetico cs \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("INNER JOIN MaxCodigoPorCliente mcp ON mcp.cliente_codigo = cs.codigo \n");
        sql.append("INNER JOIN respostaclienteparq rcp ON rcp.codigo = mcp.max_codigo \n");
        sql.append("INNER JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rc.resposta = 'SIM' \n");
        sql.append("AND rcp.urlassinatura IS NOT NULL \n");
        sql.append("AND rcp.urlassinatura <> ''; \n");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        try (ResultSet rs = stm.executeQuery()) {
            if (rs.next()) {
                nrTotalNaoAssinados = rs.getInt("nrtotal");
            }
        }
        return nrTotalNaoAssinados;
    }

    public Integer consultarNrTotalClientesParQPositivosV2(String ctx, Integer empresaZw) throws Exception {
        Integer nrTotalNaoAssinados = 0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT cs.codigo) AS nrtotal \n");
        sql.append("FROM respostaclienteparq rcp \n");
        sql.append("INNER JOIN clientesintetico cs ON cs.codigo = rcp.cliente_codigo \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = cs.pessoa_codigo \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append(" \n");
        sql.append("AND rcp.ativo = TRUE \n");
        sql.append("AND rcp.parqpositivo = TRUE \n");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        try (ResultSet rs = stm.executeQuery()) {
            if (rs.next()) {
                nrTotalNaoAssinados = rs.getInt("nrtotal");
            }
        }
        return nrTotalNaoAssinados;
    }

    public JSONArray alunoParQValido(String ctx, Integer empresaZw, Integer codigoCliente) throws Exception {
        JSONArray resultadoFinal = new JSONArray();
        int diasParaVencimentoParq = 0;

        if (empresaZw == null) {
            empresaZw = sessaoService.getUsuarioAtual().getEmpresaAtual();
            if (empresaZw == null) {
                throw new ServiceException("Empresa não encontrada");
            }
        }
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String query = "SELECT diasparavencimentoparq FROM empresa WHERE codigo = ?";
            try (PreparedStatement stmt = conZW.prepareStatement(query)) {
                stmt.setInt(1, empresaZw);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        diasParaVencimentoParq = rs.getInt("diasparavencimentoparq");
                    }
                }
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT r.codigo, r.dataresposta, r.urlassinatura, r.cliente_codigo, r.usuario_codigo, r.dataresposta, cs.nome, cs.matricula, \n");
        sql.append("r.ativo, r.parqpositivo \n");
        sql.append("FROM respostaclienteparq r  \n");
        sql.append("INNER JOIN clientesintetico cs ON cs.codigo = r.cliente_codigo  \n");
        sql.append("WHERE cs.empresa = ").append(empresaZw).append("  \n");
        sql.append("AND r.urlassinatura is not null \n");
        sql.append("AND r.urlassinatura <> '' \n");

        if (codigoCliente != null) {
            sql.append("AND cs.codigo = ").append(codigoCliente).append(" \n");
        }

        sql.append("ORDER BY r.dataresposta DESC");

        try (PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
             ResultSet rs = stm.executeQuery()) {
            while (rs.next()) {
                JSONObject jsonCliente = new JSONObject();
                int clienteCodigo = rs.getInt("cliente_codigo");
                int usuarioCodigo = rs.getInt("usuario_codigo");

                jsonCliente.put("codigo", rs.getInt("codigo"));
                jsonCliente.put("dataresposta", rs.getString("dataresposta"));
                jsonCliente.put("urlassinatura", rs.getString("urlassinatura"));
                jsonCliente.put("cliente_codigo", clienteCodigo);
                jsonCliente.put("usuario_codigo", usuarioCodigo);
                jsonCliente.put("diasparavencimentoparq", diasParaVencimentoParq);
                boolean parQPositivo = rs.getBoolean("parqpositivo");
                if (rs.getString("dataresposta") != null) {
                    Date dataResposta = Uteis.getDate(rs.getString("dataresposta"), "yyyy-MM-dd");
                    Date dataVencimento = Uteis.somarDias(dataResposta, diasParaVencimentoParq);
                    if (diasParaVencimentoParq == 0) {
                        jsonCliente.put("diasparavencimentoparq", "-");
                        jsonCliente.put("parQPositivo", parQPositivo ? "Positivo" : "Assinado");
                    } else {
                        jsonCliente.put("diasparavencimentoparq", Calendario.getData(dataVencimento, "yyyy-MM-dd"));
                    }

                    if (diasParaVencimentoParq > 0 && dataVencimento.before(Calendario.hoje()) && rs.getBoolean("ativo")) {
                        jsonCliente.put("parQPositivo", "Vencido"); // é considerado inativo/vencido o parq ativo que a dataresposta + diasparavencimentoparq seja menor que a data atual
                    } else if (parQPositivo) {
                        jsonCliente.put("parQPositivo", "Positivo");
                    } else {
                        jsonCliente.put("parQPositivo", "Negativo");
                    }
                } else {
                    jsonCliente.put("diasparavencimentoparq", "-");
                    jsonCliente.put("parQPositivo", "Inativo");
                }
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    String usuarioQuery = "SELECT nome FROM usuario WHERE codigo = ?";
                    try (PreparedStatement usuarioStmt = conZW.prepareStatement(usuarioQuery)) {
                        usuarioStmt.setInt(1, usuarioCodigo);
                        try (ResultSet usuarioRs = usuarioStmt.executeQuery()) {
                            if (usuarioRs.next()) {
                                jsonCliente.put("usuario_nome", usuarioRs.getString("nome"));
                            }
                        }
                    }
                }

                resultadoFinal.put(jsonCliente);
            }
        }

        return resultadoFinal;
    }

    public void gravarLogParq(String ctx, Log log) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, chaveprimariaentidadesubordinada,\n");
        sql.append(" nomecampo, valorcampoanterior, valorcampoalterado, \n");
        sql.append(" dataalteracao, responsavelalteracao, operacao, pessoa, cliente)  \n");
        sql.append(" VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        Connection con = getConnection(ctx);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            int i = 0;
            stm.setString(++i, log.getNomeEntidade());
            stm.setString(++i, log.getNomeEntidadeDescricao());
            stm.setString(++i, log.getChavePrimaria());
            stm.setString(++i, log.getChavePrimariaEntidadeSubordinada());
            stm.setString(++i, log.getNomeCampo());
            stm.setString(++i, log.getValorCampoAnterior());
            stm.setString(++i, log.getValorCampoAlterado());
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setString(++i, log.getResponsavelAlteracao());
            stm.setString(++i, log.getOperacao());
            stm.setInt(++i, log.getPessoa());
            stm.setInt(++i, log.getCliente());
            stm.execute();
        }
    }

}
