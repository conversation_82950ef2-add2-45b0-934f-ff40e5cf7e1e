/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ClienteAcompanhamentoDaoImpl extends DaoGenericoImpl<ClienteAcompanhamento, Integer> implements
        ClienteAcompanhamentoDao {
    @Override
    public ClienteAcompanhamento buscarUltimoAcompanhamentoDoCliente(String ctx, Integer codigoCliente, Integer codigoProfessor) {
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM ClienteAcompanhamento obj ")
                .append("WHERE obj.cliente.codigo = :codigoCliente ")
                .append("AND obj.professor.codigo = :codigoProfessor ")
                .append("ORDER BY obj.inicio DESC");

        Map<String, Object> params = new HashMap<>();
        params.put("codigoCliente", codigoCliente);
        params.put("codigoProfessor", codigoProfessor);

        try {
            List<ClienteAcompanhamento> resultados = findByParam(ctx, hql.toString(), params, 1);
            return resultados.isEmpty() ? null : resultados.get(0);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao buscar último acompanhamento do cliente", e);
        }
    }
}
