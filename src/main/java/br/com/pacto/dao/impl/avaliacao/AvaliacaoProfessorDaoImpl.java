package br.com.pacto.dao.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoProfessor;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoProfessorDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.NivelExcecoes;
import br.com.pacto.util.MensagemUtils;
import org.hibernate.impl.SessionFactoryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityExistsException;
import java.sql.Connection;
import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON> on 09/10/2016.
 */
@Repository
public class AvaliacaoProfessorDaoImpl extends DaoGenericoImpl<AvaliacaoProfessor, Integer> implements AvaliacaoProfessorDao {

    @Autowired
    private MensagemUtils mensagensUtil;

    public Connection getConnection(final String ctx) throws Exception {
        SessionFactoryImpl sessionFactory = (SessionFactoryImpl) getCurrentSession(ctx).getSessionFactory();
        Connection conn = sessionFactory.getConnectionProvider().getConnection();
        try {
            return conn;
        } catch (EntityExistsException e) {
            throw new Exception(mensagensUtil.tratarMensagemUniqueConstraint(e));
        }
    }

    @Override
    public List<AvaliacaoProfessor> findByCodProfessor(String codProfessores, String ctx) throws Exception {
        HashMap<String, Object> param = new HashMap<String, Object>();
        getCurrentSession(ctx).clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM AvaliacaoProfessor obj ");
        where.append(" where obj.professorSintetico.codigo IN (").append(codProfessores).append(")");
        hql.append(where);

        List<AvaliacaoProfessor> lista = null;
        try {
            lista = findByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);

        }

        return lista;
    }

    @Override
    public List<AvaliacaoProfessor> findByCodUsuario(String ctx, Integer codUsuario) throws ServiceException {
        HashMap<String, Object> param = new HashMap<String, Object>();
        getCurrentSession(ctx).clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM AvaliacaoProfessor obj ");
        where.append(" where obj.usuario.codigo = :codUsuario");
        param.put("codUsuario", codUsuario);
        hql.append(where);

        List<AvaliacaoProfessor> lista = null;
        try {
            lista = findByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);

        }

        return lista;
    }

    @Override
    public List<AvaliacaoProfessor> findByCodProfessorECodUsuario(String ctx, Integer codUsuario, Integer codProfessor) throws ServiceException {
        HashMap<String, Object> param = new HashMap<String, Object>();
        getCurrentSession(ctx).clear();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM AvaliacaoProfessor obj ");
        where.append(" where obj.usuario.codigo = :codUsuario and obj.professorSintetico.codigo = :codProfessor");
        param.put("codUsuario", codUsuario);
        param.put("codProfessor", codProfessor);
        hql.append(where);

        List<AvaliacaoProfessor> lista = null;
        try {
            lista = findByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);

        }

        return lista;
    }
}