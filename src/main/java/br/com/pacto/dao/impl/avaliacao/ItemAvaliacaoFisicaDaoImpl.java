/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.avaliacao;

import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoFisicaDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ItemAvaliacaoFisicaDaoImpl extends DaoGenericoImpl<ItemAvaliacaoFisica, Integer> implements ItemAvaliacaoFisicaDao {

    @Override
    public ItemAvaliacaoFisica obterItemAvaliacaoPorAnamnese(String ctx, int anamneseId) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM ItemAvaliacaoFisica  obj WHERE obj.anamnese.codigo = :anamneseId");
        HashMap<String, Object> params = new HashMap<>();
        params.put("anamneseId", anamneseId);
        return findFirstObjectByParam(ctx, hql.toString(), params);
    }
}
