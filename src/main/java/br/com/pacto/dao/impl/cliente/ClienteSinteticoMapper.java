package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.service.intf.gestao.ResultSetMapper;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ClienteSinteticoMapper implements ResultSetMapper<ClienteSintetico> {

    @Override
    public ClienteSintetico map(ResultSet rs) throws SQLException {
        ClienteSintetico clienteSintetico = new ClienteSintetico();
        clienteSintetico.setCodigo(rs.getInt("codigo"));
        clienteSintetico.setDia(rs.getDate("dia"));
        clienteSintetico.setNome(rs.getString("nome"));
        clienteSintetico.setCodigoCliente(rs.getInt("codigocliente"));
        clienteSintetico.setCodigoContrato(rs.getInt("codigocontrato"));
        clienteSintetico.setCodigoPessoa(rs.getInt("pessoa_codigo"));
        clienteSintetico.setColaboradores(rs.getString("colaboradores"));
        clienteSintetico.setDataNascimento(rs.getDate("datanascimento"));
        clienteSintetico.setEmail(rs.getString("email"));
        clienteSintetico.setMatricula(rs.getInt("matricula"));
        clienteSintetico.setSexo(rs.getString("sexo"));
        clienteSintetico.setIdade(rs.getInt("idade"));
        clienteSintetico.setProfissao(rs.getString("profissao"));
        clienteSintetico.setSituacao(rs.getString("situacao"));
        clienteSintetico.setDuracaoContratoMeses(rs.getInt("duracaocontratomeses"));
        clienteSintetico.setDescricaoDuracao(rs.getString("descricaoduracao"));
        clienteSintetico.setMnemonicoDoContrato(rs.getString("mnemonicodocontrato"));
        clienteSintetico.setNomeplano(rs.getString("nomeplano"));
        clienteSintetico.setValorfaturadocontrato(rs.getDouble("valorfaturadocontrato"));
        clienteSintetico.setValorPagoContrato(rs.getDouble("valorpagocontrato"));
        clienteSintetico.setValorParcAbertoContrato(rs.getDouble("valorparcabertocontrato"));
        clienteSintetico.setSaldoContaCorrenteCliente(rs.getDouble("saldocontacorrentecliente"));
        clienteSintetico.setDataVigenciaDe(rs.getDate("datavigenciade"));
        clienteSintetico.setDataVigenciaAte(rs.getDate("datavigenciaate"));
        clienteSintetico.setDataVigenciaAteAjustada(rs.getDate("datavigenciaateajustada"));
        clienteSintetico.setDataLancamentoContrato(rs.getDate("datalancamentocontrato"));
        clienteSintetico.setDataRenovacaoContrato(rs.getDate("datarenovacaocontrato"));
        clienteSintetico.setDataRematriculaContrato(rs.getDate("datarematriculacontrato"));
        clienteSintetico.setDataUltimoBV(rs.getDate("dataultimobv"));
        clienteSintetico.setDataMatricula(rs.getDate("datamatricula"));
        clienteSintetico.setDataUltimarematricula(rs.getDate("dataultimarematricula"));
        clienteSintetico.setDiasAssiduidadeUltRematriculaAteHoje(rs.getInt("diasassiduidadeultrematriculaatehoje"));
        clienteSintetico.setDiasFaltaSemAcesso(rs.getInt("diasfaltasemacesso"));
        clienteSintetico.setDataUltimoacesso(rs.getDate("dataultimoacesso"));
        clienteSintetico.setFaseAtualCRM(rs.getString("faseatualcrm"));
        clienteSintetico.setDataUltimoContatoCRM(rs.getDate("dataultimocontatocrm"));
        clienteSintetico.setResponsavelUltimoContatoCRM(rs.getString("responsavelultimocontatocrm"));
        clienteSintetico.setCodigoUltimoContatoCRM(rs.getInt("codigoultimocontatocrm"));
        clienteSintetico.setSituacaoContrato(rs.getString("situacaocontrato"));
        clienteSintetico.setTipoPeriodoAcesso(rs.getString("tipoperiodoacesso"));
        clienteSintetico.setDataInicioPeriodoAcesso(rs.getDate("datainicioperiodoacesso"));
        clienteSintetico.setDataFimPeriodoAcesso(rs.getDate("datafimperiodoacesso"));
        clienteSintetico.setDiasAcessoSemanaPassada(rs.getInt("diasacessosemanapassada"));
        clienteSintetico.setDiasAcessoSemana2(rs.getInt("diasacessomes2"));
        clienteSintetico.setDiasAcessoSemana3(rs.getInt("diasacessomes3"));
        clienteSintetico.setDiasAcessoSemana4(rs.getInt("diasacessomes4"));
        clienteSintetico.setVezesPorSemana(rs.getInt("vezesporsemana"));
        clienteSintetico.setDiasAcessoUltimoMes(rs.getInt("diasacessoultimomes"));
        clienteSintetico.setDiasAcessoMes2(rs.getInt("diasacessomes2"));
        clienteSintetico.setDiasAcessoMes3(rs.getInt("diasacessomes3"));
        clienteSintetico.setDiasAcessoMes4(rs.getInt("diasacessomes4"));
        clienteSintetico.setMediaDiasAcesso4Meses(rs.getInt("mediadiasacesso4meses"));
        clienteSintetico.setEmpresa(rs.getInt("empresa"));
        clienteSintetico.setTelefones(rs.getString("telefones"));
        clienteSintetico.setPesoRisco(rs.getInt("pesorisco"));
        clienteSintetico.setSituacaoMatriculaContrato(rs.getString("situacaomatriculacontrato"));
        clienteSintetico.setCodigoAcesso(rs.getString("codigoacesso"));
        clienteSintetico.setModalidades(rs.getString("modalidades"));
        clienteSintetico.setParq(rs.getBoolean("parq"));
        clienteSintetico.setCrossfit(rs.getBoolean("crossfit"));
        clienteSintetico.setTotalCreditoTreino(rs.getInt("totalcreditotreino"));
        clienteSintetico.setSaldoCreditoTreino(rs.getInt("saldocreditotreino"));
        clienteSintetico.setDescricoesModalidades(rs.getString("descricoesmodalidades"));
        clienteSintetico.setNomeConsulta(rs.getString("nomeconsulta"));
        clienteSintetico.setDataCadastro(rs.getDate("datacadastro"));
        clienteSintetico.setExisteParcVencidaContrato(rs.getBoolean("existeparcvencidacontrato"));
        clienteSintetico.setFreePassInicio(rs.getDate("freepassinicio"));
        clienteSintetico.setFreePassFim(rs.getDate("freepassfim"));

        return clienteSintetico;
    }
}
