/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.programa;

import br.com.pacto.bean.programa.ObjetivoIntermediarioAluno;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.programa.ObjetivoIntermediarioAlunoDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ObjetivoIntermediarioAlunoDaoImpl extends DaoGenericoImpl<ObjetivoIntermediarioAluno, Integer> implements
        ObjetivoIntermediarioAlunoDao {
    @Override
    public List<ObjetivoIntermediarioAluno> findByObjetivoAluno(String ctx, Integer codigo) throws Exception {
        HashMap<String, Object> p = new HashMap<String, Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM ObjetivoIntermediarioAluno obj ");
        sql.append("WHERE obj.objetivoaluno.codigo = :codigo ");
        p.put("codigo", codigo);

        return findByParam(ctx, sql.toString(), p);
    }
}