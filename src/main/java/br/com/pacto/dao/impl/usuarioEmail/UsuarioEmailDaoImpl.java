package br.com.pacto.dao.impl.usuarioEmail;

import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.usuarioEmail.UsuarioEmailDao;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * Created by <PERSON> on 27/12/2016.
 */
@Repository
@Qualifier(value = "usuarioEmailDao")
public class UsuarioEmailDaoImpl extends DaoGenericoImpl<UsuarioEmail, Integer> implements
        UsuarioEmailDao {

}