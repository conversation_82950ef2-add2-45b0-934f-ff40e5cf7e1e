/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.programa;

import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.FichaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.stereotype.Repository;

import javax.management.Query;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ProgramaTreinoFichaDaoImpl extends DaoGenericoImpl<ProgramaTreinoFicha, Integer> implements
        ProgramaTreinoFichaDao {

    @Override
    public List<ProgramaTreinoFicha> obterPorProgramaTreino(String ctx, Integer programaTreinoId) throws ServiceException {
        int maxResult = Integer.MAX_VALUE;
        int indiceInicial = 0;

        StringBuilder hql = new StringBuilder();
        hql.append("Select obj From ProgramaTreinoFicha obj where obj.programa.codigo = ").append(programaTreinoId);

        List<ProgramaTreinoFicha> listReturn = null;

        try {
            listReturn = findByParam(ctx, hql.toString(), new HashMap<String, Object>(), maxResult, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHAS);
        }
        return listReturn;
    }

    @Override
    public List<ProgramaTreinoFicha> obterPorFicha(String ctx, Integer fichaId) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM ProgramaTreinoFicha WHERE ficha_codigo = ").append(fichaId);

            List<ProgramaTreinoFicha> programasTreinoFicha;
            try (ResultSet rs = createStatement(ctx, sql.toString())) {
                programasTreinoFicha = new ArrayList<>();
                while (rs.next()) {
                    ProgramaTreinoFicha programaTreinoFicha = new ProgramaTreinoFicha();
                    programaTreinoFicha.setCodigo(rs.getInt("codigo"));
                    ProgramaTreinoDao programaTreinoDao = (UtilContext.getBean(ProgramaTreinoDao.class));
                    ProgramaTreino programaTreino = programaTreinoDao.obterPorId(ctx, rs.getInt("programa_codigo"));
                    programaTreinoFicha.setPrograma(programaTreino);
                    programaTreinoFicha.setFicha(new Ficha(rs.getInt("ficha_codigo")));
                    programaTreinoFicha.setTipoExecucao(TipoExecucaoEnum.valueOf(rs.getInt("tipoexecucao")));
                    programaTreinoFicha.setVersao(rs.getInt("versao"));

                    String sqlDiasSemana = scriptGetDiasSemana(programaTreinoFicha.getCodigo());
                    try (ResultSet resultSet = createStatement(ctx, sqlDiasSemana)) {
                        while (resultSet.next()) {
                            programaTreinoFicha.getDiaSemana().add(resultSet.getString("diasemana"));
                        }
                    }
                    programasTreinoFicha.add(programaTreinoFicha);
                }
            }
            return programasTreinoFicha;
        } catch (Exception ex) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO, ex);
        }
    }

    @Override
    public void atualizarLista(String ctx, List<ProgramaTreinoFicha> programaTreinoFichas) throws Exception {
        for (ProgramaTreinoFicha programaTreinoFicha : programaTreinoFichas) {
            StringBuilder script;
            if (UteisValidacao.emptyNumber(programaTreinoFicha.getCodigo())) {
                script = scriptCreate(programaTreinoFicha);
            } else {
                script = scriptUpdate(programaTreinoFicha);
            }
            try (ResultSet scriptProgramaTreinoFicha = createStatement(ctx, script.toString())) {
                if (scriptProgramaTreinoFicha.next()) {
                    programaTreinoFicha.setCodigo(scriptProgramaTreinoFicha.getInt("codigo"));
                }
            }

            String getDiasSemanas = scriptGetDiasSemana(programaTreinoFicha.getCodigo());
            try (ResultSet rs = createStatement(ctx, getDiasSemanas)) {
                if (rs.next()) {
                    deletarDiasSemana(ctx, programaTreinoFicha.getCodigo());
                }
            }
            if (programaTreinoFicha.getTipoExecucao() != null
                    && programaTreinoFicha.getTipoExecucao().equals(TipoExecucaoEnum.DIAS_SEMANA)
                    && !UteisValidacao.emptyList(programaTreinoFicha.getDiaSemana())) {
                for (String diaSemana : programaTreinoFicha.getDiaSemana()) {
                    inserirDiasSemana(ctx, programaTreinoFicha.getCodigo(), diaSemana);
                }
            }
        }
    }

    @Override
    public void atualizarProgramaTreinoFicha(String ctx, ProgramaTreinoFicha programaTreinoFicha) throws Exception {
        if(programaTreinoFicha.getVersao() == null) {
            programaTreinoFicha.setVersao(1);
        } else {
            programaTreinoFicha.setVersao(programaTreinoFicha.getVersao() + 1);
        }

        StringBuilder script;
        if (UteisValidacao.emptyNumber(programaTreinoFicha.getCodigo())) {
            script = scriptCreate(programaTreinoFicha);
        } else {
            script = scriptUpdate(programaTreinoFicha);
        }
        try (ResultSet scriptProgramaTreinoFicha = createStatement(ctx, script.toString())) {
            if (scriptProgramaTreinoFicha.next()) {
                programaTreinoFicha.setCodigo(scriptProgramaTreinoFicha.getInt("codigo"));
            }
        }

        String getDiasSemanas = scriptGetDiasSemana(programaTreinoFicha.getCodigo());
        try (ResultSet rs = createStatement(ctx, getDiasSemanas)) {
            if (rs.next()) {
                deletarDiasSemana(ctx, programaTreinoFicha.getCodigo());
            }
        }
        if (programaTreinoFicha.getTipoExecucao() != null
                && programaTreinoFicha.getTipoExecucao().equals(TipoExecucaoEnum.DIAS_SEMANA)
                && !UteisValidacao.emptyList(programaTreinoFicha.getDiaSemana())) {
            for (String diaSemana : programaTreinoFicha.getDiaSemana()) {
                inserirDiasSemana(ctx, programaTreinoFicha.getCodigo(), diaSemana);
            }
        }
    }

    private StringBuilder scriptUpdate(ProgramaTreinoFicha programaTreinoFicha) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE programatreinoficha SET ");
        sql.append("tipoexecucao = ").append(programaTreinoFicha.getTipoExecucao() != null ? programaTreinoFicha.getTipoExecucao().getId() : null).append(", ");
        sql.append("versao = ").append(programaTreinoFicha.getVersao()).append(", ");
        sql.append("ficha_codigo = ").append(programaTreinoFicha.getFicha() != null ? programaTreinoFicha.getFicha().getCodigo() : null).append(", ");
        sql.append("programa_codigo = ").append(programaTreinoFicha.getPrograma() != null ? programaTreinoFicha.getPrograma().getCodigo() : null);
        sql.append(" WHERE codigo = ").append(programaTreinoFicha.getCodigo());
        sql.append(" RETURNING codigo");

        return sql;
    }

    private StringBuilder scriptCreate(ProgramaTreinoFicha programaTreinoFicha) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO programatreinoficha(");
        sql.append("tipoexecucao, versao, ficha_codigo, programa_codigo) ");
        sql.append(" VALUES(").append(programaTreinoFicha.getTipoExecucao() != null ? programaTreinoFicha.getTipoExecucao().getId() : null).append(", ");
        sql.append(programaTreinoFicha.getVersao()).append(", ").append(programaTreinoFicha.getFicha() != null ? programaTreinoFicha.getFicha().getCodigo() : null).append(", ");
        sql.append(programaTreinoFicha.getPrograma() != null ? programaTreinoFicha.getPrograma().getCodigo() : null).append(") RETURNING codigo");

        return sql;
    }

    private void inserirDiasSemana(String ctx, Integer programaTreinoFichaCodigo, String siglaDiaSemana) throws Exception {
        String scriptInserirDiasSemana = "INSERT INTO programatreinoficha_diasemana (programatreinoficha_codigo, diasemana) VALUES (" + programaTreinoFichaCodigo + ",'" + siglaDiaSemana + "') RETURNING programatreinoficha_codigo";
        try (ResultSet statement = createStatement(ctx, scriptInserirDiasSemana)) {
            statement.close();
        }
    }

    private void deletarDiasSemana(String ctx, Integer programaTreinoFichaCodigo) throws Exception {
        String scriptDeletarDiasSemana = "DELETE FROM programatreinoficha_diasemana WHERE programatreinoficha_codigo = " + programaTreinoFichaCodigo + " RETURNING true";
        try (ResultSet statement = createStatement(ctx, scriptDeletarDiasSemana)) {
            statement.close();
        }
    }

    private String scriptGetDiasSemana(Integer programaTreinoFichaCodigo) {
        return "SELECT diasemana FROM programatreinoficha_diasemana WHERE programatreinoficha_codigo = " + programaTreinoFichaCodigo;
    }
}
