package br.com.pacto.dao.impl.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.controller.json.benchmark.FiltroBenchmarkJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.benchmark.BenchmarkDao;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import br.com.pacto.util.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * Created by Rafael on 11/07/2016.
 */
@Repository
public class BenchmarkDaoImpl extends DaoGenericoImpl<Benchmark, Integer> implements
        BenchmarkDao{

    private static final int MAXIMO_COLABORADORES_LISTAR = 50;

    @Override
    public List<Benchmark> listarBenchmarks(String ctx, FiltroBenchmarkJSON filtros, PaginadorDTO paginadorDTO)  throws ServiceException{
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_COLABORADORES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_COLABORADORES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("SELECT obj FROM Benchmark obj ");
        if ((filtros.getParametro() != null) && (!filtros.getParametro().trim().equals(""))) {
            if (where.length() == 0) {
                where.append(" WHERE ");
            } else {
                where.append(" AND ");
            }
            where.append(" upper(obj.nome) like '%").append(filtros.getParametro().toUpperCase()).append("%'");
        }
        if (!UteisValidacao.emptyList(filtros.getTipoBenchmark())) {
            if (where.length() == 0) {
                where.append(" WHERE ");
            } else {
                where.append(" AND ");
            }
            String tipoBenchmarksIds = "";
            for (Integer tipoBenchmarkId : filtros.getTipoBenchmark()) {
                tipoBenchmarksIds += "," + tipoBenchmarkId;
            }
            tipoBenchmarksIds = tipoBenchmarksIds.substring(1);
            where.append(" obj.tipoBenchmark.codigo in (").append(tipoBenchmarksIds).append(") ");
        }

        if (!UteisValidacao.emptyList(filtros.getTipoExercicios())) {
            if (where.length() == 0) {
                where.append(" WHERE ");
            } else {
                where.append(" AND ");
            }
            String exercicios = "";
            for (TipoWodEnum exercicio : filtros.getTipoExercicios()) {
                exercicios += "," + exercicio.ordinal();
            }
            exercicios = exercicios.substring(1);
            where.append(" obj.tipoWod in (").append(exercicios).append(") ");
        }


        if (where.length() > 0) {
            hql.append(where.toString());
        }
        if (paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty()) {
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<Benchmark> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    @Override
    public Benchmark consultarBenchmark(String ctx, String nome, Integer codigo) throws ServiceException {
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT obj FROM Benchmark obj");
        hql.append(" WHERE upper(obj.nome) = :nome");
        if (codigo != null) {
            hql.append(" and obj.codigo <> :codigo");
        }
        Map<String,Object> params = new HashMap<String, Object>();
        params.put("nome", nome.trim().toUpperCase());
        if (codigo != null) {
            params.put("codigo", codigo);
        }
        Benchmark benchmark = null;

        try {
            benchmark = findObjectByParam(ctx, hql.toString(), params);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
        return benchmark;
    }
}
