/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeGrupoMuscularDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeGrupoMuscularDaoImpl extends DaoGenericoImpl<AtividadeGrupoMuscular, Integer> implements
        AtividadeGrupoMuscularDao {

    @Override
    public List<AtividadeGrupoMuscular> obterPorGrupoMusculares(String ctx, List<Integer> grupoMuscularesIds) throws Exception {
        StringBuilder hql = new StringBuilder();
        String selecionados = "";
        for (Integer grupoMuscularId : grupoMuscularesIds) {
            selecionados += "," + grupoMuscularId;
        }
        selecionados = selecionados.replaceFirst(",", "");
        hql.append("WHERE obj.grupoMuscular.codigo in (").append(selecionados).append(") ");

        return findByParam(ctx, hql, new HashMap<>());
    }
}