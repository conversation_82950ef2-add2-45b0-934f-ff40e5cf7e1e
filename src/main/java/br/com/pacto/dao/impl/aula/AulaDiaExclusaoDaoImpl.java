/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Aula;
import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aulaExcluida.FiltroAulaExcluidaJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.AulaDiaExclusaoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AulaDiaExclusaoDaoImpl extends DaoGenericoImpl<AulaDiaExclusao, Integer> implements
        AulaDiaExclusaoDao {

    private static final int MAXIMO_AULAS_EXCLUIDAS_CONSULTAR = 50;
    @Autowired
    private final UsuarioService usuarioService;

    @Autowired
    public AulaDiaExclusaoDaoImpl(UsuarioService usuarioService) {
        this.usuarioService = usuarioService;
    }

    @Override
    public List<AulaDiaExclusao> obterAulasExcluidas(String ctx, Date inicio, Date fim) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        where.append("WHERE obj.dataAulaDia between :dataInicio and :dataFim ");
        params.put("dataInicio", Calendario.getDataComHoraZerada(inicio));
        params.put("dataFim", Calendario.getDataComHora(fim, "23:59:59"));

        return findByParam(ctx, where, params);
    }

    @Override
    public List<AulaDiaExclusao> consultarOrdenada(String ctx, FiltroAulaExcluidaJSON filtros, String idsHorarioTurma, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            int maxResults = MAXIMO_AULAS_EXCLUIDAS_CONSULTAR;
            int indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAXIMO_AULAS_EXCLUIDAS_CONSULTAR : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }

            if (paginadorDTO != null && paginadorDTO.getSort() == null) {
                paginadorDTO.setSort("dataExclusao,DESC");
            }

            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM AulaDiaExclusao obj ");

            StringBuilder where = new StringBuilder();
            StringBuilder whereCount = new StringBuilder();
            where.append("WHERE obj.codigoHorarioTurma in (" + idsHorarioTurma + ") ");
            whereCount.append(" WHERE obj.codigoHorarioTurma in (" + idsHorarioTurma + ") ");
            Map<String, Object> params = new HashMap<>();
            if(filtros.getInicio() != null && filtros.getFim() != null) {
                where.append("AND obj.dataAulaDia between "+Calendario.getDataComHoraZerada(filtros.getInicio())+" and "+Calendario.getDataComHora(filtros.getFim(), "23:59:59")+" ");
                whereCount.append("AND obj.dataAulaDia between '"+ Uteis.getDataHora00(filtros.getInicio())+"' and '"+Uteis.getDataAplicandoFormatacao(filtros.getFim(), "yyyy-MM-dd")+" 23:59:59' ");
                params.put("dataInicio", Calendario.getDataComHoraZerada(filtros.getInicio()));
                params.put("dataFim", Calendario.getDataComHora(filtros.getFim(), "23:59:59"));
            } else if(filtros.getInicio() != null) {
                where.append("AND obj.dataAulaDia >= :dataInicio ");
                whereCount.append("AND obj.dataAulaDia >= "+Calendario.getDataComHoraZerada(filtros.getInicio())+" ");
                params.put("dataInicio", Calendario.getDataComHoraZerada(filtros.getInicio()));
            } else if(filtros.getFim() != null) {
                where.append("AND obj.dataAulaDia <= :dataFim ");
                whereCount.append("AND obj.dataAulaDia <= "+Calendario.getDataComHora(filtros.getFim(), "23:59:59")+" ");
                params.put("dataFim", Calendario.getDataComHora(filtros.getFim(), "23:59:59"));
            }
            hql.append(where.toString());

            if (paginadorDTO != null) {
                hql.append(paginadorDTO.getSQLOrderBy());
                StringBuilder sqlCount = new StringBuilder();
                sqlCount.append("SELECT COUNT(*) FROM AulaDiaExclusao obj ");
                sqlCount.append(whereCount);
                try (ResultSet rs = createStatement(ctx, sqlCount.toString())) {
                    paginadorDTO.setQuantidadeTotalElementos(rs.next() ? rs.getLong("count") : 0L);
                }
            }

            StringBuilder sqlList = new StringBuilder();
            String[] sort = paginadorDTO.getSort().split(",");
            sqlList.append("SELECT * FROM ( ");
                sqlList.append("SELECT ROW_NUMBER() OVER(ORDER BY " + sort[0] + " " + sort[1] +") AS rowNumber, * FROM AulaDiaExclusao obj ");
                sqlList.append(whereCount);
            sqlList.append(") x WHERE x.rowNumber BETWEEN " + (indiceInicial + 1) + " AND " + (indiceInicial + maxResults));
            List<AulaDiaExclusao> lista;
            try (ResultSet rs = createStatement(ctx, sqlList.toString())) {
                lista = new ArrayList<>();
                while (rs.next()) {
                    AulaDiaExclusao item = new AulaDiaExclusao();
                    item.setCodigo(rs.getInt("codigo"));
                    item.setCodigoHorarioTurma(rs.getInt("codigohorarioturma"));
                    item.setDataAulaDia(rs.getDate("dataauladia"));
                    item.setDataExclusao(rs.getTimestamp("dataexclusao"));
                    item.setJustificativa(rs.getString("justificativa"));
                    Aula aula = new Aula();
                    aula.setCodigo(rs.getInt("aula_codigo"));
                    item.setAula(aula);
                    item.setUsuario_codigo(rs.getInt("usuario_codigo"));
                    lista.add(item);
                }
            }

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}
