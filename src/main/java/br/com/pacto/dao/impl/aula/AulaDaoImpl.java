/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Aula;
import br.com.pacto.controller.json.aulaDia.FiltroAulasJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.AulaDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.AgendadoJSON;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AulaDaoImpl extends DaoGenericoImpl<Aula, Integer> implements
        AulaDao {

    @Override
    public List<Aula> aulasDiaSemana(String ctx, String dia) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        hql.append("select obj from Aula obj where obj.diasSemana like CONCAT('%',:dia,'%')");
        Query query = getEntityManager(ctx).createQuery(hql.toString());
        query.setParameter("dia", dia);
        List<Aula> result = query.getResultList();
        List<Aula> aulas = new ArrayList<Aula>();
//        // TODO: tem um problema de inicialização (org.hibernate.LazyInitializationException: failed to lazily initialize a collection of role)
        for (Aula aula : result) {
            Hibernate.initialize(aula.getHorarios());
            aulas.add(aula);
        }
        return result;
    }

    @Override
    public List<AgendadoJSON> obterAlunosPorAulaHorario(String ctx, Date dia, Integer codigo) throws Exception {
        getCurrentSession(ctx).clear();
        StringBuilder sql = new StringBuilder();
        sql.append(" select " );
        sql.append("    pes.nome, ");
        sql.append("    pes.fotokey, " );
        sql.append("    cli.matricula, ");
        sql.append("    cli.sexo, ");
        sql.append("    pes.codigo as codigopessoa,  ");
        sql.append("    cli.codigo as codigocliente, ");
        sql.append("    aht.codigo as codigoagendamento, ");
        sql.append("    aht.inicio, ");
        sql.append("    aht.fim, ");
        sql.append("    aa.presencaconfirmada as confirmado, ");
        sql.append("    ARRAY_TO_STRING(ARRAY(select telefone from telefone where pessoa_codigo = cli.pessoa_codigo), ';') as telefones");
        sql.append(" from aulahorario aht ");
        sql.append("        inner join aulaaluno aa on aa.horario_codigo = aht.codigo ");
        sql.append("        inner join clientesintetico cli on cli.codigo = aa.cliente_codigo ");
        sql.append("        inner join pessoa pes on cli.pessoa_codigo = pes.codigo ");
        sql.append("    where aa.dia = ? and aht.codigo = ? order by pes.nome ");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());
        stm.setDate(1, Uteis.getDataJDBC(dia));
        stm.setInt(2, codigo);

        List<AgendadoJSON> alunos;
        try (ResultSet rs = stm.executeQuery()) {

            alunos = new ArrayList<AgendadoJSON>();
            while (rs.next()) {
                AgendadoJSON aluno = new AgendadoJSON();
                aluno.setNome(rs.getString("nome"));
                aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                aluno.setMatricula(rs.getString("matricula"));
                //TODO: add sexo
                //TODO: add vencimento plano
                aluno.setCodigoPessoa(rs.getInt("codigopessoa"));
                aluno.setCodigoCliente(rs.getInt("codigocliente"));
                aluno.setId_agendamento(rs.getString("codigoagendamento"));
                aluno.setInicio(rs.getString("inicio"));
                aluno.setFim(rs.getString("fim"));
                aluno.setConfirmado(rs.getBoolean("confirmado"));
                aluno.setTelefones(rs.getString("telefones"));
                alunos.add(aluno);
            }
        }

        return alunos;
    }

    @Override
    public List<Aula> listarAulas(String ctx, FiltroAulasJSON filtros, String professorSelecionado, String ambienteSelecionado, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = 25;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 25 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();

        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("select obj from Aula obj ");
        if(!UteisValidacao.emptyString(filtros.getParametro())){
            hql.append("where upper(obj.nome) like '").append("%").append(filtros.getParametro().toUpperCase()).append("%' ");
        }
        if (!StringUtils.isBlank(professorSelecionado)){
            if (where.length() > 0){
                where.append(" AND ");
            }
            where.append(" obj.professor.codigo in (").append(professorSelecionado).append(") ");
        }
        if(!StringUtils.isBlank(ambienteSelecionado)){
            if (where.length() > 0){
                where.append(" AND ");
            }
            where.append(" obj.ambiente.codigo in (").append(ambienteSelecionado).append(") ");
        }
        if (!UteisValidacao.emptyList(filtros.getModalidadeIds())) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            String modalidadeSelecionada = "";
            for (Integer modalidadeId : filtros.getModalidadeIds()) {
                modalidadeSelecionada += "," + modalidadeId;
            }
            modalidadeSelecionada = modalidadeSelecionada.replaceFirst(",", "");
            where.append( " obj.modalidade.codigo in (").append(modalidadeSelecionada).append(")");
        }
        if (filtros.getInicio() != null){
            if (where.length() > 0){
                where.append(" AND ");
            }
            where.append(" obj.dataInicio = ").append("'").append(filtros.getInicio()).append("'");
        }
        if (filtros.getFim() != null){
            if (where.length() > 0){
                where.append(" AND ");
            }
            where.append(" obj.dataFim = ").append("'").append(filtros.getFim()).append("'");
        }


        if (where.length() > 0){
            where.insert(0, " where ");
            hql.append(where.toString());
        }

        hql.append(paginadorDTO.getSQLOrderByUse());

        List<Aula> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
             lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;

    }
}
