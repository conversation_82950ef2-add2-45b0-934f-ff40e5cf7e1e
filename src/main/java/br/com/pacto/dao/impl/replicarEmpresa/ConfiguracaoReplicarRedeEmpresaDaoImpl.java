package br.com.pacto.dao.impl.replicarEmpresa;

import br.com.pacto.bean.replicarEmpresa.ConfiguracaoRedeEmpresa;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.replicarEmpresa.ConfiguracaoReplicarRedeEmpresaDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class ConfiguracaoReplicarRedeEmpresaDaoImpl extends DaoGenericoImpl<ConfiguracaoRedeEmpresa, Integer> implements ConfiguracaoReplicarRedeEmpresaDao {

    @Override
    public List<ConfiguracaoRedeEmpresa> findByChaveOrigem(String chaveOrigem) throws ServiceException {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("chaveOrigem", chaveOrigem);
            return findByParam(chaveOrigem, "select obj from ConfiguracaoRedeEmpresa obj where obj.chaveOrigem = :chaveOrigem", param);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public ConfiguracaoRedeEmpresa findByChaveDestino(String chaveOrigem, String chaveDestino) throws ServiceException {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("chaveDestino", chaveDestino);
            return findObjectByParam(chaveOrigem, "select obj from ConfiguracaoRedeEmpresa obj where obj.chaveDestino = :chaveDestino", param);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}
