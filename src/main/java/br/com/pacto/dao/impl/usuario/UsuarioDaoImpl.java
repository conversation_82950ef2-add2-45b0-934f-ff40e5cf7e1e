package br.com.pacto.dao.impl.usuario;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.UsuarioExcecoes;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Qualifier(value = "usuarioDao")
public class UsuarioDaoImpl extends DaoGenericoImpl<Usuario, Integer> implements
		UsuarioDao {

	@Autowired
	ConexaoZWService conexaoZWService;

	@Autowired
	ProfessorSinteticoDao professorSinteticoDao;

	@Autowired
	ProfessorSinteticoService professorSinteticoService;

	@Autowired
	PessoaService pessoaService;

	@Autowired
	PerfilDao perfilDao;

	@Autowired
	UsuarioService usuarioService;

	private static final int MAXIMO_APARELHOS_CONSULTAR = 50;

	@Override
	public Usuario consultarPorAluno(String ctx, ClienteSintetico cs) throws ServiceException {
		try{
			String hql = "SELECT obj FROM Usuario obj WHERE obj.cliente = :cliente";
			Map<String, Object> params = new HashMap<>();
			params.put("cliente", cs);
            return findObjectByParam(ctx, hql, params);
		} catch (Exception e) {
		    e.printStackTrace();
			throw new ServiceException(e);
		}
	}

	@Override
	public List<Usuario> listaUsuarioColaboradorTreino(String ctx, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
		int maxResults = MAXIMO_APARELHOS_CONSULTAR;
		int indiceInicial = 0;
		if (paginadorDTO != null) {
			maxResults = paginadorDTO.getSize() == null ? MAXIMO_APARELHOS_CONSULTAR : paginadorDTO.getSize().intValue();
			indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
		}
		StringBuilder hql = new StringBuilder();
		StringBuilder where = new StringBuilder(" WHERE obj.professor.codigo > 0 ");
		hql.append("SELECT new Usuario(obj.codigo, obj.userName, obj.professor.codigo, obj.professor.nome) FROM Usuario obj ");
		if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros())) {
			where.append(" AND (upper(obj.professor.nome) like '").append(filtros.getParametros().toUpperCase()).append("%'");
		}
		if (filtros.getUserName() && !StringUtils.isBlank(filtros.getParametros())) {
			if (where.length() > 0 && !filtros.getNome()) {
				where.append(" AND (");
			} else if (where.length() > 0 && filtros.getNome()) {
				where.append(" or ");
			}
			where.append(" upper(obj.userName) like '").append(filtros.getParametros().toUpperCase()).append("%') ");
		} else if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros()) && !filtros.getUserName()) {
			where.append(") ");
		}
		where.append(" AND obj.professor.empresa.codigo = ").append(empresaId);
		if (where.length() > 0) {
			hql.append(where.toString());
		}
		if (paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty()) {
			if (paginadorDTO.getSort().equals("nome,DESC")) {
				hql.append(" ORDER BY obj.professor.nome DESC");
			} else if (paginadorDTO.getSort().equals("nome,ASC")) {
				hql.append(" ORDER BY obj.professor.nome ASC");
			} else {
				hql.append(paginadorDTO.getSQLOrderByUse());
			}
		} else {
			hql.append(" ORDER BY obj.professor.nome ASC");
		}
		List<Usuario> lista = null;
		try {
			if (paginadorDTO != null) {
				paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, null).longValue());
			}
			lista = findByParam(ctx, hql.toString(), new HashMap<String, Object>(), maxResults, indiceInicial);
		} catch (Exception e) {
			throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIOS, e);
		}

		paginadorDTO.setSize((long) maxResults);
		paginadorDTO.setPage((long) indiceInicial);

		return lista;
	}

	@Override
	public List<Usuario> listaUsuarioColaborador(String ctx, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean count) throws ServiceException {
		List<Usuario> users = new ArrayList<>();
		int maxResults = MAXIMO_APARELHOS_CONSULTAR;
		int indiceInicial=0;

		StringBuilder hql = new StringBuilder();
		StringBuilder where = new StringBuilder(" WHERE obj.colaborador > 0 ");
		if(count){
			hql.append("SELECT count(obj.codigo) FROM Usuario obj ");
		}else {
			hql.append("SELECT obj.codigo, obj.userName, obj.colaborador, p.nome, col.pessoa FROM Usuario obj ");
		}
		hql.append(" INNER JOIN colaborador col on col.codigo = obj.colaborador");
		hql.append(" INNER JOIN pessoa p on p.codigo = col.pessoa");

		if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros())) {
			where.append(" AND (upper(p.nome) like '").append(filtros.getParametros().toUpperCase()).append("%'");
		}
		if (filtros.getUserName() && !StringUtils.isBlank(filtros.getParametros())) {
			if (where.length() > 0 && !filtros.getNome()) {
				where.append(" AND (");
			} else if (where.length() > 0 && filtros.getNome()) {
				where.append(" or ");
			}
			where.append(" upper(obj.userName) like '").append(filtros.getParametros().toUpperCase()).append("%') ");
		} else if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros()) && !filtros.getUserName()) {
			where.append(") ");
		}

		where.append(" AND col.empresa = ").append(empresaId);

		if (where.length() > 0){
			hql.append(where);
		}

		if(!count) {
			if (paginadorDTO != null && paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty()) {
				if (paginadorDTO.getSort().equals("nome,DESC")) {
					hql.append(" ORDER BY p.nome DESC");
				} else if (paginadorDTO.getSort().equals("nome,ASC")) {
					hql.append(" ORDER BY p.nome ASC");
				} else {
					hql.append(paginadorDTO.getSQLOrderByUse());
				}
			} else {
				hql.append(" ORDER BY p.nome ASC");
			}
		}

		try {
			try(Connection conZW = conexaoZWService.conexaoZw(ctx)){
				if (paginadorDTO != null) {
					if(count) {
						try(ResultSet rs = ConexaoZWServiceImpl.criarConsulta(hql.toString(), conZW)){
							if(rs.next()){
								paginadorDTO.setQuantidadeTotalElementos(rs.getLong("count"));
								return null;
							}
						}
					}
					maxResults = paginadorDTO.getSize() == null ? MAXIMO_APARELHOS_CONSULTAR : paginadorDTO.getSize().intValue();
					indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;

					paginadorDTO.setSize((long) maxResults);
					paginadorDTO.setPage((long) indiceInicial);

					hql.append(" LIMIT ").append(maxResults).append(" \n");
					hql.append(" OFFSET ").append(indiceInicial).append(" \n");
				}
				try(ResultSet rs = ConexaoZWServiceImpl.criarConsulta(hql.toString(), conZW)){
					while(rs.next()){
						users.add(montarDadosUsuario(ctx, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
					}
				}
			}
			return users;
		} catch (Exception e) {
			throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIOS, e);
		}
	}

	@Override
	public Usuario obterPorId(String ctx, Integer id) throws ServiceException {
		try {
			getCurrentSession(ctx).clear();
			return findById(ctx, id);
		} catch (Exception e) {
			throw new ServiceException(e);
		}
	}

	@Override
	public Usuario obterUsuarioPorColaborador(String ctx, Integer colaboradorId) throws ServiceException {
		try {
			getCurrentSession(ctx).clear();
			return findObjectByAttribute(ctx, "professor.codigo", colaboradorId);
		} catch (Exception e) {
			throw new ServiceException(e);
		}
	}

	@Override
	public String obterUsernameAlunoPorFicha(String ctx, Integer fichaId) throws ServiceException {
		try{
			getCurrentSession(ctx).clear();
			StringBuffer  sql = new StringBuffer();
			sql.append("select u.username ");
			sql.append("from clientesintetico cs ");
			sql.append("inner join usuario u ");
			sql.append("	on cs.codigo = u.cliente_codigo ");
			sql.append("inner join programatreino pt ");
			sql.append("	on cs.codigo = pt.cliente_codigo ");
			sql.append("inner join programatreinoficha ptf ");
			sql.append("	on pt.codigo = ptf.programa_codigo ");
			sql.append("inner join ficha f ");
			sql.append("	on ptf.ficha_codigo = f.codigo ");
			sql.append("where f.codigo = ");
			sql.append(fichaId);

			List<String> username = listOfObjects(ctx, sql.toString());
			if(username != null && !username.isEmpty()){
				return username.get(0);
			}
			return null;
		} catch (Exception e) {
			throw new ServiceException(e);
		}
	}

	@Override
	public boolean verificaSeJaExisteUsuarioComEmail(String ctx, Integer id, String email) throws ServiceException {
		try {
			getCurrentSession(ctx).clear();
			String hql = "SELECT count(u) FROM Usuario u where u.codigo <> :codigo AND u.email = :email";
			Query query = getCurrentSession(ctx).createQuery(hql);
			query.setParameter("codigo", id);
			query.setParameter("email", email);
			return (Long) query.uniqueResult() > 0;
		} catch (Exception e) {
			throw new ServiceException(e);
		}
	}

	public Usuario montarDadosUsuario(String ctx, ResultSet rs, Integer nivelMontarDados) throws Exception {
		Usuario usuario = new Usuario();
		if (Uteis.resultSetContemColuna(rs, "codigo_usuario")) {
			usuario.setCodigo(rs.getInt("codigo_usuario"));
			usuario.setUsuarioZW(rs.getInt("codigo_usuario"));
		} else {
			usuario.setCodigo(rs.getInt("codigo"));
			usuario.setUsuarioZW(rs.getInt("codigo"));
		}
	    usuario.setUserName(rs.getString("username"));
		usuario.setSenha(Uteis.resultSetContemColuna(rs, "senha") ? rs.getString("senha") : null);
		usuario.setNome(Uteis.resultSetContemColuna(rs, "nome") ? rs.getString("nome") : null);
		usuario.setEmpresaZW(Uteis.resultSetContemColuna(rs, "empresa") ? rs.getInt("empresa") : null);
		usuario.setStatus(Uteis.resultSetContemColuna(rs, "statusTw") ? StatusEnum.getFromID(rs.getInt("statusTw")) : null);
		usuario.setTipo(Uteis.resultSetContemColuna(rs, "tipoTw") ? TipoUsuarioEnum.getFromID(rs.getInt("tipoTw")) : null);
		usuario.setPerfil(Uteis.resultSetContemColuna(rs, "perfiltw") ? perfilDao.findById(ctx, rs.getInt("perfiltw")) : null);
		usuario.setUsuarioGeral(Uteis.resultSetContemColuna(rs, "usuariogeral") ? rs.getString("usuariogeral") : null);
		if (Uteis.resultSetContemColuna(rs, "colaborador") && (Uteis.NIVELMONTARDADOS_DADOSBASICOS == nivelMontarDados)) {
			usuario.setProfessor(new ProfessorSintetico());
			usuario.getProfessor().setCodigoColaborador(rs.getInt("colaborador"));
			usuario.getProfessor().setNome(Uteis.resultSetContemColuna(rs, "nome") ? rs.getString("nome") : null);
			usuario.getProfessor().setPessoa(new Pessoa());
			usuario.getProfessor().getPessoa().setNome(Uteis.resultSetContemColuna(rs, "nome") ? rs.getString("nome") : null);
			usuario.getProfessor().setValidadeCREF(Uteis.resultSetContemColuna(rs, "validadecref") ? rs.getDate("validadecref") : null);
			usuario.getProfessor().setCodigoPessoa(rs.getInt("pessoa"));
		}

		if(Uteis.resultSetContemColuna(rs, "fotokey")) {
			usuario.setFotoKeyApp(rs.getString("fotokey"));
		}

		if (Uteis.NIVELMONTARDADOS_TODOS == nivelMontarDados) {
			usuario.setProfessor(professorSinteticoDao.obterPorIdColaborador(ctx, rs.getInt("colaborador")));
			if(usuario.getProfessor() == null){
				sincronizarProfessorSimples(ctx, rs, usuario);
				usuario.setProfessor(professorSinteticoDao.obterPorIdColaborador(ctx, rs.getInt("colaborador")));
			}
			if(Uteis.resultSetContemColuna(rs, "pessoa")){
				usuario.getProfessor().setCodigoPessoa(rs.getInt("pessoa"));
			}

			usuario.setUsuarioEmail(new UsuarioEmail());
			usuario.getUsuarioEmail().setCodigo(rs.getInt("codigoUsuEmail"));
			usuario.getUsuarioEmail().setEmail(rs.getString("email"));
			usuario.getUsuarioEmail().setVerificado(rs.getBoolean("verificado"));
			usuario.getUsuarioEmail().setUsuario(usuario);
		}
		return usuario;
	}

	@Override
	public Map<String, String> consultaVersaoFotoApp(String ctx, Integer codUsuario) throws Exception {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("SELECT fotokeyapp, versaofotoapp FROM usuario WHERE fotokeyapp is not null AND versaofotoapp > 0 AND codigo = " + codUsuario);

		Map<String, String> fotoVersao = new HashMap<>();
        try (ResultSet rs = createStatement(ctx, stringBuilder.toString())) {
            if (rs.next()) {
                fotoVersao.put("fotokeyapp", rs.getString("fotokeyapp"));
                fotoVersao.put("versaofotoapp", rs.getString("versaofotoapp"));
                return fotoVersao;
            }
        }
        return null;
	}

	@Override
	public Map<String, String> consultaVersaoFotoAppColaborador(String ctx, Integer codUsuario) throws Exception {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("SELECT fotokeyapp, versaofotoapp FROM usuario WHERE fotokeyapp is not null AND versaofotoapp > 0 AND usuariozw = " + codUsuario);

		Map<String, String> fotoVersao = new HashMap<>();
        try (ResultSet rs = createStatement(ctx, stringBuilder.toString())) {
            if (rs.next()) {
                fotoVersao.put("fotokeyapp", rs.getString("fotokeyapp"));
                fotoVersao.put("versaofotoapp", rs.getString("versaofotoapp"));
                return fotoVersao;
            }
        }
        return null;
	}

	private void sincronizarProfessorSimples(String ctx, ResultSet rs, Usuario usuario) {
		try {
			ProfessorSintetico prof = new ProfessorSintetico();
			prof.setCodigoColaborador(rs.getInt("colaborador"));
			prof.setCodigoPessoa(rs.getInt("pessoa"));
			prof.setNome(rs.getString("nome"));
			prof.setAtivo(usuario.getStatus() == StatusEnum.ATIVO);
			prof.setPessoa(new Pessoa());
			prof.getPessoa().setNome(prof.getNome());
			String retorno = professorSinteticoService.sincronizarProfessor(ctx, prof, usuario.getEmpresaZW());
			if (retorno.equals("ok")) {
				Uteis.logarDebug(String.format("Colaborador: %s inserido na base do treino pelo \"sincronizarProfessorSimples\", pois o mesmo não havia sido encontrado. Chave: %s and instance: %s, EXECUTADO EM: %s",
						prof.getCodigoColaborador(),
						ctx,
						Aplicacao.instanceName,
						Uteis.getDataComHHMM(Calendario.hoje())));
			}
		} catch (Exception ignore) {
		}
	}
}
