package br.com.pacto.dao.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.AvaliacaoPostural;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoPosturalDao;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;

@Repository
public class AvaliacaoPosturalDaoImpl extends DaoGenericoImpl<AvaliacaoPostural, Integer> implements AvaliacaoPosturalDao {

    @Override
    public AvaliacaoPostural getAvaliacaoPostural(String ctx, AvaliacaoFisica avaliacao) throws Exception {
        EntityManager entityManager = getEntityManager(ctx);
        if (!entityManager.getTransaction().isActive()) {
            entityManager.getTransaction().begin();
        }

        String sql = "SELECT obj from " + AvaliacaoPostural.class.getSimpleName() + " obj " +
                "where obj.avaliacao.codigo = :codigo";

        Query query = entityManager.createQuery(sql);
        query.setParameter("codigo", avaliacao.getCodigo());
        List<AvaliacaoPostural> result = query.getResultList();
        entityManager.flush();
        entityManager.clear();
        return result != null && !result.isEmpty() ? result.get(0) : new AvaliacaoPostural();
    }
}
