package br.com.pacto.dao.impl.notificacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.notificacao.NotificacaoAulaAgendada;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.notificacao.NotificacaoAulaAgendadaDao;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class NotificacaoAulaAgendadaDaoImpl extends DaoGenericoImpl<NotificacaoAulaAgendada, Integer> implements NotificacaoAulaAgendadaDao {

    @Override
    public List<NotificacaoAulaAgendada> findByClienteHorarioDia(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, Date data, Boolean canceladas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT notif FROM NotificacaoAulaAgendada notif");
        sql.append(" WHERE notif.cliente.codigo = :codigoCliente");
        sql.append(" AND notif.horario = :codigoHorarioTurma");
        sql.append(" AND DATE(notif.dia) = :data");

        if (!canceladas) {
            sql.append(" AND notif.cancelada = false");
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("codigoCliente", cliente.getCodigo());
        params.put("codigoHorarioTurma", codigoHorarioTurma);
        params.put("data", data);

        return findByParamList(ctx, sql.toString(), params, 0, 0);
    }
}