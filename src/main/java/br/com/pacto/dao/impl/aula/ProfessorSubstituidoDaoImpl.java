/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.ProfessorSubstituidoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorSubstituidoExcecoes;
import br.com.pacto.util.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ProfessorSubstituidoDaoImpl  extends DaoGenericoImpl<ProfessorSubstituido, Integer> implements
        ProfessorSubstituidoDao {

    private static final int MAXIMO_PROFESSOR_SUBSTUIDO_CONSULTAR = 50;

    @Override
    public List<ProfessorSubstituido> obterProfessoresSubstituidos(String ctx, Date inicio, Date fim) throws Exception {
        StringBuilder where = new StringBuilder();
        Map<java.lang.String, Object> params = new HashMap<>();
        where.append("WHERE obj.diaAula between :dataInicio and :dataFim order by codigo desc ");
        params.put("dataInicio", Calendario.getDataComHoraZerada(inicio));
        params.put("dataFim", Calendario.getDataComHora(fim, "23:59:59"));

        return findByParam(ctx, where, params);
    }

    @Override
    public ProfessorSubstituido obterProfessorSubstituido(String ctx, Date dataHoje, int codigoHorarioTurma) throws Exception {
        StringBuilder hql = new StringBuilder();
        Map<java.lang.String, Object> params = new HashMap<>();

        hql.append("SELECT obj FROM ProfessorSubstituido obj ");
        hql.append("WHERE obj.diaAula between :dataInicio and :dataFim ");
        hql.append("AND obj.codigoHorarioTurma = :codigoHorarioTurma ");
        hql.append("order by codigo desc limit 1");

        params.put("dataInicio", Calendario.getDataComHoraZerada(dataHoje));
        params.put("dataFim", Calendario.getDataComHora(dataHoje, "23:59:59"));
        params.put("codigoHorarioTurma", codigoHorarioTurma);

        return findObjectByParam(ctx, hql.toString(), params);
    }

    @Override
    public void removerPorAulaHorario(String ctx, List<Integer> aulasHorarioId) throws Exception {
        String aulasHorarioSelecionado = "";
        for (Integer aulaHorarioId : aulasHorarioId) {
            aulasHorarioSelecionado += "," + aulaHorarioId;
        }
        aulasHorarioSelecionado = aulasHorarioSelecionado.replaceFirst(",", "");
        StringBuilder hql = new StringBuilder();
        hql.append("DELETE FROM professorsubstituido ps ");
        hql.append(" WHERE ps.codigoHorarioTurma in (").append(aulasHorarioSelecionado).append(") ");

        executeNativeSQL(ctx, hql.toString());
    }

    @Override
    public void removerPorAula(String ctx, Integer aulaId) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("DELETE FROM professorsubstituido ps ");
        hql.append(" WHERE ps.codigoHorarioTurma = (SELECT ahr.codigo FROM aulahorario ahr INNER JOIN Aula al on al.codigo = ahr.aula_codigo  WHERE al.codigo = ").append(aulaId).append(") ");

        executeNativeSQL(ctx, hql.toString());
    }

    @Override
    public List<ProfessorSubstituido> filtrarProfessoresSubstituidos(String ctx, FiltroGestaoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAXIMO_PROFESSOR_SUBSTUIDO_CONSULTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
           maxResults = paginadorDTO.getSize() == null ? MAXIMO_PROFESSOR_SUBSTUIDO_CONSULTAR : paginadorDTO.getSize().intValue();
           indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : (paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue());
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        hql.append("SELECT obj FROM ProfessorSubstituido obj ");
        if (filtros != null) {
            if (filtros.getInicio() != null && filtros.getFim() != null) {
                if (where.length() > 0) {
                    where.append(" AND ");
                }
                where.append(" obj.dataSubstituicao between :diaInicio and :diaFim ");
                params.put("diaInicio", filtros.getInicio());
                params.put("diaFim", filtros.getFim());
            }
            if (!UteisValidacao.emptyList(filtros.getProfessoresIds())) {
                String professoresIds = "";
                for (Integer professorId : filtros.getProfessoresIds()) {
                    professoresIds += "," + professorId;
                }
                professoresIds = professoresIds.replaceFirst(",", "");
                if (where.length() > 0) {
                    where.append(" AND ");
                }
                where.append(" obj.codigoProfessorOriginal in ( ").append(professoresIds).append(" ) ");
            }
            if (where.length() > 0) {
                where.insert(0, " WHERE ");
                hql.append(where.toString());
            }
        }
        List<ProfessorSubstituido> ret = new ArrayList<>();
        try {
            if (paginadorDTO != null) {
                hql.append(paginadorDTO.getSQLOrderByUse());
                Long qtdeElemento = countWithParam(ctx, "codigo", where, params).longValue();
                paginadorDTO.setQuantidadeTotalElementos(qtdeElemento);
                if (qtdeElemento < indiceInicial) {
                    indiceInicial = 0;
                }
            }
            ret.addAll(findByParam(ctx, hql.toString(), params, maxResults, indiceInicial));
        } catch (Exception ex) {
            throw new ServiceException(ProfessorSubstituidoExcecoes.ERRO_CONSULTAR_PROFESSORES_SUBSTITUIDOS, ex);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return ret;
    }

}
