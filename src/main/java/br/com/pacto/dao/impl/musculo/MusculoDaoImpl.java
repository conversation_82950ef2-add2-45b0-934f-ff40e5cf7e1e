/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.musculo.MusculoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.NivelExcecoes;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class MusculoDaoImpl extends DaoGenericoImpl<Musculo, Integer> implements
        MusculoDao {

    private static final int MAXIMO_NIVEL_CONSULTAR = 30;

    public List<Musculo> consultarMusculos(String ctx, FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException {
        int maxResults = MAXIMO_NIVEL_CONSULTAR;
        int indiceInicial=0;
        Map<String, Object> param = new HashMap<String, Object>();
        getCurrentSession(ctx).clear();
        String nome = "";
        nome = filtros.getParametro();
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_NIVEL_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM Musculo obj ");
        if ((nome != null) && (!nome.trim().equals(""))) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", nome.toUpperCase());
        }
        hql.append(where);
        hql.append(paginadorDTO.getSQLOrderByUse());

        List<Musculo> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS, e);

        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

}