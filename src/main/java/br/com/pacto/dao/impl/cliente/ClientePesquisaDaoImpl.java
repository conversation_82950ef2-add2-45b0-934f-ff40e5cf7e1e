package br.com.pacto.dao.impl.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.ClientePesquisaDao;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.Normalizer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ClientePesquisaDaoImpl extends DaoGenericoImpl<ClientePesquisa, Integer> implements ClientePesquisaDao {

    private static final int MAXIMO_CLIENTE_SINTETICO_CONSULTAR = 10;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private PerfilService perfilService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;

    public List<ClientePesquisa> consultarClientesPesquisa(String ctx, String parametro, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;
        int indiceInicial = 0;
        Map<String, Object> param = new HashMap<String, Object>();
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CLIENTE_SINTETICO_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        Boolean podeVerAlunosOutrosProfessores = false;
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        Perfil perfil = perfilService.obterPorUsuario(ctx, usuarioAtual.getId());
        if (perfil != null) {
            for (Permissao permissao : perfil.getPermissoes()) {
                if (permissao.getRecurso().equals(RecursoEnum.VER_ALUNOS_OUTRAS_CARTEIRAS)) {
                    podeVerAlunosOutrosProfessores = !(UteisValidacao.emptyList(permissao.getTipoPermissoes()) && permissao.getTipoPermissoes().size() == 0);
                }
            }
        }
        ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
        Boolean podeVerAlunosOutrasEmpresas = cfgPermAlunoMarcarAulaOutraUnidade != null && cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean();

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj FROM ClientePesquisa obj ");
        if(!podeVerAlunosOutrasEmpresas){
            where.append(" obj.empresa = :empresaId ");
            param.put("empresaId", empresaId);
            where.append(" AND ");
        }
        if(!podeVerAlunosOutrosProfessores){
            hql.append(", ClienteSintetico cli ");
            where.append(" cli.matricula = obj.matricula AND cli.professorSintetico.codigoColaborador = :procar AND ");
            if (usuarioAtual != null && UteisValidacao.emptyNumber(usuarioAtual.getColaboradorId())) {
                param.put("procar", professorSinteticoService.obterCodigoColaboradorPorUsuario(ctx, usuarioAtual.getId()));
            } else {
                param.put("procar", usuarioAtual.getColaboradorId());
            }
        }
        where.append(" (");
        try {
            Integer matricula = Integer.valueOf(parametro);
            where.append(" obj.matricula = :matricula ");
            param.put("matricula", matricula);
            where.append(" OR ");
        } catch (NumberFormatException ignore) {
        }
        where.append(" UNACCENT(UPPER(obj.nome)) LIKE UPPER(CONCAT(:nome,'%'))");
        param.put("nome", removerCaracteresEspeciais(parametro).replaceAll(" ", "%"));
        where.append(" OR UNACCENT(UPPER(obj.nomeConsulta)) LIKE UPPER(CONCAT(:nome,'%'))");
        param.put("nome", removerCaracteresEspeciais(parametro).replaceAll(" ", "%"));
        where.append("  OR UNACCENT(obj.emails)  LIKE CONCAT('%',:email,'%'))");
        param.put("email", removerCaracteresEspeciais(parametro));

        if (where.length() > 0) {
            where.insert(0, " where ");
            hql.append(where.toString());
        }

        List<ClientePesquisa> lista;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamHqlFull(ctx,
                        hql.toString().replaceFirst("SELECT obj", "SELECT COUNT(obj.matricula) "), param).longValue());
            }
            hql.append(" ORDER BY obj.nome");
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;

    }

    public static String removerCaracteresEspeciais(String texto) {
        texto = Normalizer.normalize(texto, Normalizer.Form.NFD);
        texto = texto.replaceAll("[^\\p{ASCII}]", "");
        return texto;
    }

}
