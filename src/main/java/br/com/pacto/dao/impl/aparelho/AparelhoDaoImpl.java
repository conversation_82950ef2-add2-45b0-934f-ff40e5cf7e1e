/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aparelho;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import br.com.pacto.controller.json.programa.AparelhoTO;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aparelho.AparelhoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aparelho.FiltroAparelhoJSON;
import br.com.pacto.service.impl.notificacao.excecao.AparelhoExcecoes;
import br.com.pacto.util.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AparelhoDaoImpl extends DaoGenericoImpl<Aparelho, Integer> implements
        AparelhoDao {

    private static final int MAXIMO_APARELHOS_CONSULTAR = 50;

    public List<AparelhoResponseTO> consultarAparelhos(final String ctx, FiltroAparelhoJSON filtroAparelhoJSON, PaginadorDTO paginadorDTO)throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<String, Object>();
        int maxResults = MAXIMO_APARELHOS_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_APARELHOS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<AparelhoResponseTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM Aparelho obj ");
        where.append(" where obj.crossfit = ").append(filtroAparelhoJSON.getCrossfit()).append(" ");
        if (filtroAparelhoJSON != null && filtroAparelhoJSON.getNome()) {
            where.append("and  upper(obj.nome) like CONCAT('%',:nome,'%')");
            hql.append(where.toString());
            param.put("nome", filtroAparelhoJSON.getParamentro().toUpperCase());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<Aparelho> lista;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHOS, e);
        }
        if (lista != null) {
            for (Aparelho aparelho : lista) {
                listaRet.add(new AparelhoResponseTO(aparelho));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);

        return listaRet;
    }

    @Override
    public List<AparelhoResponseTO> consultarAparelhosHabilitadosReservaEquipamento(String ctx) throws ServiceException {
        getCurrentSession(ctx).clear();

        StringBuilder hql = new StringBuilder();
        List<AparelhoResponseTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM Aparelho obj WHERE obj.usarEmReservaEquipamentos = TRUE AND obj.crossfit = FALSE");

        List<Aparelho> lista;
        try {
            lista = findByParam(ctx, hql.toString(), new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHOS_RESERVA_EQUIPAMENTO, e);
        }

        if (lista != null) {
            for (Aparelho aparelho : lista) {
                listaRet.add(new AparelhoResponseTO(aparelho));
            }
        }

        return listaRet;
    }

    public boolean aparelhoDuplicado(String ctx, AparelhoTO aparelhoTO) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("WHERE upper(nome) = :nome ");
        hql.append("AND crossfit = :crossfit ");
        if (!UteisValidacao.emptyNumber(aparelhoTO.getId())) {
            hql.append("AND codigo <> :codigo");
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome", aparelhoTO.getNome().trim().toUpperCase());
        params.put("crossfit", aparelhoTO.getCrossfit());
        if (!UteisValidacao.emptyNumber(aparelhoTO.getId())) {
            params.put("codigo", aparelhoTO.getId());
        }
        List<Aparelho> aparelhos = findByParam(ctx, hql, params);

        return !UteisValidacao.emptyNumber(aparelhos.size());
    }


}
