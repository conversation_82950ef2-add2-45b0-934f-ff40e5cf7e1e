/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividadeEmpresa;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividadeEmpresa.AtividadeEmpresaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeEmpresaDaoImpl extends DaoGenericoImpl<AtividadeEmpresa, Integer> implements
        AtividadeEmpresaDao {


    @Override
    public List<AtividadeEmpresa> listarAtividades(String ctx, FiltroAtividadeJSON filtros, Integer empresa, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();

        int maxResults = 50;
        int indiceInicial = 0;

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<>();
        hql.append("SELECT obj FROM AtividadeEmpresa obj ");
        where.append("where 1=1 ");
        where.append(" and obj.empresa.codigo = :empresa ");
        param.put("empresa", empresa);

        if ((filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            if (where.length() > 0) {
                where.append(" AND ");
            }
            where.append(" upper(obj.atividade.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", filtros.getParametro().toUpperCase());
        }


        hql.append(where.toString());

        List<AtividadeEmpresa> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }
}