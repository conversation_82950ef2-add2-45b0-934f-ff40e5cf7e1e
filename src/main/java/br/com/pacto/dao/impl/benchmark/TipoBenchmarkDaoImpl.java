package br.com.pacto.dao.impl.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import br.com.pacto.controller.json.crossfit.FiltroTipoBenchmarkJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.benchmark.TipoBenchmarkDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.TipoBenchmarkExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 12/07/2016.
 */
@Repository
public class TipoBenchmarkDaoImpl extends DaoGenericoImpl<TipoBenchmark, Integer> implements
        TipoBenchmarkDao {

    private static final int MAXIMO_CONSULTAR = 50;

    public List<TipoBenchmarkResponseTO> consultarTodos(String ctx, FiltroTipoBenchmarkJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException{
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<String, Object>();
        int maxResults = MAXIMO_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<TipoBenchmarkResponseTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM TipoBenchmark obj ");
        if ((filtros.getNome() != false) && (!filtros.getParametro().equals(""))) {
            where.append(" upper(obj.nome) LIKE CONCAT('%',:nome,'%')");
            param.put("nome", filtros.getParametro().toUpperCase());
        }
        if (where.length() > 0){
            where.insert(0, " WHERE ");
            hql.append(where.toString());
        }
        if(paginadorDTO.getSortMap()!= null && !paginadorDTO.getSortMap().isEmpty()){
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<TipoBenchmark> lista;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_BUSCAR_TIPOBENCHMARK, e);
        }
        if (lista != null) {
            for (TipoBenchmark tipoBenchmark : lista) {
                listaRet.add(new TipoBenchmarkResponseTO(tipoBenchmark));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return listaRet;

    }
}
