/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.serie;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.Ordenacao;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Repository
public class TreinoRealizadoDaoImpl extends DaoGenericoImpl<TreinoRealizado, Integer> implements
        TreinoRealizadoDao {

    @Override
    public Map<String, Object> getTreinos(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception {

        Long l = new Long(Uteis.nrDiasEntreDatas(primeira, ultima));
        Map<String, Object> map = new HashMap<String, Object>();

        String sql = "select count(codigo) as treinos from treinorealizado  " +
                "where cliente_codigo  = ? " +
                "and datainicio between ? and ?";

        try(PreparedStatement statement = getConnection(ctx).prepareStatement(sql)) {
            statement.setInt(1, codigoCliente);
            statement.setTimestamp(2, new Timestamp(Uteis.setHoraMinutoSegundo(primeira, 00, 00, 00).getTime()));
            statement.setTimestamp(3, new Timestamp(Uteis.setHoraMinutoSegundo(ultima, 23, 59, 59).getTime()));
            try(ResultSet rs = statement.executeQuery()) {
                if(rs.next()) {
                    map.put("treinos", rs.getInt("treinos"));
                    int semanas = l.intValue()/7;
                    int mediaSemanal = semanas == 0 ? 0 : rs.getInt("treinos")/semanas;
                    map.put("mediaTreinosSemana", mediaSemanal);
                }
            }
        };
        return map;

    }

    @Override
    public List<Map<String, Object>> getGruposMusculares(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception {
        String sqlGrupo =
        " select g.nome as grupomuscular, count(sr.codigo) as series from serierealizada sr "+
        " inner join treinorealizado tr on tr.codigo = sr.treinorealizado_codigo   "+
        " inner join atividadeficha af on af.codigo = sr.atividadeficha_codigo"+
        " inner join atividade at on at.codigo = af.atividade_codigo"+
        " inner join atividadegrupomuscular ag on ag.atividade_codigo = at.codigo"+
        " inner join grupomuscular g on g.codigo = ag.grupomuscular_codigo"+
        " where tr.cliente_codigo  = ?" +
        " and tr.datainicio between ? " +
        " and ? GROUP BY g.nome";

        List<Map<String, Object>> grupos = new ArrayList<Map<String, Object>>();
        try(PreparedStatement statement = getConnection(ctx).prepareStatement(sqlGrupo)){
            statement.setInt(1, codigoCliente);
            statement.setTimestamp(2, new Timestamp(Uteis.setHoraMinutoSegundo(primeira, 00, 00, 00).getTime()));
            statement.setTimestamp(3, new Timestamp(Uteis.setHoraMinutoSegundo(ultima, 23, 59, 59).getTime()));
            try(ResultSet set = statement.executeQuery()){
                while(set.next()) {
                    Map<String, Object> m = new HashMap<String, Object>();
                    m.put("grupo", set.getString("grupomuscular"));
                    m.put("series", set.getInt("series"));
                    grupos.add(m);
                }
            }
        }

        return grupos;
    }

    @Override
    public List<Map<String, Object>> getGruposMuscularesApp(String ctx, Date primeira, Date ultima,
                                                         Integer cliente) throws Exception {
        String sqlGrupo = "select g.nome as grupomuscular, count(tr.codigo) as treinos from treinorealizado tr\n" +
                "                         inner join programatreinoficha ptf on ptf.codigo = tr.programatreinoficha_codigo\n" +
                "                         inner join atividadeficha af on af.ficha_codigo = ptf.ficha_codigo\n" +
                "                         inner join atividadegrupomuscular ag on ag.atividade_codigo = af.atividade_codigo\n" +
                "                         inner join grupomuscular g on g.codigo = ag.grupomuscular_codigo\n" +
                "                         where tr.datainicio between ? and ?  \n" +
                "                         and cliente_codigo = ? \n" +
                "                         GROUP BY g.nome";

        List<Map<String, Object>> grupos = new ArrayList<Map<String, Object>>();
        try (Connection con = getConnection(ctx)){
            try(PreparedStatement statement = con.prepareStatement(sqlGrupo)){
                statement.setTimestamp(1, new Timestamp(Uteis.setHoraMinutoSegundo(primeira, 00, 00, 00).getTime()));
                statement.setTimestamp(2, new Timestamp(Uteis.setHoraMinutoSegundo(ultima, 23, 59, 59).getTime()));
                statement.setInt(3, cliente);
                try(ResultSet set = statement.executeQuery()){
                    while(set.next()) {
                        Map<String, Object> m = new HashMap<String, Object>();
                        m.put("grupo", set.getString("grupomuscular"));
                        m.put("series", set.getInt("treinos"));
                        grupos.add(m);
                    }
                }
            }
        }
        return grupos;
    }

    @Override
    public List<Map<String, Object>> getGruposPrograma(String ctx,Integer codigoPrograma) throws Exception {
        List<Map<String, Object>> gruposPrograma = new ArrayList<Map<String, Object>>();
            String sqlGrupoPrograma =
            " select g.nome as grupomuscular, count(sr.codigo) as series from serie sr "+
            " inner join atividadeficha af on af.codigo = sr.atividadeficha_codigo "+
            " inner join programatreinoficha ptf on ptf.ficha_codigo = af.ficha_codigo "+
            " inner join atividade at on at.codigo = af.atividade_codigo "+
            " inner join atividadegrupomuscular ag on ag.atividade_codigo = at.codigo "+
            " inner join grupomuscular g on g.codigo = ag.grupomuscular_codigo "+
            " where ptf.programa_codigo  = ?"+
            " GROUP BY g.nome ";
            try(PreparedStatement statement = getConnection(ctx).prepareStatement(sqlGrupoPrograma)) {
                statement.setInt(1, codigoPrograma);
                try(ResultSet set = statement.executeQuery()){
                    while(set.next()){
                        Map<String, Object> m = new HashMap<String, Object>();
                        m.put("grupo", set.getString("grupomuscular"));
                        m.put("series", set.getInt("series"));
                        gruposPrograma.add(m);
                    }
                }
            }
            return gruposPrograma;
    }

    @Override
    public Map<PerimetriaEnum, List<GenericoTO>> getAtividades(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception {
        String sqlAtividades =
        " select at.nome, count(sr.codigo) as nrseries, "+
        " array_to_string(ARRAY(select perimetros from grupomuscular_perimetros where grupomuscular_codigo "+
        " in(select grupomuscular_codigo from atividadegrupomuscular where atividade_codigo = at.codigo)), ',') as perimetros "+
        " from serierealizada sr "+
        " inner join treinorealizado tr on tr.codigo = sr.treinorealizado_codigo "+
        " inner join atividadeficha af on af.codigo = sr.atividadeficha_codigo "+
        " inner join atividade at on at.codigo = af.atividade_codigo "+
        " where tr.cliente_codigo  = ?"+
        " and tr.datainicio between ? "+
        " and ?"+
        " GROUP BY at.codigo, at.nome ORDER BY 2 desc";

        Map<PerimetriaEnum, List<GenericoTO>> atividades = new HashMap<PerimetriaEnum, List<GenericoTO>>();
        try (PreparedStatement statement = getConnection(ctx).prepareStatement(sqlAtividades)) {
            statement.setInt(1, codigoCliente);
            statement.setTimestamp(2, new Timestamp(Uteis.setHoraMinutoSegundo(primeira, 00, 00, 00).getTime()));
            statement.setTimestamp(3, new Timestamp(Uteis.setHoraMinutoSegundo(ultima, 23, 59, 59).getTime()));
            try (ResultSet rsatividades = statement.executeQuery()) {
                while(rsatividades.next()){
                    String pers = rsatividades.getString("perimetros");
                    if(UteisValidacao.emptyString(pers)){
                        continue;
                    }
                    String[] split = pers.split(",");
                    for(String p : split){
                        try {
                            PerimetriaEnum pEnum = PerimetriaEnum.getFromOrdinal(Integer.valueOf(p));
                            List<GenericoTO> maps = atividades.get(pEnum);
                            if(maps == null){
                                maps = new ArrayList<GenericoTO>();
                            }
                            GenericoTO gen = new GenericoTO();
                            gen.setLabel(rsatividades.getString("nome"));
                            gen.setOrdem(rsatividades.getInt("nrseries"));
                            maps.add(gen);
                            atividades.put(pEnum, maps);
                        }catch (Exception e){}

                    }
                }
                Set<PerimetriaEnum> keySet = atividades.keySet();
                for(PerimetriaEnum pe : keySet){
                    Integer total = 0;
                    List<GenericoTO> maps = atividades.get(pe);
                    for(GenericoTO m : maps){
                        total += m.getOrdem();
                    }
                    if(total > 0){
                        for(GenericoTO m : maps){
                            m.setOrdem(new Double(m.getOrdem().doubleValue()/total.doubleValue()*100.0).intValue());
                        }
                    }
                    List lista = Ordenacao.ordenarLista(maps, "ordem");
                    Collections.reverse(lista);
                    atividades.put(pe, lista);
                }
            }
        }

        return atividades;
    }

    @Override
    public Integer getTreinosByMatricula(String ctx, Date inicio, Date fim, Integer matricula) throws Exception {
        Integer treinos = 0;
        Long l = new Long(Uteis.nrDiasEntreDatas(inicio, fim));

        String sql = "select count(*) as treinos from treinorealizado tr JOIN clientesintetico cs ON cs.codigo = tr.cliente_codigo " +
                "where cs.matricula  = ? " +
                "and tr.datainicio between ? and ?";

        try(PreparedStatement statement = getConnection(ctx).prepareStatement(sql)) {
            statement.setInt(1, matricula);
            statement.setTimestamp(2, new Timestamp(Uteis.setHoraMinutoSegundo(inicio, 00, 00, 00).getTime()));
            statement.setTimestamp(3, new Timestamp(Uteis.setHoraMinutoSegundo(fim, 23, 59, 59).getTime()));
            try(ResultSet rs = statement.executeQuery()) {
                if(rs.next()) {
                    treinos = rs.getInt("treinos");
                }
            }
        };
        return treinos;
    }
}
