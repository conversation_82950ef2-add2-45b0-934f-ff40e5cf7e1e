/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.serie;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.SerieExcecoes;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class SerieDaoImpl extends DaoGenericoImpl<Serie, Integer> implements
        SerieDao {

    @Override
    public void removeList(String ctx, List<Serie> series) throws Exception {
        deleteList(ctx, series);
    }

    @Override
    public List<Serie> atualizarList(String ctx, List<Serie> series) throws Exception {
        try {
            for(Serie s : series){
                s.setarCompValores();
            }
        }catch (Exception e){
            Uteis.logar(e, SerieDaoImpl.class);
        }
        return updateList(ctx, series);
    }

    @Override
    public List<Serie> obterPorAtividadeFicha(String ctx, Integer atividadeFichaId) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM Serie WHERE atividadeficha_codigo = ").append(atividadeFichaId);

            List<Serie> series;
            try (ResultSet rs = createStatement(ctx, sql.toString())) {
                series = new ArrayList<>();
                while (rs.next()) {
                    Serie serie = new Serie();
                    serie.setCodigo(rs.getInt("codigo"));
                    serie.setAtividadeFicha(new AtividadeFicha(rs.getInt("atividadeficha_codigo")));
                    serie.setCadencia(rs.getString("cadencia"));
                    serie.setRepeticao(rs.getInt("repeticao"));
                    serie.setRepeticaoComp(rs.getString("repeticaocomp"));
                    serie.setCarga(rs.getDouble("carga"));
                    serie.setCargaComp(rs.getString("cargacomp"));
                    serie.setDuracao(rs.getInt("duracao"));
                    serie.setDistancia(rs.getInt("distancia"));
                    serie.setVelocidade(rs.getDouble("velocidade"));
                    serie.setComplemento(rs.getString("complemento"));
                    serie.setDescanso(rs.getInt("descanso"));
                    serie.setOrdem(rs.getInt("ordem"));
                    serie.setAtualizadoApp(rs.getBoolean("atualizadoApp"));

                    series.add(serie);
                }
            }

            return series;
        } catch (Exception ex) {
            throw new ServiceException(SerieExcecoes.ERRO_BUSCAR_SERIES, ex);
        }
    }
}
