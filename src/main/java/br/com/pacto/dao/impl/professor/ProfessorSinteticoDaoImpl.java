/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.professor;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.sql.rowset.FilteredRowSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ProfessorSinteticoDaoImpl extends DaoGenericoImpl<ProfessorSintetico, Integer> implements
        ProfessorSinteticoDao {

    private static final int MAXIMO_COLABORADORES_LISTAR = 50;

    @Override
    public List<ProfessorSintetico> listarColaboradores(final String ctx, FiltroColaboradorJSON filtros,
                                                        PaginadorDTO paginadorDTO,
                                                        Integer empresaId,
                                                        Boolean todosTipos) throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<String, Object>();
        int maxResults = MAXIMO_COLABORADORES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_COLABORADORES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj FROM ProfessorSintetico obj ");
        if (SuperControle.independente(ctx)) {
            where.append(" obj.empresa.codigo = :empresaId");
        } else {
            where.append(" obj.empresa.codZW = :empresaId");
        }
        param.put("empresaId", empresaId);
        if (!UteisValidacao.emptyList(filtros.getSituacoes())) {
            if (!where.toString().isEmpty()) {
                where.append(" and ");
            }
            if (filtros.getSituacoes().size() == 1) {
                where.append(" (obj.ativo = " + (filtros.getSituacoes().get(0).equals(SituacaoColaboradorEnum.ATIVO)) + ")");
            } else {
                where.append(" (obj.ativo = " + (filtros.getSituacoes().get(0).equals(SituacaoColaboradorEnum.ATIVO)) +
                " or obj.ativo = " + (filtros.getSituacoes().get(1).equals(SituacaoColaboradorEnum.ATIVO)) + ")");
            }
        } else {
            if (!where.toString().isEmpty()) {
                where.append(" and ");
            }
            where.append(" obj.ativo is true ");
        }
        if (!SuperControle.independente(ctx) && !todosTipos) {
            where.append(" and obj.professorTW is true");
        }
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros())) {
            if (!where.toString().isEmpty()) {
                where.append(" and ");
            }
            where.append(" upper(obj.nome) like CONCAT('%',:nome,'%')) )");
            param.put("nome", filtros.getParametros().toUpperCase().trim());
        }
        if(filtros.getUserName() && !StringUtils.isBlank(filtros.getParametros())) {
            if (!where.toString().isEmpty() && !filtros.getNome()){
                where.append(" and ");
            } else if (!where.toString().isEmpty() && filtros.getNome()) {
                where.append(" or ");
            }
            where.append(" obj.codigo IN (SELECT usu.professor.codigo FROM Usuario usu WHERE upper(usu.userName) like CONCAT('%',:username,'%')) )");
            param.put("username", filtros.getParametros().toUpperCase().trim());
        } else if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros()) && !filtros.getUserName()) {
            where.append(")");
        }
        if (where.length() > 0) {
            where.insert(0, " where ");
            hql.append(where.toString());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            return findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    @Override
    public List<ProfessorSintetico> colaboradoresIsNullUsuario(String ctx) throws ServiceException {
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT ps FROM Usuario us ");
        hql.append("RIGHT JOIN us.professor ps ");
        hql.append("WHERE us.professor.codigo IS NULL");

        try {
            return findByParam(ctx, hql.toString(), new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }
    @Override
    public ProfessorSintetico obterPorIdColaborador (String ctx, Integer idColaborador) throws ServiceException {
        StringBuilder hql = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();
        hql.append("SELECT obj FROM ProfessorSintetico obj ");
        if(SuperControle.independente(ctx)){
            hql.append("WHERE obj.codigo = :idColaborador");
        }else{
            hql.append("WHERE obj.codigoColaborador = :idColaborador");
        }
        param.put("idColaborador", idColaborador);

        try {
            return findObjectByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    @Override
    public ProfessorSintetico obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return findById(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
