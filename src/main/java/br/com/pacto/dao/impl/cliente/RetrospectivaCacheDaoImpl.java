package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.RetrospectivaCache;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.RetrospectivaCacheDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RetrospectivaCacheDaoImpl extends DaoGenericoImpl<RetrospectivaCache, Integer> implements
        RetrospectivaCacheDao {
    @Override
    public RetrospectivaCache findByMatriculaAno(String ctx, Integer matricula, Integer ano) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select r from RetrospectivaCache r ");
        sql.append("where r.matricula = :matricula ");
        sql.append("and r.ano = :ano ");
        List<RetrospectivaCache> resultados = getEntityManager(ctx)
                .createQuery(sql.toString(), RetrospectivaCache.class)
                .setParameter("matricula", matricula)
                .setParameter("ano", ano)
                .getResultList();

        return resultados.isEmpty() ? null : resultados.get(0);
    }
}
