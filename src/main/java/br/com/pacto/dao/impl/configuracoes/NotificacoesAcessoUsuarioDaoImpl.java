/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.configuracoes;

import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.NotificacoesAcessoUsuario;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.configuracoes.NotificacoesAcessoUsuarioDao;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public class NotificacoesAcessoUsuarioDaoImpl extends DaoGenericoImpl<NotificacoesAcessoUsuario, Integer> implements
        NotificacoesAcessoUsuarioDao {
}