package br.com.pacto.dao.impl.disponibilidade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.TipoAgendamentoDuracaoDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.disponibilidade.DisponibilidadeDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.agenda.AgendaModoBDServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AparelhoExcecoes;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class DisponibilidadeDaoImpl extends DaoGenericoImpl<Disponibilidade, Integer> implements DisponibilidadeDao {

    @Autowired
    ProfessorSinteticoDao professorSinteticoDao;

    @Autowired
    AgendamentoDao agendamentoDao;

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    @Autowired
    private ConfiguracaoSistemaService cs;

    private static final int MAXIMO_DISPONIBILIDADE = 50;

    @Override
    public List<Disponibilidade> findAllDisponibilidades(String ctx, Integer empresaId, FiltroDisponibilidadeDTO filtro, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        int maxResults = MAXIMO_DISPONIBILIDADE;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_DISPONIBILIDADE : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        hql.append("select obj from Disponibilidade obj ");
        hql.append(" inner join obj.horarios h ");
        hql.append(" left join obj.itensValidacao iV ");
        where.append(" where 1 = 1 ");

        if (filtro != null) {
            if (!UteisValidacao.emptyList(filtro.getProfessorIds())) {
                String professores = "";
                for (Integer cod : filtro.getProfessorIds()) {
                    professores += "," + cod;
                }
                where.append(" AND h.professor.codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
            }

            if (filtro.getFilterNew() != null) {
                if (filtro.getFilterNew() != null && filtro.getFilterNew().getVigencia() != null && !filtro.getFilterNew().getVigencia().isEmpty()) {
                    List<String> vigencia = new ArrayList<>();
                    filtro.getFilterNew().getVigencia().forEach(v -> {
                        vigencia.add(v.getId());
                    });
                    where.append(" AND ( \n");
                    if (vigencia.contains("NAO_VIGENTE")) {
                        where.append(" obj.fim < '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                    }
                    if (vigencia.contains("NAO_VIGENTE") && vigencia.contains("VIGENTE")) {
                        where.append(" OR ");
                    }
                    if (vigencia.contains("VIGENTE")) {
                        where.append(" obj.fim >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                    }
                    where.append(" ) \n");
                } else {
                    where.append(" AND obj.fim >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                }

                if (!UteisValidacao.emptyList(filtro.getFilterNew().getComportamentoFc())) {
                    String tiposAgendamentoId = "";
                    for (TipoAgendamentoDTO tipoAgendamento : filtro.getFilterNew().getComportamentoFc()) {
                        tiposAgendamentoId += "," + tipoAgendamento.getId();
                    }
                    where.append(" AND obj.comportamento IN (").append(tiposAgendamentoId.replaceFirst(",", "")).append(") ");
                }
                if (filtro.getFilterNew().getTipoValidacaoFc() != null && !filtro.getFilterNew().getTipoValidacaoFc().isEmpty()) {
                    where.append(" AND obj.tipoValidacao = ").append(filtro.getFilterNew().getTipoValidacaoFc().get("id"));
                    if (filtro.getFilterNew().getItemValidacaoFc() != null && !filtro.getFilterNew().getItemValidacaoFc().isEmpty()) {
                        if (filtro.getFilterNew().getTipoValidacaoFc().get("nome").equals("Plano")) {
                            String planosId = "";
                            for (ItemValidacaoDisponibilidadeDTO itemValidacao : filtro.getFilterNew().getItemValidacaoFc()) {
                                planosId += "," + itemValidacao.getPlano();
                            }
                            where.append(" AND iV.plano IN (").append(planosId.replaceFirst(",", "")).append(") ");
                        }
                        if (filtro.getFilterNew().getTipoValidacaoFc().get("nome").equals("Produto")) {
                            String produtosId = "";
                            for (ItemValidacaoDisponibilidadeDTO itemValidacao : filtro.getFilterNew().getItemValidacaoFc()) {
                                produtosId += "," + itemValidacao.getProduto();
                            }
                            where.append(" AND iV.produto IN (").append(produtosId.replaceFirst(",", "")).append(") ");
                        }
                    }
                }

                filtroTipoHorario(filtro, where);
                filtroDiasSemana(filtro, where);
            } else {
                where.append(" AND obj.fim >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
            }

        }
        where.append(" AND h.professor.empresa.codZW = ").append(empresaId);

        if(isNotBlank(filtro.getParametro())){
            if(Uteis.isNumeroValido(filtro.getParametro())){
                where.append(" and obj.codigo = ").append(filtro.getParametro());
            } else {
                where.append(" and remove_acento_upper(obj.nome) like remove_acento_upper('%").append(filtro.getParametro().toUpperCase()).append("%')");
            }
        }


        hql.append(where);
        hql.append(" GROUP BY obj");

        if (paginadorDTO != null) {
            hql.append(paginadorDTO.getSQLOrderBy());
            List<Disponibilidade> ret = findByParam(ctx, hql.toString(), new HashMap<>());
            paginadorDTO.setQuantidadeTotalElementos((ret != null && !ret.isEmpty()) ? (long) ret.size() : 0);
            ret = findByParam(ctx, hql.toString(), new HashMap<>(), maxResults, indiceInicial);
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            return ret;
        }

        return findByParam(ctx, hql.toString(), new HashMap<>(), maxResults, indiceInicial);
    }


    private void filtroDiasSemana(FiltroDisponibilidadeDTO filtro, StringBuilder where) {
        StringBuilder diasSemana = new StringBuilder();
        if(filtro.getFilterNew().getSegFc() != null && filtro.getFilterNew().getSegFc()){
            diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("SG");
        }
        if(filtro.getFilterNew().getTerFc() != null && filtro.getFilterNew().getTerFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("TR");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("TR");
            }
        }
        if(filtro.getFilterNew().getQuaFc() != null && filtro.getFilterNew().getQuaFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("QA");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("QA");
            }
        }
        if(filtro.getFilterNew().getQuiFc() != null && filtro.getFilterNew().getQuiFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("QI");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("QI");
            }
        }
        if(filtro.getFilterNew().getSexFc() != null && filtro.getFilterNew().getSexFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("SX");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("SX");
            }
        }
        if(filtro.getFilterNew().getSabFc() != null && filtro.getFilterNew().getSabFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("SB");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("SB");
            }
        }
        if(filtro.getFilterNew().getDomFc() != null && filtro.getFilterNew().getDomFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(obj.diasSemana) like '%").append("DM");
            }else{
                diasSemana.append("%' OR upper(obj.diasSemana) like '%").append("DM");
            }
        }
        if(!diasSemana.toString().isEmpty()) {
            diasSemana.append("%')");
            where.append(diasSemana);
        }
    }

    private void filtroTipoHorario(FiltroDisponibilidadeDTO filtro, StringBuilder where) {
        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc() &&
                (filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc()) &&
                (filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() == null || !filtro.getFilterNew().getTipoHorarioIntervaloTempoFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                (filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()) &&
                (filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() == null || !filtro.getFilterNew().getTipoHorarioIntervaloTempoFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_LIVRE.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() != null && filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() &&
                (filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()) &&
                (filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                    (filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() == null || !filtro.getFilterNew().getTipoHorarioIntervaloTempoFc())){
                where.append(" AND (obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_LIVRE.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                    filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() != null && filtro.getFilterNew().getTipoHorarioIntervaloTempoFc()){
                where.append(" AND (obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_LIVRE.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if((filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc()) &&
                    filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() != null && filtro.getFilterNew().getTipoHorarioIntervaloTempoFc()){
                where.append(" AND (obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc()){
                if(filtro.getFilterNew().getTipoHorarioIntervaloTempoFc() != null && filtro.getFilterNew().getTipoHorarioIntervaloTempoFc()){
                    where.append(" AND (obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal()).
                    append(" OR obj.tipoHorario = ").append(TipoDuracaoEvento.DURACAO_LIVRE.ordinal()).
                    append(" OR obj.tipoHorario = ").append(TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal()).append(")");
                }
            }
        }
    }

    public List<TipoAgendamentoDTO> consultarDisponibilidadeTipoAgendamento(final String ctx, String nomeDisponibilidade,
                                                        Boolean ativos,
                                                        PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<>();
        int maxResults = 50;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<TipoAgendamentoDTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM Disponibilidade obj ");

        if ((nomeDisponibilidade != null) && (!nomeDisponibilidade.trim().equals(""))) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", nomeDisponibilidade.toUpperCase());
        }
        if(ativos){
            try {
                where.append(where.toString().isEmpty() ? " where " : " and ");
                where.append(" '"+Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00:00") + "' <= inicio and " +
                        " '"+Uteis.getDataHoraJDBC(Calendario.hoje(), "23:59:59") + "' <= fim ");
            }catch (Exception ex){}
        }
        if (where.length() > 0){
            hql.append(where.toString());
        }
        if(paginadorDTO.getSortMap()!= null && !paginadorDTO.getSortMap().isEmpty()){
            hql.append(paginadorDTO.getSQLOrderByUse());
        } else {
            hql.append(" ORDER BY nome ASC");
        }
        List<Disponibilidade> lista;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHOS, e);
        }
        if (lista != null) {
            for (Disponibilidade disp : lista) {
                listaRet.add(new TipoAgendamentoDTO(disp));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);

        return listaRet;
    }

    public List<TipoAgendamentoDTO> consultarDisponibilidadesHorarioTipoAgendamento(String ctx, Date dia, Integer hora, Integer empresaId) throws ServiceException {
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select distinct obj.horarioDisponibilidade.disponibilidade from Agendamento obj ");
            query.append(" where obj.horarioDisponibilidade.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<Disponibilidade> disponibilidades = findByParam(ctx, query.toString(), p);
            return new ArrayList(){{
                for(Disponibilidade disp : disponibilidades){
                    add(new TipoAgendamentoDTO(disp));
                }
            }};
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessorTipoAgendamento(String ctx, Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException {
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select distinct obj.professor from Agendamento obj ");
            query.append(" where obj.horarioDisponibilidade.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.ativo is true ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            query.append(" and obj.horarioDisponibilidade.disponibilidade.codigo = ").append(tipo);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<ProfessorSintetico> professorSinteticos = professorSinteticoDao.findByParam(ctx, query.toString(), p);
            return new ArrayList(){{
                for(ProfessorSintetico p : professorSinteticos){
                    add(new ColaboradorSimplesTO(p, SuperControle.independente(ctx)));
                }
            }};
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }


    @Override
    public TipoAgendamentoDuracaoDTO obterPorIdTipoAgendamentoDuracao(
            String ctx, Integer id, Integer empresaId, Integer ambienteId, String inicio, String fim) throws ServiceException {
        try {
            ConfiguracaoSistema bloquearAgendamentosNoMesmoAmbiente = cs.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            Disponibilidade disponibilidade = findById(ctx, id);

            if (Objects.equals(bloquearAgendamentosNoMesmoAmbiente.getValor(), "true") && ambienteId != null && inicio != null && fim != null) {
                String query = "SELECT ag.codigo, inicio, fim FROM agendamento ag " +
                        " JOIN horariodisponibilidade hd ON ag.horariodisponibilidade_codigo = hd.codigo" +
                        " WHERE hd.ambiente_codigo = " + ambienteId +
                        " AND ag.disponibilidade = false";

                List<AgendaTotalTO> turmas = new ArrayList<>();
                List<Agendamento> agendamentos = new ArrayList<>();

                try (ResultSet rs = createStatement(ctx, query)) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                    while (rs.next()) {
                        LocalDateTime localDateTimeInicio = LocalDateTime.parse(rs.getString("inicio"), formatter);
                        LocalDateTime localDateTimeFim = LocalDateTime.parse(rs.getString("fim"), formatter);

                        Agendamento ag = new Agendamento();
                        ag.setCodigo(rs.getInt("codigo"));
                        ag.setInicio(Timestamp.valueOf(localDateTimeInicio));
                        ag.setFim(Timestamp.valueOf(localDateTimeFim));

                        agendamentos.add(ag);
                    }
                }

                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    AgendaModoBDServiceImpl agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
                    turmas = agendaAulasService.consultarAgendamentos(ctx,
                            formatter.parse(inicio), formatter.parse(fim), empresaId, null, "", null);
                }

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime inicioAgendamento = LocalDateTime.parse(inicio, formatter);
                LocalDateTime fimAgendamento = LocalDateTime.parse(fim, formatter);

                String diaSemana = obterDiaSemana(inicioAgendamento.getDayOfWeek());

                // 🔹 Verificar conflitos nos agendamentos (tabela 1)
                for (Agendamento ag : agendamentos) {
                    LocalDateTime agInicio = ag.getInicio().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime agFim = ag.getFim().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                    if (agInicio.isBefore(fimAgendamento) && agFim.isAfter(inicioAgendamento)) {
                        return new TipoAgendamentoDuracaoDTO(disponibilidade, false); // Conflito encontrado
                    }
                }

                // 🔹 Verificar conflitos nas turmas (tabela 2)
                for (AgendaTotalTO turma : turmas) {
                    if (!turma.getDiaSemana().equals(diaSemana)) {
                        continue;
                    }

                    LocalDateTime turmaInicio = turma.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime turmaFim = turma.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                    if (turmaInicio.isBefore(fimAgendamento) && turmaFim.isAfter(inicioAgendamento) && turma.getNrVagasPreenchidas() > 0 ) {
                        return new TipoAgendamentoDuracaoDTO(disponibilidade, false); // Conflito encontrado
                    }
                }
            }

            return new TipoAgendamentoDuracaoDTO(disponibilidade, true); // Nenhum conflito encontrado
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String obterDiaSemana(DayOfWeek dia) {
        switch (dia) {
            case MONDAY: return "SG";
            case TUESDAY: return "TR";
            case WEDNESDAY: return "QA";
            case THURSDAY: return "QI";
            case FRIDAY: return "SX";
            case SATURDAY: return "SB";
            case SUNDAY: return "DM";
            default: return "";
        }
    }

    public AgendaDisponibilidadeDTO detalheDisponibilidadeAgenda(String ctx, Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException {
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            query.append(" where obj.horarioDisponibilidade.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            query.append(" and obj.horarioDisponibilidade.disponibilidade.codigo = ").append(tipo);
            query.append(" and obj.professor.").append(SuperControle.independente(ctx) ? "codigo = " : "codigoColaborador = ").append(professor);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<Agendamento> agendamentos = agendamentoDao.findByParam(ctx, query.toString(), p);
            if(agendamentos == null || agendamentos.isEmpty()){
                return new AgendaDisponibilidadeDTO();
            }
            return new AgendaDisponibilidadeDTO(agendamentos.get(0));
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Boolean existeAgendamentoAlunoHorarioDisponibilidade(String ctx, Integer idHorario) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("select obj from Agendamento obj ");
            hql.append(" where obj.horarioDisponibilidade = ").append(idHorario);
            hql.append(" and obj.disponibilidade is false ");
            List<Agendamento> agend = agendamentoDao.findByParam(ctx, hql.toString(), new HashMap<>(), 1, 0);
            if (!agend.isEmpty()) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
