package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.PerfilDISC;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.PerfilDISCDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PerfilDISCDaoImpl extends DaoGenericoImpl<PerfilDISC, Integer> implements
        PerfilDISCDao {
    @Override
    public List<PerfilDISC> findByCliente(String ctx, ClienteSintetico cliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select p from PerfilDISC p ");
        sql.append("where p.cliente = :cliente ");
        sql.append("order by p.dataCadastro desc");
        return (List<PerfilDISC>) getEntityManager(ctx).createQuery(sql.toString())
                .setParameter("cliente", cliente)
                .getResultList();
    }
}