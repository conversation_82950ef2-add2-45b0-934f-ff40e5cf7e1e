/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.nivelwod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.nivelwod.NivelWodDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class NivelWodDaoImpl extends DaoGenericoImpl<NivelWod,Integer> implements NivelWodDao {

    private static final int MAXIMO_NIVEIS_WOD_LISTAR = 50;


    @Override
    public List<NivelWod> listarNiveisWod(String ctx, FiltroNivelWodJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {

        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_NIVEIS_WOD_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_NIVEIS_WOD_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("SELECT obj FROM NivelWod obj ");
        if ((filtros.getParametro() != null) && (!filtros.getParametro().trim().equals(""))) {
            if (where.length() == 0) {
                where.append(" where upper(obj.nome) like '").append(filtros.getParametro().toUpperCase()).append("%'");
            }
        }

        if (where.length() > 0) {
            hql.append(where.toString());
        }
        if (paginadorDTO.getSortMap() != null && !paginadorDTO.getSortMap().isEmpty()) {
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<NivelWod> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }


    @Override
    public NivelWod obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findObjectByParam(ctx, query, params);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
