/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.pefil.permissao;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.perfil.permissao.PermissaoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.FichaExcecoes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.util.HashSet;

/**
 *
 * <AUTHOR>
 */

@Repository
public class PermissaoDaoImpl extends DaoGenericoImpl<Permissao, Integer> implements
        PermissaoDao {
    @Autowired
    private PerfilDao perfilDao;

    public Permissao obterPermissaoPorPerfilRecurso(final String ctx,  final Integer perfil,  final RecursoEnum recurso) throws ServiceException {
        Permissao permissao = new Permissao();
        try {
            StringBuilder sbPermissao = new StringBuilder();

            sbPermissao.append("select * from permissao p "
                    + "where p.recurso = ")
                    .append(recurso.getId())
                    .append(" and p.perfil_codigo = ")
                    .append(perfil);

            try {
                try (ResultSet resultSet = createStatement(ctx, sbPermissao.toString())) {
                    while (resultSet.next()) {
                        permissao.setCodigo(resultSet.getInt("codigo"));
                        permissao.setRecurso(RecursoEnum.getFromId(resultSet.getInt("recurso")));
                        permissao.setPerfil(perfilDao.findById(ctx, resultSet.getInt("perfil_codigo")));

                        /* Get tipoPermissoes*/
                        HashSet<TipoPermissaoEnum> tipoPermissao = new HashSet<>();
                        String sbTipoPermissao = "select * from permissao_tipopermissoes pt where permissao_codigo = " + permissao.getCodigo();
                        try (ResultSet resultPerm = createStatement(ctx, sbTipoPermissao)) {
                            while (resultPerm.next()) {
                                int tipoDePermissao = resultPerm.getInt("tipopermissoes");
                                tipoPermissao.add(TipoPermissaoEnum.getFromOrdinal(tipoDePermissao));
                            }
                        }
                        permissao.setTipoPermissoes(tipoPermissao);
                    }
                }

            } catch (Exception e) {
                throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_PERFIL, e);
            }

        } catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_PERFIL, e);
        }
        return permissao;
    }

}
