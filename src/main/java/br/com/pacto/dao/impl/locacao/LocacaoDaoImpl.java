package br.com.pacto.dao.impl.locacao;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.locacao.Locacao;
import br.com.pacto.controller.json.locacao.FiltrosLocacaoJSON;
import br.com.pacto.controller.json.locacao.LocacaoHorarioTO;
import br.com.pacto.controller.json.locacao.TipoHorarioLocacaoEnum;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.locacao.LocacaoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UteisValidacao;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class LocacaoDaoImpl extends DaoGenericoImpl<Locacao, Integer> implements LocacaoDao {

    private static final int MAX = 10;

    @Override
    public List<Locacao> consultarLocacoes(String ctx, FiltrosLocacaoJSON filtros,
                                           Integer empresa,
                                           PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAX;
        int indiceInicial=0;
        getCurrentSession(ctx).clear();
        try {
            Map<String, Object > param = new HashMap<>();
            getCurrentSession(ctx).clear();

            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAX : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            }

            StringBuilder hqlFinal = new StringBuilder();
            StringBuilder select = new StringBuilder();
            StringBuilder join = new StringBuilder();
            StringBuilder where = new StringBuilder();
            StringBuilder orderBy = new StringBuilder();

            select.append("SELECT \n");
            select.append(" DISTINCT obj \n");
            select.append("FROM Locacao obj \n");

            join.append(" LEFT JOIN obj.horarios lh \n");

            where.append(" WHERE obj.empresa = ").append(empresa);

            if(!UteisValidacao.emptyString(filtros.getQuicksearchValue())) {
                where.append(" AND (upper(obj.nome) like '%").append(filtros.getQuicksearchValue().toUpperCase()).append("%' \n");
                where.append(" OR upper(obj.descricao) like '%").append(filtros.getQuicksearchValue().toUpperCase()).append("%') \n");
            }

            if (filtros != null && filtros.getFilterNew() != null) {
                if (filtros.getFilterNew() != null && filtros.getFilterNew().getVigencia() != null && !filtros.getFilterNew().getVigencia().isEmpty()) {
                    List<String> vigencia = new ArrayList<>();
                    filtros.getFilterNew().getVigencia().forEach(v -> {
                        vigencia.add(v.getId());
                    });
                    where.append(" AND ( \n");
                    if (vigencia.contains("NAO_VIGENTE")) {
                        where.append(" obj.dataFinal < '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                    }
                    if (vigencia.contains("NAO_VIGENTE") && vigencia.contains("VIGENTE")) {
                        where.append(" OR ");
                    }
                    if (vigencia.contains("VIGENTE")) {
                        where.append(" obj.dataFinal >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                    }
                    where.append(" ) \n");
                } else {
                    where.append(" AND obj.dataFinal >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
                }

                filtroTipoHorario(filtros, where);
                filtroDiasSemana(filtros, where);
            } else {
                where.append(" AND obj.dataFinal >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append(" 00:00:00' ");
            }

            String sqlCount = "SELECT COUNT(DISTINCT obj.codigo) FROM Locacao obj \n " + join + where;
            paginadorDTO.setQuantidadeTotalElementos(countWithParamHqlFull(ctx, sqlCount, param).longValue());

            if ((paginadorDTO.getSortMap() != null) && (paginadorDTO.getSortMap().size() > 0)) {
                for (Map.Entry<String, String> entry : paginadorDTO.getSortMap().entrySet()){
                    if (orderBy.length() >=1){
                        orderBy.append(",");
                    }
                    String colunaOrdenar = entry.getKey();
                    String direcao = entry.getValue();

                    switch (colunaOrdenar) {
                        case "diaSemana":
                            colunaOrdenar = "";
                            break;
                        case "tipoHorarioDescricao":
                            colunaOrdenar = "obj.tipoHorario.descricao";
                            break;
                        default:
                            colunaOrdenar = "obj." + colunaOrdenar;
                    }
                    if (colunaOrdenar != null && !colunaOrdenar.isEmpty()) {
                        orderBy.append(colunaOrdenar).append(" ").append(direcao);
                    }
                }
            }
            if (orderBy.length() > 0){
                orderBy.insert(0," ORDER BY ");
            }

            hqlFinal.append(select);
            hqlFinal.append(join);
            hqlFinal.append(where);
            hqlFinal.append(orderBy);

            List<Locacao> lista;

            lista = findByParam(ctx, hqlFinal.toString(), param, maxResults,indiceInicial);

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void filtroDiasSemana(FiltrosLocacaoJSON filtro, StringBuilder where) {
        StringBuilder diasSemana = new StringBuilder();
        if(filtro.getFilterNew().getSegFc() != null && filtro.getFilterNew().getSegFc()){
            diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("SG");
        }
        if(filtro.getFilterNew().getTerFc() != null && filtro.getFilterNew().getTerFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("TR");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("TR");
            }
        }
        if(filtro.getFilterNew().getQuaFc() != null && filtro.getFilterNew().getQuaFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("QA");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("QA");
            }
        }
        if(filtro.getFilterNew().getQuiFc() != null && filtro.getFilterNew().getQuiFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("QI");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("QI");
            }
        }
        if(filtro.getFilterNew().getSexFc() != null && filtro.getFilterNew().getSexFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("SX");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("SX");
            }
        }
        if(filtro.getFilterNew().getSabFc() != null && filtro.getFilterNew().getSabFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("SB");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("SB");
            }
        }
        if(filtro.getFilterNew().getDomFc() != null && filtro.getFilterNew().getDomFc()){
            if(diasSemana.toString().isEmpty()){
                diasSemana.append(" AND (upper(lh.diaSemana) like '%").append("DM");
            }else{
                diasSemana.append("%' OR upper(lh.diaSemana) like '%").append("DM");
            }
        }
        if(!diasSemana.toString().isEmpty()) {
            diasSemana.append("%')");
            where.append(diasSemana);
        }
    }

    private void filtroTipoHorario(FiltrosLocacaoJSON filtro, StringBuilder where) {
        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc() &&
                (filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc()) &&
                (filtro.getFilterNew().getTipoHorarioPlayFc() == null || !filtro.getFilterNew().getTipoHorarioPlayFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PRE_DEFINIDO.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                (filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()) &&
                (filtro.getFilterNew().getTipoHorarioPlayFc() == null || !filtro.getFilterNew().getTipoHorarioPlayFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.LIVRE.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioPlayFc() != null && filtro.getFilterNew().getTipoHorarioPlayFc() &&
                (filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()) &&
                (filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc())){
            where.append(" AND obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PLAY.ordinal());
        }

        if(filtro.getFilterNew().getTipoHorarioPlayFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                    (filtro.getFilterNew().getTipoHorarioPlayFc() == null || !filtro.getFilterNew().getTipoHorarioPlayFc())){
                where.append(" AND (obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PRE_DEFINIDO.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.LIVRE.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() == null || !filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc() &&
                    filtro.getFilterNew().getTipoHorarioPlayFc() != null && filtro.getFilterNew().getTipoHorarioPlayFc()){
                where.append(" AND (obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.LIVRE.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PLAY.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if((filtro.getFilterNew().getTipoHorarioLivreFc() == null || !filtro.getFilterNew().getTipoHorarioLivreFc()) &&
                    filtro.getFilterNew().getTipoHorarioPlayFc() != null && filtro.getFilterNew().getTipoHorarioPlayFc()){
                where.append(" AND (obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PRE_DEFINIDO.ordinal()).
                        append(" OR obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PLAY.ordinal()).append(")");
            }
        }

        if(filtro.getFilterNew().getTipoHorarioPreDefinidoFc() != null && filtro.getFilterNew().getTipoHorarioPreDefinidoFc()){
            if(filtro.getFilterNew().getTipoHorarioLivreFc() != null && filtro.getFilterNew().getTipoHorarioLivreFc()){
                if(filtro.getFilterNew().getTipoHorarioPlayFc() != null && filtro.getFilterNew().getTipoHorarioPlayFc()){
                    where.append(" AND (obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PRE_DEFINIDO.ordinal()).
                            append(" OR obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.LIVRE.ordinal()).
                            append(" OR obj.tipoHorario = ").append(TipoHorarioLocacaoEnum.PLAY.ordinal()).append(")");
                }
            }
        }
    }

    @Override
    public boolean isLocacaoVigente(String ctx, Integer codLocacao, Date dia) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select exists ( \n");
        sql.append("    select distinct l \n");
        sql.append("    from locacao l \n");
        sql.append("    inner join locacaohorario lh on lh.locacao = l.codigo \n");
        sql.append("    where l.codigo = ? \n");
        sql.append("    and ? between l.datainicial::date and l.datafinal::date \n");
        sql.append(" ) ");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        stm.setInt(1, codLocacao);
        stm.setDate(2, Uteis.getDataJDBC(dia));

        try (ResultSet rs = stm.executeQuery()) {
            if (rs.next()) {
                return rs.getBoolean("exists");
            }
        }

        return false;
    }

    @Override
    public List<LocacaoHorarioTO> consultarDisponibilidadesLocacao(String ctx, Integer codLocacao, Date dia, String diaSemana) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select lh.codigo, lh.diasemana, lh.horainicio, lh.horafim \n");
        sql.append(" from locacaohorario lh  \n");
        sql.append(" left join agendamentolocacao al on  ( \n");
        sql.append("    al.locacaohorario = lh.codigo \n");
        sql.append("    and al.inicio between concat(?, lh.horaInicio)::timestamp  and  concat(?, lh.horaFim)::timestamp \n");
        sql.append(" ) \n");
        sql.append(" where lh.locacao = ? \n");
        sql.append(" and lh.ativo \n");
        sql.append(" and lh.diasemana = ? \n");
        sql.append(" and al.codigo is null ");

        PreparedStatement stm = getConnection(ctx).prepareStatement(sql.toString());

        String dataFormatada = Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + " ";
        stm.setString(1, dataFormatada);
        stm.setString(2, dataFormatada);
        stm.setInt(3, codLocacao);
        stm.setString(4, diaSemana);

        ArrayList<LocacaoHorarioTO> listHorarios = new ArrayList<>();
        try (ResultSet rs = stm.executeQuery()) {
            while (rs.next()) {
                LocacaoHorarioTO horarios = new LocacaoHorarioTO();
                horarios.setCodigo(rs.getInt("codigo"));
                horarios.setDiaSemana(rs.getString("diasemana"));
                horarios.setHoraInicio(rs.getString("horainicio"));
                horarios.setHoraFim(rs.getString("horafim"));
                listHorarios.add(horarios);
            }
        }

        return listHorarios;
    }

}
