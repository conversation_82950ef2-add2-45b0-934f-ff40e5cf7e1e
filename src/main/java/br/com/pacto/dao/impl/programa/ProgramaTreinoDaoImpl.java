/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.*;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.FiltroGestaoProgramaDTO;
import br.com.pacto.controller.json.programa.FiltroProgramaTreinoJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ProgramaTreinoDaoImpl extends DaoGenericoImpl<ProgramaTreino, Integer> implements
        ProgramaTreinoDao {

    private static final int MAXIMO_PROGRAMAS_CONSULTAR = 50;
//    @Temporal(TemporalType.TIMESTAMP)
//    private Date dataLancamento = Calendario.hoje();

    @Autowired
    private ProfessorSinteticoService ps;
    @Autowired
    private ProgramaTreinoService pts;

    @Override
    public List<ProgramaTreino> programasPredefinidos(String ctx) throws ServiceException {
        getCurrentSession(ctx).clear();
        String query = "select obj from ProgramaTreino obj where preDefinido = true order by upper(nome) ";
        try {
            return findByParam(ctx, query, new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProgramaTreino> programasPredefinidos(String ctx, Integer situacao) throws ServiceException {
        getCurrentSession(ctx).clear();
        String query = "select obj from ProgramaTreino obj where preDefinido = true and situacao = "+ situacao +" order by upper(nome) ";
        try {
            return findByParam(ctx, query, new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProgramaTreino obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return findByIdClearSession(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<ProgramaTreinoAndamentoTO> consultarAndamento(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> params = new HashMap<String, Object>();
        int maxResults = MAXIMO_PROGRAMAS_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_PROGRAMAS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder sql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        try {
            Date inicio = Calendario.getDataComHoraZerada(Calendario.hoje());

            sql.append("select obj.nome, obj.cliente_codigo, obj.totalaulasprevistas, p.nrtreinos, c.nome as nomeCliente," +
                    " obj.professorCarteira_codigo, obj.professorMontou_codigo ");
            sql.append(" from ProgramaTreino obj ");
            sql.append(" inner join programatreinoandamento p on obj.codigo = p.programa_codigo  ");
            sql.append(" inner join clientesintetico c on obj.cliente_codigo = c.codigo ");
            where.append(" WHERE c.empresa = ").append(empresaZW);
            where.append(" and obj.totalAulasPrevistas > 0 and (obj.dataInicio = '").append(inicio)
                    .append("' or  obj.dataTerminoPrevisto = '").append(inicio).append("' or '")
                    .append(inicio).append("' BETWEEN obj.dataInicio AND obj.dataTerminoPrevisto) ");

            if(filtro!=null){
                String professores = "";
                if(filtro.getColaboradorIds()!=null){
                    for (Integer cod : filtro.getColaboradorIds()) {
                        ProfessorSintetico professorSintetico;
                        if (SuperControle.independente(ctx)) {
                            professorSintetico = ps.obterPorId(ctx, cod);
                        } else {
                            professorSintetico = ps.consultarPorCodigoColaborador(ctx, cod);
                        }
                        professores += "," + professorSintetico.getCodigo();
                    }
                }
                if (!professores.isEmpty()) {
                    where.append(" AND ( obj.professorCarteira_codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
                    where.append(" OR obj.professorMontou_codigo IN (").append(professores.replaceFirst(",", "")).append(")) ");
                }
            }
            if (filtro.getParametro() != null && !filtro.getParametro().trim().equals("")) {
                where.append(" and upper(c.nome) like '%").append(filtro.getParametro().toUpperCase()).append("%'");
            }
            sql.append(where);

            StringBuilder execucoes = new StringBuilder();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                String sort = paginadorDTO.getSort().contains("ASC")?"ASC":"DESC";
                String sortQuantidade = paginadorDTO.getSort().contains("ASC")?"DESC":"ASC";
                if(paginadorDTO.getSort().contains("aluno")){
                    paginadorDTO.setSort("c.nome," + sort);
                } else if(paginadorDTO.getSort().contains("nome")){
                    paginadorDTO.setSort("obj.nome," + sort);
                } else if(paginadorDTO.getSort().contains("execucoes")){
                    execucoes.append(" order by p.nrtreinos ").append(sortQuantidade).append(" ,obj.totalaulasprevistas ").append(sortQuantidade);
                } else if(paginadorDTO.getSort().contains("porcentagem")){
                    paginadorDTO.setSort("coalesce(((cast(coalesce(p.nrtreinos$0)as float)/cast(obj.totalAulasPrevistas as float))* 100.0)$0)," + sortQuantidade);
                }
            } else {
                paginadorDTO.setSort("coalesce(p.nrtreinos$0), DESC");
            }

            if (!StringUtils.isBlank(execucoes)) {
                sql.append(execucoes);
            } else {
                sql.append(paginadorDTO.getSQLOrderBy());
            }

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<ProgramaTreinoAndamentoTO> andamento = new ArrayList<>();
            try {
                try (ResultSet rs = createStatement(ctx, sql.toString().concat(" limit ").concat(String.valueOf(maxResults))
                        .concat(" offset ").concat(String.valueOf(indiceInicial)))) {
                    while (rs.next()) {
                        ProgramaTreino programaTreino = new ProgramaTreino();
                        programaTreino.setNome(rs.getString("nome"));
                        programaTreino.setCliente(new ClienteSintetico(rs.getInt("cliente_codigo"), rs.getString("nomeCliente")));
                        programaTreino.setNrTreinosRealizados(rs.getInt("nrtreinos"));
                        programaTreino.setTotalAulasPrevistas(rs.getInt("totalaulasprevistas"));

                        double nrTreinosPrevistos = programaTreino.getTotalAulasPrevistas() != null ? programaTreino.getTotalAulasPrevistas() : 0;
                        double nrTreinosRealizados = programaTreino.getNrTreinosRealizados() != null ? programaTreino.getNrTreinosRealizados() : 0;
                        double porcentagem = (nrTreinosRealizados != 0) ? (nrTreinosRealizados / nrTreinosPrevistos) * 100.0 : 0;
                        andamento.add(new ProgramaTreinoAndamentoTO(programaTreino.getNome(), programaTreino.getCliente().getNome(), (int) nrTreinosRealizados, (int) nrTreinosPrevistos, new BigDecimal(porcentagem).setScale(2, RoundingMode.HALF_UP).doubleValue()));
                    }
                }
                if (paginadorDTO != null) {
                    try (ResultSet rsCount = createStatement(ctx,
                            "select COUNT(*) as cont from ( " + sql + ") as f")) {
                        if (rsCount.next()) {
                            paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                        } else {
                            paginadorDTO.setQuantidadeTotalElementos(10l);
                        }
                    }
                }
            } catch (Exception e) {
                throw new ServiceException(e);
            }

            return andamento;
        } catch (Exception e) {
            throw new ServiceException(e);
        }


    }

    public List<ExecucoesTreinoTO> consultarExecucoesTreino(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_PROGRAMAS_CONSULTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_PROGRAMAS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT c.matricula, c.nome as aluno, p.nome as professor, COUNT(t.cliente_codigo) AS execucoes \n");
            sql.append("FROM treinorealizado t \n");
            sql.append("INNER JOIN clientesintetico c ON t.cliente_codigo = c.codigo \n");
            sql.append("LEFT JOIN professorsintetico p ON c.professorsintetico_codigo = p.codigo \n");
            sql.append("WHERE t.datainicio BETWEEN '").append(Calendario.getDataComHoraZerada(filtro.getInicio())).append("' AND '").append(Calendario.getDataComHora(filtro.getFim(), "23:59:59")).append("' \n");
            if (filtro != null) {
                if (filtro.getParametro() != null && !filtro.getParametro().trim().equals("")) {
                    sql.append("AND (UPPER(c.nome) LIKE '%").append(filtro.getParametro().toUpperCase()).append("%' ");
                    sql.append("OR UPPER(p.nome) LIKE '%").append(filtro.getParametro().toUpperCase()).append("%') \n");
                }
                if (filtro.getColaboradorIds() != null) {
                    String professores = "";
                    StringBuilder sqlSemProfessor = new StringBuilder();
                    StringBuilder sqlComProfessor = new StringBuilder();
                    for (Integer cod : filtro.getColaboradorIds()) {
                        if (cod == 0) {
                            sqlSemProfessor.append("c.professorsintetico_codigo IS NULL ");
                            continue;
                        }
                        if (SuperControle.independente(ctx)) {
                            professores += "," + cod;
                        } else {
                            Integer codPs = ps.obterCodigoProfessorPorColaborador(ctx, cod);
                            professores += codPs != null ? "," + codPs : "";
                        }
                    }
                    if (!professores.isEmpty()) {
                        sqlComProfessor.append("p.codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
                    }
                    if (!sqlSemProfessor.toString().isEmpty() && !sqlComProfessor.toString().isEmpty()) {
                        sql.append("AND (").append(sqlSemProfessor.toString()).append(" OR ").append(sqlComProfessor.toString()).append(") \n");
                    } else if (!sqlSemProfessor.toString().isEmpty()) {
                        sql.append("AND ").append(sqlSemProfessor.toString()).append(" \n");
                    } else if (!sqlComProfessor.toString().isEmpty()) {
                        sql.append("AND ").append(sqlComProfessor.toString()).append(" \n");
                    }
                }
                if (!UteisValidacao.emptyList(filtro.getOrigemExecucaoFicha())) {
                    String idsEnumOrigem = "";
                    for (String origem : filtro.getOrigemExecucaoFicha()) {
                        switch (origem) {
                            case "RETIRA_FICHA":
                                idsEnumOrigem += ",0,3";
                                break;
                            case "ACOMP_PROFESSOR":
                                idsEnumOrigem += ",1";
                                break;
                            case "SMARTPHONE":
                                idsEnumOrigem += ",2";
                                break;
                        }
                    }
                    if (!idsEnumOrigem.isEmpty()) {
                        sql.append("AND t.origem IN (").append(idsEnumOrigem.replaceFirst(",", "")).append(") \n");
                    }
                }
            }
            sql.append("GROUP by c.matricula, c.nome, p.nome \n");
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                String sort = paginadorDTO.getSort().contains("ASC")?"ASC":"DESC";
                if (paginadorDTO.getSort().contains("aluno")) {
                    paginadorDTO.setSort("c.nome," + sort);
                } else if(paginadorDTO.getSort().contains("professor")){
                    paginadorDTO.setSort("p.nome," + sort);
                }
            } else {
                paginadorDTO.setSort("c.nome, ASC");
            }
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            sql.append(paginadorDTO.getSQLOrderBy());
            List<ExecucoesTreinoTO> execucoes = new ArrayList<>();
            String sqlConsulta = sql.toString().concat(" limit ").concat(String.valueOf(maxResults)).concat(" offset ").concat(String.valueOf(indiceInicial));
            try (ResultSet rs = createStatement(ctx, sqlConsulta)) {
                while (rs.next()) {
                    ExecucoesTreinoTO execucoesAluno = new ExecucoesTreinoTO();
                    execucoesAluno.setMatricula(rs.getString("matricula"));
                    execucoesAluno.setAluno(rs.getString("aluno"));
                    execucoesAluno.setProfessor(rs.getString("professor") == null ? "" : rs.getString("professor"));
                    execucoesAluno.setExecucoes(rs.getString("execucoes"));
                    execucoes.add(execucoesAluno);
                }
            }
            if (paginadorDTO != null) {
                try (ResultSet rsCount = createStatement(ctx,"select COUNT(*) as cont from ( " + sql + ") as f")) {
                    if (rsCount.next()) {
                        paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                    } else {
                        paginadorDTO.setQuantidadeTotalElementos(10l);
                    }
                }
            }
            return execucoes;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    public ProgramaTreino alterar(String ctx, ProgramaTreino pt) throws ServiceException {
        Transaction transaction = getCurrentSession(ctx).beginTransaction();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE programatreino SET ");
            sql.append("datainicio = ");
            if (pt.getDataInicio() != null) {
                sql.append("'").append(new Timestamp(pt.getDataInicio().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("datalancamento = ");
            if (pt.getDataLancamento() != null) {
                sql.append("'").append(new Timestamp(pt.getDataLancamento().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("dataproximarevisao = ");
            if (pt.getDataProximaRevisao() != null) {
                sql.append("'").append(new Timestamp(pt.getDataProximaRevisao().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("datarenovacao = ");
            if (pt.getDataRenovacao() != null) {
                sql.append("'").append(new Timestamp(pt.getDataRenovacao().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("dataterminoprevisto = ");
            if (pt.getDataTerminoPrevisto() != null) {
                sql.append("'").append(new Timestamp(pt.getDataTerminoPrevisto().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("dataultimaatualizacao = ");
            if (pt.getDataUltimaAtualizacao() != null) {
                sql.append("'").append(new Timestamp(pt.getDataUltimaAtualizacao().getTime())).append("', ");
            } else {
                sql.append("null, ");
            }
            sql.append("diasporsemana = ").append(pt.getDiasPorSemana()).append(", ");
            sql.append("nome = '").append(pt.getNome().replace("'", "''")).append("', ");
            sql.append("nrtreinosrealizados = ").append(pt.getNrTreinosRealizados()).append(", ");
            sql.append("programatreinorenovacao = ").append(pt.getProgramaTreinoRenovacao()).append(", ");
            sql.append("programatreinorenovado = ").append(pt.getProgramaTreinoRenovado()).append(", ");
            sql.append("situacao = ").append(pt.getSituacao() != null ? pt.getSituacao().getId() : 1).append(", ");
            sql.append("totalaulasprevistas = ").append(pt.getTotalAulasPrevistas()).append(", ");
            sql.append("treinorapido = ").append(pt.getTreinoRapido()).append(", ");
            sql.append("versao = ").append(pt.getVersao()).append(", ");
            sql.append("cliente_codigo = ").append(pt.getCliente() != null ? pt.getCliente().getCodigo() : null).append(", ");
            sql.append("professorcarteira_codigo = ").append(pt.getProfessorCarteira() != null ? pt.getProfessorCarteira().getCodigo() : null).append(", ");
            sql.append("professormontou_codigo = ").append(pt.getProfessorMontou() != null ? pt.getProfessorMontou().getCodigo() : null).append(", ");
            sql.append("predefinido = ").append(pt.getPreDefinido()).append(", ");
            sql.append("nivel_codigo = ").append(pt.getNivel() != null ? pt.getNivel().getCodigo() : null);
            sql.append(" WHERE codigo = ").append(pt.getCodigo());
            sql.append(" RETURNING codigo");

            try (ResultSet statement = createStatement(ctx, sql.toString())) {
                statement.next();
            }
            transaction.commit();

            return pt;
        } catch (Exception ex) {
            transaction.rollback();
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, ex);
        }
    }

    public void atualizarSituacaoProgramaPredefinido(String ctx, Integer id, Integer situacao) throws ServiceException {
        try {
            ProgramaTreino programaTreino = obterPorId(ctx, id);
            programaTreino.setSituacao(situacao == 0 ? ProgramaSituacaoEnum.ATIVO : ProgramaSituacaoEnum.INATIVO);
            update(ctx, programaTreino);
        } catch (Exception e) {
            throw new ServiceException(ProgramaTreinoExcecoes.ERRO_ALTERAR_PROGRAMA_TREINO, e);
        }
    }


    public List<ProgramaTreino> obterProgramasAluno(FiltroProgramaTreinoJSON filtros,
                                                    PaginadorDTO paginadorDTO,
                                                    String ctx,
                                                    ClienteSintetico cs) throws ServiceException {

        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort().replace("inicio", "datainicio"));
            paginadorDTO.setSort(paginadorDTO.getSort().replace("termino", "dataterminoprevisto"));
        } else {
            paginadorDTO.setSort("dataTerminoPrevisto,DESC");
        }

        int maxResults = MAXIMO_PROGRAMAS_CONSULTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_PROGRAMAS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();

        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();
        hql.append("SELECT obj FROM ProgramaTreino obj ");
        where.append("WHERE obj.cliente.codigo = :codigoCliente \n");
        param.put("codigoCliente", cs.getCodigo());

        if ((filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            where.append("AND UPPER(obj.nome) LIKE CONCAT('%',:nome,'%') \n");
            param.put("nome", filtros.getParametro().toUpperCase());
        }

        hql.append(where.toString());
        List<ProgramaTreino> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamHqlFullFromSizeList(ctx, hql.toString(), param).longValue());
            }
            hql.append(paginadorDTO.getSQLOrderByUse());
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);

        return lista;
    }

    @Override
    public List<ProgramaTreino> obterProgramasPorRevisaoIA(String ctx) throws ServiceException {
        getCurrentSession(ctx).clear();
        String query = "SELECT obj FROM ProgramaTreino obj WHERE obj.isGeradoPorIA is :geradoPorIA";
        Map<String, Object> params = new HashMap<>();
        params.put("geradoPorIA", true);

        try {
            return findByParam(ctx, query, params);
        } catch (Exception e) {
            throw new ServiceException("Erro ao buscar treinos gerados por IA para revisão.", e);
        }
    }

}
