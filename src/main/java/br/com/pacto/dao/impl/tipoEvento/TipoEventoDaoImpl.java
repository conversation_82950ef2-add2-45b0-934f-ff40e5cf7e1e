/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.tipoEvento;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.tipoevento.TipoEventoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AparelhoExcecoes;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class TipoEventoDaoImpl extends DaoGenericoImpl<TipoEvento, Integer> implements
        TipoEventoDao {
    private static final int MAXIMO_APARELHOS_CONSULTAR = 50;

    public List<TipoAgendamentoDTO> consultarTipoEvento(final String ctx, String nomeTipoEvento,
                                                        Boolean ativos,
                                                        PaginadorDTO paginadorDTO)throws ServiceException {
        getCurrentSession(ctx).clear();
        Map<String, Object> param = new HashMap<String, Object>();
        int maxResults = MAXIMO_APARELHOS_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_APARELHOS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<TipoAgendamentoDTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM TipoEvento obj ");

        if ((nomeTipoEvento != null) && (!nomeTipoEvento.trim().equals(""))) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", nomeTipoEvento.toUpperCase());
        }
        if(ativos){
            where.append(where.toString().isEmpty() ? " where " : " and ");
            where.append(" obj.ativo is true ");
        }
        if (where.length() > 0){
            hql.append(where.toString());
        }
        if(paginadorDTO.getSortMap()!= null && !paginadorDTO.getSortMap().isEmpty()){
            hql.append(paginadorDTO.getSQLOrderByUse());
        } else {
            hql.append(" ORDER BY nome ASC");
        }
        List<TipoEvento> lista;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHOS, e);
        }
        if (lista != null) {
            for (TipoEvento tpEvento : lista) {
                listaRet.add(new TipoAgendamentoDTO(tpEvento));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);

        return listaRet;
    }

}