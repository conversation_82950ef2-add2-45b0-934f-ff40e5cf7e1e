/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.ClienteObservacaoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class ClienteObservacaoDaoImpl extends DaoGenericoImpl<ClienteObservacao, Integer> implements ClienteObservacaoDao {

    @Override
    @SuppressWarnings("unchecked")
    public List<ClienteObservacao> consultarPorMatriculaCliente(String contexto, String matriculaCliente) throws Exception {
        if (StringUtils.isEmpty(contexto) || StringUtils.isEmpty(matriculaCliente)) {
            throw new ServiceException("O contexto e a matrícula do cliente devem ser informados.");
        }

        return (List<ClienteObservacao>) getEntityManager(contexto)
                .createQuery(montarSQLConsultaPorMatricula(matriculaCliente))
                .getResultList();
    }
    @Override
    @SuppressWarnings("unchecked")
    public List<ClienteObservacao> consultarPorMatriculaCliente(String contexto, String matriculaCliente, boolean anexoAvaliacao) throws Exception {
        if (StringUtils.isEmpty(contexto) || StringUtils.isEmpty(matriculaCliente)) {
            throw new ServiceException("O contexto e a matrícula do cliente devem ser informados.");
        }

        return (List<ClienteObservacao>) getEntityManager(contexto)
                .createQuery(montarSQLConsultaPorMatricula(matriculaCliente, anexoAvaliacao))
                .getResultList();
    }

    private String montarSQLConsultaPorMatricula(String matriculaCliente) {
        return new StringBuilder("SELECT clienteobservacao FROM ").append(ClienteObservacao.class.getSimpleName()).append(" as clienteobservacao")
                .append(" INNER JOIN clienteobservacao.cliente")
                .append(" WHERE clienteobservacao.cliente.matricula = ").append(matriculaCliente)
                .append(" AND clienteobservacao.avaliacaoFisica is false")
                .append(" ORDER BY clienteobservacao.dataObservacao DESC")
                .toString();
    }
    private String montarSQLConsultaPorMatricula(String matriculaCliente, boolean anexoAvaliacao) {
        return new StringBuilder("SELECT clienteobservacao FROM ").append(ClienteObservacao.class.getSimpleName()).append(" as clienteobservacao")
                .append(" INNER JOIN clienteobservacao.cliente")
                .append(" WHERE clienteobservacao.cliente.matricula = ").append(matriculaCliente)
                .append(" AND clienteobservacao.avaliacaoFisica is ").append(anexoAvaliacao)
                .toString();
    }

    @Override
    public List<ClienteObservacao> consultarPorMatriculaClienteApp(String contexto, String matriculaCliente, int maxResult, int index) throws Exception {
        if (StringUtils.isEmpty(contexto) || StringUtils.isEmpty(matriculaCliente)) {
            throw new ServiceException("O contexto e a matrícula do cliente devem ser informados.");
        }
        if(UteisValidacao.emptyNumber(index))
        {
            index = 0;
        }
        if(UteisValidacao.emptyNumber(maxResult))
        {
            return (List<ClienteObservacao>) getEntityManager(contexto)
                    .createQuery(montarSQLConsultaPorMatricula(matriculaCliente))
                    .setFirstResult(index)
                    .getResultList();
        }
        return (List<ClienteObservacao>) getEntityManager(contexto)
                .createQuery(montarSQLConsultaPorMatricula(matriculaCliente))
                .setFirstResult(index)
                .setMaxResults(maxResult == 0 ? 0 : maxResult)
                .getResultList();
    }
}
