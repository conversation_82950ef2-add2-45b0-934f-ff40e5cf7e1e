package br.com.pacto.dao.impl.cliente;

import br.com.pacto.bean.cliente.HistoricoPresenca;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.cliente.HistoricoPresencaDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class HistoricoPresencaDaoImpl extends DaoGenericoImpl<HistoricoPresenca, Integer> implements
        HistoricoPresencaDao {

    @Override
    public HistoricoPresenca findByMatricula(String ctx, Integer matricula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select hp from HistoricoPresenca hp ");
        sql.append("where hp.cliente.matricula = :matricula ");

        List<HistoricoPresenca> resultados = getEntityManager(ctx)
                .createQuery(sql.toString(), HistoricoPresenca.class)
                .setParameter("matricula", matricula)
                .getResultList();

        return resultados.isEmpty() ? null : resultados.get(0);
    }
}
