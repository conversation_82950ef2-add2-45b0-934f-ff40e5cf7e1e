/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeFichaExcecoes;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Transaction;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeFichaDaoImpl extends DaoGenericoImpl<AtividadeFicha, Integer> implements
        AtividadeFichaDao {

    @Override
    public void removeList(String ctx, List<Integer> atividadesFichaId) throws Exception {
//        StringBuilder sql = new StringBuilder();
//        sql.append("DELETE FROM AtividadeFicha WHERE codigo in (");
//        String atividadesSelecionadas = "";
        for (Integer atividadeFichaId : atividadesFichaId) {
//            atividadesSelecionadas += "," + atividadeFichaId;
            delete(ctx, atividadeFichaId);
        }
//        atividadesSelecionadas = atividadesSelecionadas.replaceFirst(",", "");
//        sql.append(atividadesSelecionadas).append(")");
//
//        executeNative(ctx, sql.toString());
    }

    @Override
    public List<AtividadeFicha> atualizarLista(String ctx, List<AtividadeFicha> atividadeFichas) throws Exception {
        Transaction transaction = getCurrentSession(ctx).beginTransaction();
        try {
            for (AtividadeFicha atividadeFicha : atividadeFichas) {
                ResultSet rs;
                if (!UteisValidacao.emptyNumber(atividadeFicha.getCodigo())) {
                    rs = scriptUpdate(ctx, atividadeFicha);
                } else {
                    rs = scriptCreate(ctx, atividadeFicha);
                }
                if (rs.next()) {
                    atividadeFicha.setCodigo(rs.getInt("codigo"));
                }
            }
            transaction.commit();
            return updateList(ctx, atividadeFichas);
        } catch (Exception ex) {
            transaction.rollback();
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, ex);
        }
    }

    private ResultSet scriptCreate(String ctx, AtividadeFicha atividadeFicha) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO AtividadeFicha (");
        sql.append("nome, nomeatividadealteradomanualmente, ordem, intensidade, setId, atividade_codigo, ficha_codigo, ");
        sql.append("metodoExecucao, versao)");
        sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        Connection connection = getConnection(ctx);
        PreparedStatement stm = connection.prepareStatement(sql.toString());
        stm.setString(i++, !UteisValidacao.emptyString(atividadeFicha.getNome()) ? atividadeFicha.getNome() : atividadeFicha.getAtividade().getNome());
        stm.setBoolean(i++, atividadeFicha.getNomeAtividadeAlteradoManualmente());
        stm.setInt(i++, atividadeFicha.getOrdem());
        if (!UteisValidacao.emptyNumber(atividadeFicha.getIntensidade())) {
            stm.setInt(i++, atividadeFicha.getIntensidade());
        } else {
            stm.setNull(i++, 0);
        }
        stm.setString(i++, !UteisValidacao.emptyString(atividadeFicha.getSetId()) ? atividadeFicha.getSetId() : "");
        stm.setInt(i++, atividadeFicha.getAtividade().getCodigo());
        stm.setInt(i++, atividadeFicha.getFicha().getCodigo());
        if (atividadeFicha.getMetodoExecucao() != null && !UteisValidacao.emptyNumber(atividadeFicha.getMetodoExecucao().ordinal())) {
            stm.setInt(i++, atividadeFicha.getMetodoExecucao().ordinal());
        } else {
            stm.setNull(i++, 0);
        }
        stm.setInt(i++, atividadeFicha.getVersao());
        ResultSet resultSet = stm.executeQuery();
        connection.close();
        return resultSet;
    }

    private ResultSet scriptUpdate(String ctx, AtividadeFicha atividadeFicha) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE AtividadeFicha SET ");
        sql.append("nome = ?, nomeatividadealteradomanualmente=?, ordem = ?, intensidade = ?, setId = ?, atividade_codigo = ?, ficha_codigo = ?, metodoExecucao = ?, versao = ?, ");
        sql.append("complementonomeatividade = ? ");
        sql.append("WHERE codigo = ? ");
        sql.append(" RETURNING codigo");

        int i = 1;
        Connection connection = getConnection(ctx);
        PreparedStatement stm = connection.prepareStatement(sql.toString());
        stm.setString(i++, !UteisValidacao.emptyString(atividadeFicha.getNome()) ? atividadeFicha.getNome() : atividadeFicha.getAtividade().getNome());
        stm.setBoolean(i++, atividadeFicha.getNomeAtividadeAlteradoManualmente());
        stm.setInt(i++, atividadeFicha.getOrdem());
        if (!UteisValidacao.emptyNumber(atividadeFicha.getIntensidade())) {
            stm.setInt(i++, atividadeFicha.getIntensidade());
        } else {
            stm.setNull(i++, 0);
        }
        stm.setString(i++, atividadeFicha.getSetId());
        stm.setInt(i++, atividadeFicha.getAtividade().getCodigo());
        stm.setInt(i++, atividadeFicha.getFicha().getCodigo());
        if (atividadeFicha.getMetodoExecucao() != null && !UteisValidacao.emptyNumber(atividadeFicha.getMetodoExecucao().ordinal())) {
            stm.setInt(i++, atividadeFicha.getMetodoExecucao().ordinal());
        } else {
            stm.setNull(i++, 0);
        }
        stm.setInt(i++, atividadeFicha.getVersao());
        stm.setString(i++, atividadeFicha.getComplementoNomeAtividade());
        stm.setInt(i++, atividadeFicha.getCodigo());
        ResultSet resultSet = stm.executeQuery();
        connection.close();
        return resultSet;
    }

    @Override
    public List<AtividadeFicha> obterPorFicha(String ctx, Integer fichaId) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT af.codigo as codigo, af.ordem as ordem, af.intensidade as intensidade, af.ficha_codigo as ficha_codigo, " +
                    " af.atividade_codigo as atividade_codigo, af.setid as setid, af.metodoexecucao as metodoexecucao, " +
                    " af.nome as nomeAtividadeFicha, a.nome as nomeAtividade, af.complementonomeatividade " +
                    " FROM AtividadeFicha af " +
                    " INNER JOIN atividade a on a.codigo = af.atividade_codigo " +
                    " WHERE ficha_codigo = ").append(fichaId);

            List<AtividadeFicha> atividadesFicha;
            try (ResultSet rs = createStatement(ctx, sql.toString())) {
                atividadesFicha = new ArrayList<>();

                while (rs.next()) {
                    AtividadeFicha atividadeFicha = new AtividadeFicha();
                    atividadeFicha.setCodigo(rs.getInt("codigo"));
                    atividadeFicha.setNome(rs.getString("nomeAtividade"));
                    atividadeFicha.setOrdem(rs.getInt("ordem"));
                    atividadeFicha.setIntensidade(rs.getInt("intensidade"));
                    atividadeFicha.setFicha(new Ficha(rs.getInt("ficha_codigo")));
                    AtividadeDao atividadeDao = (UtilContext.getBean(AtividadeDao.class));
                    atividadeFicha.setAtividade(atividadeDao.obterAlgunsCamposPorCodigo(ctx, rs.getInt("atividade_codigo")));
                    atividadeFicha.setSetId(rs.getString("setid"));
                    if (!UteisValidacao.emptyNumber(rs.getInt("metodoexecucao"))) {
                        atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromOrdinal(rs.getInt("metodoexecucao")));
                    }
                    SerieDao serieDao = (UtilContext.getBean(SerieDao.class));
                    atividadeFicha.setSeries(serieDao.obterPorAtividadeFicha(ctx, atividadeFicha.getCodigo()));
                    atividadeFicha.setComplementoNomeAtividade(rs.getString("complementonomeatividade"));
                    atividadesFicha.add(atividadeFicha);
                }
            }

            return atividadesFicha;
        } catch (Exception ex) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_BUSCAR_ATIVIDADES_FICHA, ex);
        }
    }

    @Override
    public List<Integer> findFichaCodigosByAtividadeCodigo(String ctx, Integer atividadeCodigo) throws ServiceException {
        List<Integer> codigosFicha = new ArrayList<>();
        String sql = "SELECT ficha_codigo FROM atividadeficha WHERE atividade_codigo = " + atividadeCodigo;

        try (ResultSet rs = createStatement(ctx, sql)) {
            while (rs.next()) {
                codigosFicha.add(rs.getInt("ficha_codigo"));
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao verificar uso da atividade em fichas.", e);
        }

        return codigosFicha;
    }

    @Override
    public void deleteByAtividadeCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException {
        if (atividadeCodigos == null || atividadeCodigos.isEmpty()) {
            return;
        }
        try {
            String hql = "DELETE FROM AtividadeFicha WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hql).setParameterList("codigos", atividadeCodigos).executeUpdate();
        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir AtividadeFicha por códigos de atividade.", e);
        }
    }
}
