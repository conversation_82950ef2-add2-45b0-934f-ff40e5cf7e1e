/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.tipowod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.wod.FiltroTipoWodJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.tipowod.TipoWodDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.TipoWodExcecoes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class TipoWodDaoImpl extends DaoGenericoImpl<TipoWod,Integer> implements TipoWodDao{

    private static final int MAXIMO_TIPOS_WOD_LISTAR = 50;

    @Override
    public List<TipoWod> listarTiposWod(String ctx, String filtroNome, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAXIMO_TIPOS_WOD_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_TIPOS_WOD_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj FROM TipoWod obj ");
        if ((filtroNome != null) && (!filtroNome.trim().equals(""))) {
            where.append(" where upper(obj.nome) like '").append(filtroNome.toUpperCase()).append("%'");
        }
        if (where.length() > 0) {
            hql.append(where.toString());
        }
        hql.append(paginadorDTO.getSQLOrderBy());
        List<TipoWod> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, null).longValue());
            }
            lista = findByParam(ctx, hql.toString(), new HashMap<String, Object>(), maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPOS_WOD, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    @Override
    public List<TipoWod> listarTiposWod(String ctx, FiltroTipoWodJSON filtros) throws ServiceException {
        getCurrentSession(ctx).clear();
        StringBuilder hql = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            hql.append("where upper(obj.nome) like  CONCAT('%',:nome,'%')");
            param.put("nome", filtros.getParametro().toUpperCase());
        }
        try {
            return findByParam(ctx, hql, param);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPOS_WOD, e);
        }
    }

    /**
     *  getCurrentSession(limpa a instância do entity, porque tem uma thread de conexão na classe EntityManagerService que abre várias instânicas);
     * @param ctx
     * @param query
     * @param params
     * @return
     * @throws ServiceException
     */
    @Override
    public TipoWod obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findObjectByParam(ctx, query, params);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
