/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.modalidade.FiltroModalidadeJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.aula.ModalidadeDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ModalidadeExcecoes;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 *
 * <AUTHOR>
 */
@Repository
public class ModalidadeDaoImpl extends DaoGenericoImpl<Modalidade, Integer> implements
        ModalidadeDao {
    @Autowired
    private final ConexaoZWService conexaoZWService;

    @Autowired
    public ModalidadeDaoImpl(ConexaoZWService conexaoZWService) {
        this.conexaoZWService = conexaoZWService;
    }

    @Override
    public List<Modalidade> listarModadidades(String ctx, FiltroModalidadeJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = 25;
        int indiceInicial=0;
        HashMap<String, Object> params = new HashMap<String, Object>();

        StringBuilder filtroNomeSB = new StringBuilder();

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 25 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();

        hql.append("SELECT obj FROM Modalidade obj ");

        if ((filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            filtroNomeSB.append("%").append(filtros.getParametro()).append("%");
            where.append(" where upper(obj.nome) like upper(:nome)");
        }
        if (where.length() > 0) {
            hql.append(where.toString());
            params.put("nome", filtroNomeSB.toString());
        }

        hql.append(paginadorDTO.getSQLOrderByUse());

        List<Modalidade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, params).longValue());
            }
            lista = findByParam(ctx, hql.toString(), params, maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_lISTAR_MODALIDADES, e);
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    public int consultarModalidadeHorarioTurma(String ctx, Integer idHorarioTurma) throws ServiceException {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT m.codigo, m.nome ");
        sql.append("FROM modalidade m ");
        sql.append("JOIN turma t ON m.codigo = t.modalidade ");
        sql.append("JOIN horarioturma ht ON ht.turma = t.codigo ");
        sql.append("WHERE ht.codigo = ?");

        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setInt(1, idHorarioTurma);

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt("codigo");
                } else {
                    throw new ServiceException("Modalidade não encontrada para o Horário da Turma fornecido.");
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao consultar a modalidade", e);
        }
    }
}
