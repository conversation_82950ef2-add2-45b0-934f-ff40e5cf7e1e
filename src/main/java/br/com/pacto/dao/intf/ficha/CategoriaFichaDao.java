/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.FiltroCategoriaFichaJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface CategoriaFichaDao extends DaoGenerico<CategoriaFicha, Integer> {
    List<CategoriaFicha> listarCategoriaFichas(String ctx, FiltroCategoriaFichaJSON filtroCategoriaFichaJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
