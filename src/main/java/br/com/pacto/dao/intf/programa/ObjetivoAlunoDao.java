/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.programa;

import br.com.pacto.bean.programa.ObjetivoAluno;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ObjetivoAlunoDao extends DaoGenerico<ObjetivoAluno, Integer> {

    List<ObjetivoAluno> findByMatriculaAlunoEStatus(String ctx, Integer matricula, Integer status, Boolean primario) throws Exception;
}
