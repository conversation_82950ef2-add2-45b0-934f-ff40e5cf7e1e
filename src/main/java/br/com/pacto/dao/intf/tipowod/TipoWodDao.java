/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.tipowod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.wod.FiltroTipoWodJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface TipoWodDao extends DaoGenerico<TipoWod, Integer> {

    List<TipoWod> listarTiposWod(final String ctx, String filtroNome, PaginadorDTO paginadorDTO)throws ServiceException;

    List<TipoWod> listarTiposWod(String ctx, FiltroTipoWodJSON filtros) throws ServiceException;

    TipoWod obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException;
}
