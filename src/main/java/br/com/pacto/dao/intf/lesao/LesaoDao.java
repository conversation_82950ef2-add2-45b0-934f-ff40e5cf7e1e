package br.com.pacto.dao.intf.lesao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.Lesao;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface LesaoDao extends DaoGenerico<Lesao, Integer> {

    List<Lesao> listarLesao(String ctx, FiltroLesaoJSON filtroLesaoJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<Lesao> consultarLesaoPorCliente(Integer codigoCliente, String ctx) throws Exception;
}
