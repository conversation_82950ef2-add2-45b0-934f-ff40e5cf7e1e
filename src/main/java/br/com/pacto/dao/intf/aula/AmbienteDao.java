/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.controller.json.ambiente.*;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AmbienteDao extends DaoGenerico<Ambiente, Integer>{

    List<Ambiente> consultarPorNome(String ctx, FiltroAmbienteJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<TipoAmbienteResponseTO> obterTodosTiposAmbiente(String ctx) throws Exception;

    List<ColetorResponseTO> obterColetoresAmbiente(String ctx) throws Exception;

    List<NivelTurmaResponseTO> obterNiveisTurmaAmbiente(String ctx) throws Exception;

    List<AmbienteResponseTO> obterTodosAtivo(String ctx, FiltroAmbienteJSON filtros) throws Exception;

}
