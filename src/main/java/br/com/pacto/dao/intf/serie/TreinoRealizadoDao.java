/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.serie;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.util.bean.GenericoTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface TreinoRealizadoDao extends DaoGenerico<TreinoRealizado, Integer>{

    Map<String, Object> getTreinos(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception;

    List<Map<String, Object>> getGruposMusculares(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception;

    List<Map<String, Object>> getGruposMuscularesApp(String ctx, Date primeira, Date ultima, Integer cliente) throws Exception;

    List<Map<String, Object>> getGruposPrograma(String ctx, Integer codigoPrograma) throws Exception ;

    Map<PerimetriaEnum, List<GenericoTO>> getAtividades(String ctx, Date primeira, Date ultima, Integer codigoCliente) throws Exception;

    Integer getTreinosByMatricula(String ctx, Date dataHora00, Date date, Integer matricula) throws Exception;
}
