package br.com.pacto.dao.intf.locacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.locacao.Locacao;
import br.com.pacto.controller.json.locacao.FiltrosLocacaoJSON;
import br.com.pacto.controller.json.locacao.LocacaoHorarioTO;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

public interface LocacaoDao extends DaoGenerico<Locacao, Integer>{

    List<Locacao> consultarLocacoes(String ctx, FiltrosLocacaoJSON filtrosLocacaoJSON, Integer empresa, PaginadorDTO paginadorDTO)throws ServiceException;

    boolean isLocacaoVigente(String ctx, Integer codLocacao, Date dia) throws Exception;

    List<LocacaoHorarioTO> consultarDisponibilidadesLocacao(String ctx, Integer codLocacao, Date dia, String diaSemana) throws Exception;
}
