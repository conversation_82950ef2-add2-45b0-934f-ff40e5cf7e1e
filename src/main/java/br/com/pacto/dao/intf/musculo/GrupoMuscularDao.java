/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface GrupoMuscularDao extends DaoGenerico<GrupoMuscular, Integer> {

    List<GrupoMuscularResumidoResponseTO> consultarGruposMusculares(final String ctx, FiltroGrupoMuscularJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;

}
