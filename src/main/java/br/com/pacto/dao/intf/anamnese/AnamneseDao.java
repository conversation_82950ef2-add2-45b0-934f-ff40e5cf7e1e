package br.com.pacto.dao.intf.anamnese;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.AnamneseResponseTO;
import br.com.pacto.bean.atividade.FiltroAtivoEnum;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by <PERSON>ao Alcides on 04/05/2017.
 */
public interface AnamneseDao extends DaoGenerico<Anamnese, Integer> {

    List<Anamnese> listaTodasNaoEParq(String ctx) throws Exception;

    List<Anamnese> listaTodasAtivasIntegradasNaoEParq(String ctx) throws Exception;

    List<Anamnese> listaTodasIntegradasNaoEParq(String ctx) throws Exception;
    List<Anamnese> listaTodasAtivasEParq(String ctx) throws Exception;
}
