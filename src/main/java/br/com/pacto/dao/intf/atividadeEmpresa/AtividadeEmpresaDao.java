/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.atividadeEmpresa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeEmpresaDao extends DaoGenerico<AtividadeEmpresa, Integer>{

    List<AtividadeEmpresa> listarAtividades(String ctx, FiltroAtividadeJSON filtroAtividadeJSON, Integer empresa, PaginadorDTO paginadorDTO) throws ServiceException;

}
