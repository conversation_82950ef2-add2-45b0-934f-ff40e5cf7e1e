/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.controller.json.gestao.FiltroGestaoProgramaDTO;
import br.com.pacto.controller.json.programa.FiltroProgramaTreinoJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ProgramaTreinoDao extends DaoGenerico<ProgramaTreino, Integer> {

    List<ProgramaTreino> programasPredefinidos(String ctx) throws ServiceException;

    List<ProgramaTreino> programasPredefinidos(String ctx, Integer situacao) throws ServiceException;

    ProgramaTreino obterPorId(final String ctx, Integer id) throws ServiceException;

    List<ProgramaTreinoAndamentoTO> consultarAndamento(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ExecucoesTreinoTO> consultarExecucoesTreino(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    ProgramaTreino alterar(String ctx, ProgramaTreino pt) throws ServiceException;

    void atualizarSituacaoProgramaPredefinido(String ctx, Integer id, Integer situacao) throws ServiceException;

    List<ProgramaTreino> obterProgramasAluno(FiltroProgramaTreinoJSON filtros,
                                             PaginadorDTO paginadorDTO,
                                             String ctx,
                                             ClienteSintetico cs) throws ServiceException;

    public List<ProgramaTreino> obterProgramasPorRevisaoIA(String ctx) throws ServiceException;
}
