/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.nivelwod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface NivelWodDao extends DaoGenerico<NivelWod, Integer> {



    List<NivelWod> listarNiveisWod(String ctx, FiltroNivelWodJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    NivelWod obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException;
}
