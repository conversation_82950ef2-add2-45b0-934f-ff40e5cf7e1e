package br.com.pacto.dao.intf.usuario;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

public interface UsuarioDao extends DaoGenerico<Usuario, Integer> {

    Usuario consultarPorAluno(String ctx, ClienteSintetico cs) throws ServiceException;

    List<Usuario> listaUsuarioColaborador(String ctx, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean count) throws ServiceException;

    List<Usuario> listaUsuarioColaboradorTreino(String ctx, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;

    Usuario obterPorId(String ctx, Integer id) throws ServiceException;

    Usuario obterUsuarioPorColaborador(String ctx, Integer colaboradorId) throws ServiceException;

    String obterUsernameAlunoPorFicha(String ctx, Integer fichaId) throws ServiceException;

    boolean verificaSeJaExisteUsuarioComEmail(String ctx, Integer id, String email) throws ServiceException;

    Usuario montarDadosUsuario(String ctx, ResultSet rs, Integer nivelMontarDados) throws Exception;

    Map<String, String> consultaVersaoFotoApp(String ctx, Integer codUsuario) throws Exception;

    Map<String, String> consultaVersaoFotoAppColaborador(String ctx, Integer codigo) throws Exception;
}
