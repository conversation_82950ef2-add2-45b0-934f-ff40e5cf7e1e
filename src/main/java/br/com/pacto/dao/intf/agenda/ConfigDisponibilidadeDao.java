package br.com.pacto.dao.intf.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.ConfigDisponibilidade;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

public interface ConfigDisponibilidadeDao extends DaoGenerico<ConfigDisponibilidade, Integer> {
    List<ConfigDisponibilidade> obterDisponibilidades(String ctx, Integer empresaId, FiltroDisponibilidadeDTO filtro, PaginadorDTO paginadorDTO) throws Exception;

    Integer countTotal(String ctx) throws Exception;

    ConfigDisponibilidade consultarConfiguracaoDaDisponibilidade(String ctx, Agendamento disponibilidade) throws Exception;
}
