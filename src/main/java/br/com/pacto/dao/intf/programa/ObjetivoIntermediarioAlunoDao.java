/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.programa;

import br.com.pacto.bean.programa.ObjetivoIntermediarioAluno;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ObjetivoIntermediarioAlunoDao extends DaoGenerico<ObjetivoIntermediarioAluno, Integer> {

    List<ObjetivoIntermediarioAluno> findByObjetivoAluno(String ctx, Integer codigo) throws Exception;
}
