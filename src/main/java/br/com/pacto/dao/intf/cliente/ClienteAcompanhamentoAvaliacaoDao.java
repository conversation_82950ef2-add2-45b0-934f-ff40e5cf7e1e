package br.com.pacto.dao.intf.cliente;

import br.com.pacto.bean.cliente.ClienteAcompanhamentoAvaliacao;
import br.com.pacto.controller.json.gestao.AvaliacaoAgrupadaDTO;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

public interface ClienteAcompanhamentoAvaliacaoDao extends DaoGenerico<ClienteAcompanhamentoAvaliacao, Integer> {

    ClienteAcompanhamentoAvaliacao findByAcompanhamentoId(String ctx, Integer acompanhamentoId) throws Exception;

    List<AvaliacaoAgrupadaDTO> contarAvaliacoesAgrupadasPorNota(String ctx, Integer idProfessor, Integer empresaId) throws Exception;

}
