/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeMusculoDao extends DaoGenerico<AtividadeMusculo, Integer> {
    List<AtividadeMusculo> obterPorMusculos(String ctx, List<Integer> musculosIds) throws Exception;
}
