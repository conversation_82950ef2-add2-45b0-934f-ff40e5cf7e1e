/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.bean.aula.SpiviSeat;
import br.com.pacto.controller.json.ambiente.FiltroAmbienteJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
    public interface SpiviSeatDao extends DaoGenerico<SpiviSeat, Integer>{


}
