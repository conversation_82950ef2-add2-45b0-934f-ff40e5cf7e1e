/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.perfil;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.perfil.FiltroPerfilJSON;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface PerfilDao extends DaoGenerico<Perfil, Integer>{

    List<Perfil> consultarPerfil(String ctx, FiltroPerfilJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException;
    List<Perfil> listarPerfis(String ctx) throws ServiceException;
}
