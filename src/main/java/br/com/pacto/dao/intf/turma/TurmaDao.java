package br.com.pacto.dao.intf.turma;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.aulaDia.AulaColetivaResponseDTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.NivelTurmaDTO;
import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface TurmaDao {

    TurmaResponseDTO save(String ctx, TurmaResponseDTO turmaDTO) throws Exception;

    AulaColetivaResponseDTO saveAulaColetiva(String ctx, AulaColetivaResponseDTO aulaDTO) throws Exception;

    AulaColetivaResponseDTO updateAulaColetiva(String ctx, AulaColetivaResponseDTO aulaDTO) throws Exception;

    TurmaResponseDTO update(String ctx, TurmaResponseDTO turmaDTO) throws Exception;

    void updateFotoKey(String ctx, Integer codigoTurma, String fotoKey) throws Exception;

    HorarioTurmaResponseDTO saveHorario(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception;

    HorarioTurmaResponseDTO updateHorario(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception;

    HorarioTurmaResponseDTO updateHorarioAulaColetiva(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception;

    AmbienteDTO saveAmbiente(String ctx, AmbienteDTO horarioDTO) throws Exception;

    NivelTurmaDTO saveNivelTurma(String ctx, NivelTurmaDTO nivelTurmaDTO) throws Exception;

    Boolean existsNivelTurmaByDescricao(String ctx, String descricao) throws Exception;

    Boolean existsAmbienteByDescricao(String ctx, String descricao) throws Exception;

    List<TurmaResponseDTO> obterTurmas(String ctx, Integer codTurmaEdit, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtros) throws Exception;

    AulaColetivaResponseDTO obterAulaColetiva(String ctx, Integer codigo) throws Exception;

    TurmaResponseDTO obterTurma(String ctx, Integer codigo) throws Exception;

    List<HorarioTurmaResponseDTO> listarTodosHorariosAtivosAulaColetiva(String ctx, Integer codigoAulaColetiva) throws Exception;

    List<HorarioTurmaResponseDTO> listarHorariosTurma(String ctx, JSONObject filtros, PaginadorDTO paginadorDTO, Integer turma) throws Exception;

    Long consultarPorHorarioTurmaPorPeriodoCount(String ctx, int valorConsulta, java.util.Date dataInicial, java.util.Date dataFinal) throws Exception;

    HorarioTurmaResponseDTO obterHorarioTurma(String ctx, Integer codigo) throws Exception;

    Long existemAlunosEmHorarioFuturo(String ctx, Integer codigoHorario) throws Exception;

    void nrAlunosReposicao(String ctx, HorarioTurmaResponseDTO dto, final String periodo) throws Exception;

    Long consultarPorHorarioTurmaCount(String ctx, int horarioTurma) throws Exception;

    boolean existeControleCredito(String ctx, Integer codigo) throws Exception;

    boolean existemReposicoesParaHorarioTurma(String ctx, HorarioTurmaResponseDTO dto, boolean reposicoesFuturas) throws Exception;

    boolean existeContratoModalidadeHorarioTurma(String ctx, Integer codigo) throws Exception;

    boolean existeAulaDesmarcadaSemReporParaHorarioTurma(String ctx, Integer codigo) throws Exception;

    void excluirHorarioTurma(String ctx, HorarioTurmaResponseDTO dto) throws Exception;

    void desativarHorarioTurma(String ctx, HorarioTurmaResponseDTO dto) throws Exception;

    void desativarHorarioAula(String ctx, HorarioTurmaResponseDTO dto) throws Exception;

    boolean existemAlunosNaTurma(String ctx, TurmaResponseDTO turma) throws Exception;

    List<TurmaVideoDTO> obterListaTurmaVideo(String ctx, Integer codigo) throws Exception;

    TurmaVideoDTO obterTurmaVideo(String ctx, Integer codigo) throws Exception;

    void excluirTurmaVideo(String ctx, TurmaVideoDTO dto) throws Exception;

    TurmaVideoDTO saveTurmaVideo(String ctx, TurmaVideoDTO turmaVideoDTO) throws Exception;

    void updateTurmaVideo(String ctx, TurmaVideoDTO turmaVideoDTO) throws Exception;

    Integer consultarUltimaTurmaSalva(String ctx) throws Exception;

    String obterNomeModalidadePorCodigo(String ctx, Integer codigo);

    Integer obterCodigoNivelTurmaSN(String ctx);

    String obterDiasSemanaHorarioPorTurma(String ctx, Integer codigoTurma) throws Exception;

    void saveHorarioCapacidadeCategoria(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception;

    boolean isTurmaPermiteAulaExperimental(String ctx, Integer codigoTurma);

    boolean alunoJaPossuiCompromissoReposicao(String ctx, Integer codigoHorarioTurma, Integer codigoCliente);

    void validarClienteIdadeEstaValidaParaHorarioTurma(String ctx, Integer codigoHorarioTurma, Integer codigoCliente) throws ServiceException;
}
