package br.com.pacto.dao.intf.anamnese;

import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
public interface RespostaClienteDao extends DaoGenerico<RespostaCliente, Integer> {

    RespostaCliente consultarRespostaClientePorPergunta(String ctx, Integer codigoCliente, Integer codigoPergunta) throws Exception;

    RespostaCliente consultarPorCodigoRespostaClienteParQEPergunta(String ctx, Integer codigoRespostaClienteParQ, Integer codigoPergunta) throws Exception;

    List<RespostaCliente> obterRespostasMaisRecentesPorCliente(String ctx, Integer codigoCliente) throws Exception;

    }
