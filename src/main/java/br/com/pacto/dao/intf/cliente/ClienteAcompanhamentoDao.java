/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.cliente;

import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.dao.intf.base.DaoGenerico;

/**
 *
 * <AUTHOR>
 */
public interface ClienteAcompanhamentoDao extends DaoGenerico<ClienteAcompanhamento, Integer> {

    ClienteAcompanhamento buscarUltimoAcompanhamentoDoCliente(String ctx, Integer codigoCliente, Integer codigoProfessor);

}
