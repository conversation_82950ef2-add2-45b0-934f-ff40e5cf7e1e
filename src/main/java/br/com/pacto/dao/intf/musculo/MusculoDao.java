/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface MusculoDao extends DaoGenerico<Musculo, Integer> {

    List<Musculo> consultarMusculos(String ctx, FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;
    
}
