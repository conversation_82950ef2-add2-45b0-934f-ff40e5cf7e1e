/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.nivel;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface NivelDao extends DaoGenerico<Nivel, Integer>{

    List<NivelResponseTO> consultar(String ctx, String nome, Integer ordem)throws ServiceException;
    List<Nivel> consultarNivel(String ctx, FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;
    Number consultarNivelMaiorOrdem(String ctx, Integer codigo)throws ServiceException;
}
