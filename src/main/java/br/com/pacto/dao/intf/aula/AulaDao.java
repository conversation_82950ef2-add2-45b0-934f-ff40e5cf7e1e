/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Aula;
import br.com.pacto.controller.json.aulaDia.FiltroAulasJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.AgendadoJSON;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AulaDao extends DaoGenerico<Aula, Integer>{

    List<Aula> aulasDiaSemana(String ctx, String dia) throws Exception;
    List<AgendadoJSON> obterAlunosPorAulaHorario(String ctx, Date dia, Integer codigo) throws Exception;
    List<Aula> listarAulas(String ctx, FiltroAulasJ<PERSON><PERSON> filtros, String professor<PERSON><PERSON><PERSON><PERSON>,String ambienteSelecionado, PaginadorDTO paginadorDTO)throws ServiceException;
}