/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.agenda;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public interface AgendamentoDao extends DaoGenerico<Agendamento, Integer> {

    List<Agendamento> obterDisponibilidadePorNSUApartirDia(String ctx, Integer nsu, Date dia, Date limite) throws Exception;

    Set<Integer> obterNsuApartirDia(String ctx, Date dia) throws Exception;

    void atualizarAlgunsCamposLista(String ctx, List<Integer> agendamentosId, Integer nsu) throws Exception;

    void alterarObservacaoAgendamento(String ctx, Integer agendamento, String observacao) throws ServiceException;

    List<Agendamento> obterDisponibilidadePorDia(String ctx, Integer empresa, Date dia, Date limite) throws Exception;

    public boolean verificarAlunoNaCarteira(String ctx, Integer professorCodigo, Integer clienteCodigo) throws Exception;

    }
