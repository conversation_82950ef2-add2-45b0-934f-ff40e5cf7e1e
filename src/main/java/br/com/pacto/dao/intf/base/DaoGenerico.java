package br.com.pacto.dao.intf.base;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.service.exception.ServiceException;
import org.hibernate.Session;

public interface DaoGenerico<T, ID extends Serializable> {

    Class<T> getObjectClass();
    
    Session getCurrentSession(final String ctx);

    T insert(final String ctx, T object) throws Exception;
    T insertOrMerge(final String ctx, T object) throws Exception;

    T insertNoClear(final String ctx, T object) throws Exception;

    T insertNoFlush(final String ctx, T object) throws Exception;

    T findById(final String ctx, ID id) throws Exception;

    T update(final String ctx, T object) throws Exception;

    T updateNoClear(final String ctx, T object) throws Exception;

    T updateNoFlush(final String ctx, T object) throws Exception;

    void delete(final String ctx, T object) throws Exception;

    void deleteV2(final String ctx, T object) throws Exception;

    /**
     * Deleta um objeto no banco usando somente seu ID como referencia.
     *
     * @param contexto Contexto que guarnece o objeto
     * @param id       ID do objeto
     * @throws Exception Caso ocorra algum problema na remoção
     */
    void delete(final String contexto, final ID id) throws Exception;

    void deleteNoClear(final String ctx, T object) throws Exception;

    List<T> findAll(final String ctx) throws Exception;

    /**
     *
     * @param ctx
     * @param query
     * @param params
     * @param index parametro que traz o maxResult na posição [0] e firstResult na posição [1]
     * @return
     * @throws Exception
     */
    List<T> findByParam(final String ctx, String query, Map<String, Object> params, Integer... index)
            throws Exception;

    List<T> findByParam(final String ctx, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    List<T> findByParam(final String ctx, final StringBuilder whereClause, Map<String, Object> params,
            int max, int index) throws Exception;

    List<T> findByParam(final String ctx, String query, Map<String, Object> params, int max, int index) throws Exception;

    T findObjectByParam(final String ctx, String queryS, Map<String, Object> params) throws Exception;

    T findFirstObjectByParam(final String ctx, String queryS, Map<String, Object> params) throws Exception;

    void refresh(final String ctx, T object) throws Exception;

    void refresh(final String ctx, List<T> object) throws Exception;

    T insertOrGetObjectForName(final String ctx, final String nome) throws Exception;

    T insertOrGetObjectForName(final String ctx, T object, final String attributeName) throws Exception;

    T findObjectByAttribute(final String ctx, final String attribute, final Object value)
            throws Exception;

    T findObjectByAttributes(final String ctx, final String[] atributos, final Object[] valores, final String orderBY)
            throws Exception;

    List<T> findListByAttributes(final String ctx, final String[] atributos, final Object[] valores, final String orderBY, final int maxResults, Integer... index)
            throws Exception;

    List<ProgramaTreino> findListByAttributesProgramTreino(final String ctx, final String[] atributos, final Object[] valores, final String orderBY, final int maxResults, Integer... index)
            throws Exception;

    String health(final String ctx) throws Exception;

    Number count(final String ctx, final String atributoCount, final String[] atributos, final Object[] valores) throws Exception;

    Number countWithParam(final String ctx, final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    Number countWithParamHqlFull(final String ctx, final String hql, Map<String, Object> params) throws Exception;

    Number countWithParamHqlFullFromSizeList(final String ctx, final String hql, Map<String, Object> params) throws Exception;

    List<T> findObjectsByAttributesSimple(final String ctx, final String[] colunas,
            final String[] atributos, final Object[] valores,
            final String orderBY, final int maxResults, Integer... index) throws Exception;

    void deleteComParam(final String ctx, String[] atributos, Object[] valores) throws Exception;

    void executeQuery(final String ctx, String hql) throws Exception;

    int executeNativeSQL(final String ctx, String sql) throws Exception;

    int executeNativeSQL(final String ctx, String sql, boolean rolback) throws Exception;

    void updateAlgunsCampos(final String ctx, String[] atributos, Object[] valores,
            String[] campos,
            Object[] valoresCampos) throws Exception;

    Integer insertAlgunsCampos(final String ctx, String[] atributos, Object[] valores) throws Exception;

    List listOfObjects(final String ctx, final String sql) throws Exception;

    void removeEnumValue(final String ctx, String atributo, Integer valor) throws Exception;

    Number sum(final String ctx, final String atributoSum, final String[] atributos, Object[] valores) throws Exception;

    Number sumWithParam(final String ctx, final String atributoCount, final String whereClause, Map<String, Object> params) throws Exception;

    Number avgWithParam(final String ctx, final String atributoCount, final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    Number numberWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    List<Number> listNumberWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    List<String> listStringWithParam(final String ctx, final String queryStr,
            final StringBuilder whereClause, Map<String, Object> params) throws Exception;

    boolean exists(final String ctx, final Object bean, final String attrName);

    boolean exists(final String ctx, final Object bean, final String attrName, final String codName);

    boolean existsWithParam(final String ctx, final StringBuilder whereClause) throws Exception;

    ResultSet createStatement(final String ctx, final String sql) throws Exception;
    ResultSet createPreparedStatement(String ctx, String sql, Object... params) throws Exception;

    Connection getConnection(final String ctx) throws Exception;
    
    void deleteAll(final String ctx) throws Exception;

    void executeNative(final String ctx, final String sql) throws Exception;

    void prepare(final String ctx) throws ServiceException;

    List findByParamNative(final String ctx, String query, Map<String, Object> params, int max, int index) throws Exception;

    void updateSomeFildsNoFlush(final String ctx, String[] atributos, Object[] valores,
                            String[] campos,
                            Object[] valoresCampos) throws Exception;

    List<T> updateList(final String ctx, List<T> objects) throws Exception;

    void deleteList(final String ctx, List<T> objects) throws Exception;

    T findByIdClearSession(final String ctx, ID id) throws Exception;
}
