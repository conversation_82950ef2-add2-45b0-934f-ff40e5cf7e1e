/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.cliente;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClienteObservacaoDao extends DaoGenerico<ClienteObservacao, Integer> {

    /**
     * Procura no banco de dados as observações do cliente que possui a matrícula informada.
     *
     * @param contexto         O contexto (banco) que possui os clientes
     * @param matriculaCliente A matrícula do cliente
     * @return A lista de observações encontradas para este cliente
     * @throws Exception Caso ocorra algum problema na consulta
     */
    List<ClienteObservacao> consultarPorMatriculaCliente(String contexto, String matriculaCliente) throws Exception;
    List<ClienteObservacao> consultarPorMatriculaCliente(String contexto, String matriculaCliente, boolean anexoAvaliacao) throws Exception;
    List<ClienteObservacao> consultarPorMatriculaClienteApp(String contexto, String matriculaCliente, int maxResult, int index) throws Exception;
}
