package br.com.pacto.dao.intf.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ClientePesquisaDao extends DaoGenerico<ClientePesquisa, Integer> {
    List<ClientePesquisa> consultarClientesPesquisa(String ctx, String parametro, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;
}
