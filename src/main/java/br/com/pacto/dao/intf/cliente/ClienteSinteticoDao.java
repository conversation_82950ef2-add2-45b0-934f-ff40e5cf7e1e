/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.aluno.FiltroAlunoJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.json.ClienteSintenticoJson;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ClienteSinteticoDao extends DaoGenerico<ClienteSintetico, Integer> {

    /**
     * Obtém os clientes pelos filtros a seguir<br/>
     * <b>ATENÇÃO:</b> a lista de nomes e de matrículas não podem ser nulas simultaneamente.
     *
     * @param contexto   Chave do banco
     * @param empresaZW  ID da empresa ZW (pode ser nulo)
     * @param professor  ID do professor ao qual os alunos estão vinculados (pode ser nulo)
     * @param nomes      Nomes dos alunos (pode ser nulo)
     * @param matriculas Matrículas dos alunos (pode ser nulo)
     * @param maxResults Número máximo de resultados (se for nulo ou igual a zero, não haverá limitação)
     * @return Os alunos encontrados de acordo com os filtros
     * @throws Exception Caso ocorra algum problema na consulta ou caso a lista de nomes e matrículas sejam nulas simultaneamente.
     */
    List<ClienteSintetico> findByEmpresaProfessorNomesMatriculasIn(String contexto, Integer empresaZW, Integer professor,
                                                                   List<String> nomes, List<Integer> matriculas, Integer maxResults) throws Exception;

    List<ClienteSintetico> consultarClientesSinteticos(String ctx, FiltroAlunoJSON filtros, String niveisSelecionados,
                                                       String situacoesEnumSelecionados, String statusClienteEnumSelecionado,
                                                       PaginadorDTO paginadorDTO, Integer empresaId, Integer codigoColaborador, Integer empresaZw) throws ServiceException;
    boolean consultarClientesSinteticosNiveis(Integer id, String ctx) throws ServiceException;
    ClienteSintetico obterPorId(String ctx, Integer id) throws ServiceException;
    List<ClienteSintetico> consultarClientesSinteticosIntegracaoFera(String ctx) throws Exception;
    List<ClienteSintetico> consultarResultadoEvolucaoBiPorIndicadorAvaliacaoFisica(
            String ctx,
            IndicadorAvaliacaoFisicaEnum indicadorAvaliacaoFisica,
            Integer empresaId,
            Integer codigoProfessor,
            String parametro,
            PaginadorDTO paginadorDTO) throws ServiceException;

    ArrayList<Map.Entry<String, Integer>> countResultadosAlunos(
            String ctx,
            IndicadorAvaliacaoFisicaEnum indicadorAvaliacaoFisica,
            Integer empresaId,
            Integer codigoProfessor,
            String parametro,
            PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteSintetico> consultarAlunosSemUsuario(String ctx) throws ServiceException;

    List<ClienteSintetico> consultarClientesNaoInclusoClientePesquisa(String ctx) throws  ServiceException;

    ClienteSintetico consultarSimplificadoPorCodigoCliente(final String ctx, final Integer id) throws ServiceException;

    List<ClienteSintenticoJson> findClienteSintetico(String ctx, List<AgendadoJSON> alunos) throws Exception;

    List consultarMatriculaClientesStatusNivel(String ctx, String statusClienteEnumSelecionado, String niveisSelecionados,
                                               Integer codigoProfessor, Integer empresaZw, List<Integer> colaboradorZw) throws Exception;
      /**
     +     * Executa uma query SQL nativa para buscar ClienteSintetico
     +     *
     +     * @param ctx Contexto do banco de dados
     +     * @param query Query SQL nativa a ser executada
     +     * @return Lista de ClienteSintetico resultante da query
     +     * @throws ServiceException em caso de erro na execução da query
     +     */

    List<ClienteSintetico> findClienteSinteticoQueryNativa(String ctx, String query) throws Exception;
}
