/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AulaAlunoDao extends DaoGenerico<AulaAluno, Integer>{

    List<AulaAluno> alunosHorarioDia(String ctx, Integer aulaHorarioId, Date dia) throws Exception;
}
