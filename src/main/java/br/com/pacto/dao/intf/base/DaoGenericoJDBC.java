/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.base;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import javax.persistence.EntityManager;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public interface DaoGenericoJDBC<T, ID extends Serializable> {

    <T> List<T> consultar(final String ctx, Class<T> clazz) throws Exception;

    <T> List<T> consultar(final String ctx, Class<T> clazz, String condicao) throws Exception;

    <T> T consultarPorChavePrimaria(final String ctx, Class<T> clazz, int codigo) throws Exception;

    ResultSet createStatement(final String ctx, final String sql) throws Exception;

    ResultSet criarConsulta(final String ctx, final String sql) throws Exception;

    void excluir(final String ctx, T obj) throws Exception;

    Connection getConnection(final String ctx) throws Exception;

    Session getCurrentSession(final String ctx);

    EntityManager getEntityManager(final String ctx) throws Exception;

    List<Field> getValidFields(final Class<?> cls);

    void inserir(final String ctx, T obj) throws Exception;

    <T> T montarDados(ResultSet rs, Class<T> clazz) throws Exception;

    int obterUltimoCodigoGeradoTabela(final String ctx, String nomeTabela) throws Exception;

    StringBuffer splitBetween(final String prefixo, final String[] vet);

    StringBuffer splitILike(final String prefixo, final String[] vet);

    StringBuffer splitInverse(final String prefixo, final String[] vet);
    
}
