/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.controller.json.modalidade.FiltroModalidadeJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ModalidadeDao extends DaoGenerico<Modalidade, Integer>{

    List<Modalidade> listarModadidades(String ctx, FiltroModalidadeJSON filtros, PaginadorDTO pagindaorDTO) throws ServiceException;
    public int consultarModalidadeHorarioTurma(String ctx, Integer idHorarioTurma) throws ServiceException;
}
