/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ProfessorSubstituidoDao extends DaoGenerico<ProfessorSubstituido, Integer>{

    List<ProfessorSubstituido> obterProfessoresSubstituidos(String ctx, Date inicio, Date fim) throws Exception;

    ProfessorSubstituido  obterProfessorSubstituido(String ctx, Date dataHoje, int codigoHorarioTurma) throws Exception;

    void removerPorAulaHorario(String ctx, List<Integer> aulasHorarioId) throws Exception;

    void removerPorAula(String ctx, Integer aulaId) throws Exception;

    List<ProfessorSubstituido> filtrarProfessoresSubstituidos(String ctx, FiltroGestaoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;
}
