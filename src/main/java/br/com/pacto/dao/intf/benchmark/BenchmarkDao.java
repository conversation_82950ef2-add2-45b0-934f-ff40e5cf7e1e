package br.com.pacto.dao.intf.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.controller.json.benchmark.BenchmarkResponseTO;
import br.com.pacto.controller.json.benchmark.FiltroBenchmarkJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by Rafael on 11/07/2016.
 */
public interface BenchmarkDao  extends DaoGenerico<Benchmark, Integer> {

    List<Benchmark> listarBenchmarks(String ctx, FiltroBenchmarkJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    Benchmark consultarBenchmark(String ctx, String nome, Integer codigo) throws ServiceException;
}
