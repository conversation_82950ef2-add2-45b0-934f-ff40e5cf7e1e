/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.pessoa;

import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

/**
 *
 * <AUTHOR>
 */
public interface StatusPessoaDao extends DaoGenerico<StatusPessoa, Integer> {
    /**
     * Realiza a remoção das {@link StatusPessoa} vinculadas ao {@link Usuario}
     * @param ctx chave do banco
     * @param usuario {@link Usuario}
     * @throws ServiceException
     */
    void removerPorUsuario(String ctx, Usuario usuario) throws  Exception;
}
