/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aparelho;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import br.com.pacto.controller.json.programa.AparelhoTO;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aparelho.FiltroAparelhoJSON;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AparelhoDao  extends DaoGenerico<Aparelho, Integer>{

  List<AparelhoResponseTO> consultarAparelhos(final String ctx, FiltroAparelhoJSON filtroAparelhoJSON, PaginadorDTO paginadorDTO)throws ServiceException;

  List<AparelhoResponseTO> consultarAparelhosHabilitadosReservaEquipamento(final String ctx)throws ServiceException;

  boolean aparelhoDuplicado(String ctx, AparelhoTO aparelhoTO) throws Exception;
    
}
