/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.perfil.permissao;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface PermissaoDao extends DaoGenerico<Permissao, Integer>{

    Permissao obterPermissaoPorPerfilRecurso(final String ctx,  final Integer perfil,  final RecursoEnum recurso) throws ServiceException;

}
