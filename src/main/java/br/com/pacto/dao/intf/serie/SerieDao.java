/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.serie;

import br.com.pacto.bean.serie.Serie;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface SerieDao extends DaoGenerico<Serie, Integer>{

    void removeList(String ctx, List<Serie> series) throws Exception;

    List<Serie> atualizarList(String ctx, List<Serie> series) throws Exception;

    List<Serie> obterPorAtividadeFicha(String ctx, Integer atividadeFichaId) throws ServiceException;
}
