/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.controller.json.aulaExcluida.FiltroAulaExcluidaJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AulaDiaExclusaoDao extends DaoGenerico<AulaDiaExclusao, Integer>{

    List<AulaDiaExclusao> obterAulasExcluidas(String ctx, Date inicio, Date fim) throws Exception;

    List<AulaDiaExclusao> consultarOrdenada(String ctx, FiltroAulaExcluidaJSON filtros, String idsHorarioTurma, PaginadorDTO paginadorDTO) throws ServiceException;
}
