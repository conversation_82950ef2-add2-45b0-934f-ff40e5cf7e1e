/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.converter;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.JSFUtilities;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("ProfessorConverter")
public class ProfessorConverter implements Converter{
    @Override
    public Object getAsObject(FacesContext fc, UIComponent uic, String value) {
        if(value != null && value.trim().length() > 0) {
            ProfessorSinteticoService pss = (ProfessorSinteticoService)UtilContext.getBean(ProfessorSinteticoService.class);
            try {
                return pss.obterPorId(JSFUtilities.getKey(), Integer.valueOf(value));
            } catch (ServiceException ex) {
                Uteis.logar(ex, ProfessorConverter.class);
            }
            return null;
        }
        else {
            return null;
        }
    }
 
    @Override
    public String getAsString(FacesContext fc, UIComponent uic, Object object) {
        if(object != null) {
            return String.valueOf(((ProfessorSintetico) object).getCodigo());
        }
        else {
            return null;
        }
    }   
}
