/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.converter;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.gestaopersonal.AlunoAulaPersonal;
import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.JSFUtilities;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("AlunoAulaPersonalConverter")
public class AlunoAulaPersonalConverter implements Converter {

    @Override
    public Object getAsObject(FacesContext fc, UIComponent uic, String value) {
        if (value != null && value.trim().length() > 0 && !value.equals("null")) {
            AlunoAulaPersonal alunoAula = new AlunoAulaPersonal();
            if (value.startsWith("CLI")) {
                ClienteSinteticoService css = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
                try {
                    alunoAula.setCliente(css.obterPorId(JSFUtilities.getKey(), Integer.valueOf(value.replaceAll("CLI", ""))));
                } catch (Exception ex) {
                    Uteis.logar(ex, ProfessorConverter.class);
                }
            } else if (value.startsWith("COL")) {
                ProfessorSinteticoService pss = (ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class);
                try {
                    alunoAula.setColaborador(pss.obterPorId(JSFUtilities.getKey(), Integer.valueOf(value.replaceAll("COL", ""))));
                } catch (Exception ex) {
                    Uteis.logar(ex, ProfessorConverter.class);
                }
            }
            return alunoAula;
        } else {
            return null;
        }
    }

    @Override
    public String getAsString(FacesContext fc, UIComponent uic, Object object) {
        if (object != null) {
            String str = "";
            if (((AlunoAulaPersonal) object).getCodigoCliente() == null) {
                if (((AlunoAulaPersonal) object).getCodigoColaborador() != null) {
                    str = "COL" + ((AlunoAulaPersonal) object).getCodigoColaborador();
                }
            } else {
                str = "CLI" + ((AlunoAulaPersonal) object).getCodigoCliente();
            }
            return str;
        } else {
            return null;
        }
    }
}
