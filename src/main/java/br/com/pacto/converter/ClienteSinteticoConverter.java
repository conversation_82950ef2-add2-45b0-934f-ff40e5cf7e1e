/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.converter;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.JSFUtilities;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 *
 * <AUTHOR>
 */
@FacesConverter("ClienteSinteticoConverter")
public class ClienteSinteticoConverter implements Converter{
    
    @Override
    public Object getAsObject(FacesContext fc, UIComponent uic, String value) {
        if(value != null && value.trim().length() > 0 && !value.equals("null")) {
            ClienteSinteticoService css = (ClienteSinteticoService)UtilContext.getBean(ClienteSinteticoService.class);
            try {
                return css.obterPorId(JSFUtilities.getKey(), Integer.valueOf(value));
            } catch (Exception ex) {
                Uteis.logar(ex, ProfessorConverter.class);
            }
            return null;
        }
        else {
            return null;
        }
    }
 
    @Override
    public String getAsString(FacesContext fc, UIComponent uic, Object object) {
        if(object != null) {
            return String.valueOf(((ClienteSintetico) object).getCodigo());
        }
        else {
            return null;
        }
    }   
}
