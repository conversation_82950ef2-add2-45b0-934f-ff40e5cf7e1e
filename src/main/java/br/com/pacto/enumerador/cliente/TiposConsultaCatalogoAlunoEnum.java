package br.com.pacto.enumerador.cliente;

public enum TiposConsultaCatalogoAlunoEnum {
    TODOS,
    CARTEIRA,
    ACOMPANHANDO,
    AGENDADO,
    DESACOMPANHADO,
    ACADEMIA,
    ACADEMIA_TREINO_VENCER,
    ACADEMIA_TREINO_VENCIDO,
    ACADEMIA_CARTEIRA,
    SEM_TREINO,
    TREINO_VENCIDO;

    public StatusClienteEnum getStatusClienteEnum() {
        switch (this) {
            case TODOS: return StatusClienteEnum.TODOS;
            case CARTEIRA: return StatusClienteEnum.MINHA_CARTEIRA;
            case ACOMPANHANDO: return StatusClienteEnum.ACOMPANHANDO;
            case AGENDADO: return StatusClienteEnum.AGENDADOS;
            case DESACOMPANHADO: return StatusClienteEnum.DESACOMPANHADOS;
            case ACADEMIA: return StatusClienteEnum.NA_ACADEMIA;
            case ACADEMIA_TREINO_VENCER: return StatusClienteEnum.NA_ACADEMIA_TREINO_A_VENCER;
            case ACADEMIA_TREINO_VENCIDO: return StatusClienteEnum.NA_ACADEMIA_TREINO_VENCIDO;
            case ACADEMIA_CARTEIRA: return StatusClienteEnum.NA_ACADEMIA_MINHA_CARTEIRA;
            case SEM_TREINO: return StatusClienteEnum.SEM_TREINO;
            /* case TREINO_VENCIDO: */
            default: return StatusClienteEnum.TREINO_VENCIDO;
        }
    }
}
