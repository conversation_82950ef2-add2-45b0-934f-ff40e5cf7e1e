/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.cliente;

/**
 *
 * <AUTHOR>
 */
public enum StatusClienteEnum {
    
    TODOS(0,"Todos"),
    MINHA_CARTEIRA(1, "Minha carteira"),
    NA_ACADEMIA(2, "Na academia"),
    ACOMPANHANDO(3, "Acompanhando"),
    DESACOMPANHADOS(4, "Desacompanhados"),
    AGENDADOS(5, "Agendados"),
    SEM_TREINO(6, "Sem Treino"),
    TREINO_VENCIDO(7, "Treino Vencido"),
    NA_ACADEMIA_TREINO_VENCIDO(8, "Na academia com treino vencido"),
    NA_ACADEMIA_TREINO_A_VENCER(9, "Na academia com treino a vencer"),
    NA_ACADEMIA_MINHA_CARTEIRA(10, "Na academia da minha carteira"),
    NA_ACADEMIA_SEM_TREINO(11, "Na academia sem treino");


    private Integer id;
    private String descricao;

    private StatusClienteEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public static StatusClienteEnum getFromId(Integer id){
        for(StatusClienteEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
    
}
