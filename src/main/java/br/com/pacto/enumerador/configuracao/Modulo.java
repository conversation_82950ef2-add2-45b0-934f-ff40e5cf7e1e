package br.com.pacto.enumerador.configuracao;

import br.com.pacto.base.util.ModuloNaoEncontradoException;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <h1>IMPORTANTE: <PERSON><PERSON><PERSON> você realize alguma alteração nesta classe, favor, refletir a mesma alteração na classe Modulo no módulo LoginAPP ou entre outro módulo se houver.</h1>
 *
 * Repositório para os módulos disponíveis do Sistema.
 *
 * <AUTHOR>
 * @since 08/11/2018
 */
public enum Modulo {

    AULA_CHEIA("SLC"),
    CENTRAL_DE_EVENTOS("CE"),
    CUSTOMER_RELATIONSHIP_MANAGEMENT("CRM"),
    FINANCEIRO("FIN"),
    GAME_OF_RESULTS("GOR"),
    STUDIO("EST"),
    TREINO("TR"),
    Z<PERSON><PERSON><PERSON><PERSON>_WEB("ZW"),
    UNIVERSIDADE_COORPORATIVA_PACTO("UCP"),
    GESTAO_DE_PERSONAL("GP"),
    SMART_BOX("SBX"),
    ZILLYON_AUTO_ATENDIMENTO("ZAA"),
    NOVO_TREINO("NTR")
    ;

    private static final String SEPARADOR_SIGLA_MODULOS = ",";
    private final String siglaModulo;

    Modulo(String siglaModulo) {
        this.siglaModulo = siglaModulo;
    }

    /**
     * @param siglaModulo que será ignorado <b>CASE</b> e espaços no ínicio e no fim.
     *ModuloNaoEncontradoException
     * @return Dado a <code>siglaModulo</code> informada, retorna a <b>ENUM respectiva</b> ou <b>lança {@link ModuloNaoEncontradoException}</b>.
     *
     * @throws ModuloNaoEncontradoException quando através da <code>siglaModulo</code> informada, não for possível encontrar nenhum <b>Módulo</b>.
     */
    public static Modulo fromSigla(String siglaModulo) {
        for (Modulo value : values()) {
            String siglaModuloComTrim = siglaModulo.trim();
            if (value.siglaModulo.equalsIgnoreCase(siglaModuloComTrim)) {
                return value;
            }
        }

        throw new ModuloNaoEncontradoException(siglaModulo);
    }

    /**
     * @param siglasModulos que contenha as siglas dos módulos separados por vírgula ({@link #SEPARADOR_SIGLA_MODULOS}). <br>
     *                      Para cada sigla encontrada, será ignorado <b>CASE</b> e espaços no ínicio e no fim.
     *
     * @return Dado a <code>siglasModulos</code> informada, retorna a(s) <b>ENUM(s) respectiva(s)</b> ou <b>lança {@link ModuloNaoEncontradoException}</b>.
     *
     * @throws ModuloNaoEncontradoException quando através da <code>siglaModulo</code> informada, não for possível encontrar nenhum <b>Módulo</b>.
     */
    public static Set<Modulo> fromSiglas(String siglasModulos) {
        String[] siglas = siglasModulos.split(SEPARADOR_SIGLA_MODULOS);

        Set<Modulo> modulos = new HashSet<Modulo>();
        for (String sigla : siglas) {
            if (StringUtils.isNotBlank(sigla)) {
                modulos.add(Modulo.fromSigla(sigla));
            }
        }

        return modulos;
    }

    /**
     * @param modulosHabilitados a serem verificados.
     * @param modulo             alvo.
     *
     * @return Dado uma coleção de <code>modulosHabilitados</code>, returna <b>TRUE</b> se o <code>modulo</code> informado está presente.
     */
    public static boolean isModuloHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return modulosHabilitados.contains(modulo);
    }

    /**
     * @param modulosHabilitados a serem verificados.
     * @param modulo             alvo.
     *
     * @return Dado uma coleção de <code>modulosHabilitados</code>, returna <b>TRUE</b> se o <code>modulo</code> informado <b>NÃO</b> estiver presente.
     */
    public static boolean isModuloNaoHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return !modulosHabilitados.contains(modulo);
    }

    public String getSiglaModulo() {
        return siglaModulo;
    }
}
