/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.configuracao;

/**
 *
 * <AUTHOR>
 */
public enum EscopoConfiguracaoEnum {

    GLOBAL(0, "Global"),
    EMPRESA(1, "Empresa"),
    USUARIO(2, "Usuario");
    private Integer id;
    private String desc;

    private EscopoConfiguracaoEnum(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }
}
