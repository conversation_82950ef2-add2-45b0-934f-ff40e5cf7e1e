package br.com.pacto.enumerador.anamnese;

import br.com.pacto.bean.anamnese.TipoPerguntaDTOEnum;

/**
 * Created by <PERSON><PERSON> Alcid<PERSON> on 04/05/2017.
 */
public enum TiposPerguntaEnum {
    
    SIM_NAO,
    SIMPLES_ESCOLHA,
    MULTIPLA_ESCOLHA,
    TEXTUAL
    ;

    public TipoPerguntaDTOEnum getTipoPerguntaDTOEnum() {
        switch (this) {
            case SIM_NAO: return TipoPerguntaDTOEnum.SIM_NAO;
            case SIMPLES_ESCOLHA: return TipoPerguntaDTOEnum.ESCOLHA_UNICA;
            case MULTIPLA_ESCOLHA: return TipoPerguntaDTOEnum.ESCOLHA_MULTIPLA;
            case TEXTUAL: return TipoPerguntaDTOEnum.TEXTO;
            /* case TEXTO */
            default: return TipoPerguntaDTOEnum.TEXTO;
        }
    }

    public static TiposPerguntaEnum getFromOrdinal(int ordinal){
        for(TiposPerguntaEnum t : values()){
            if(t.ordinal() == ordinal){
                return t;
            }
        }
        return null;
    }


}
