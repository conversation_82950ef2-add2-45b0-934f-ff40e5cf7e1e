package br.com.pacto.enumerador.crossfit;

import java.util.Arrays;

/**
 * Created by <PERSON> on 20/07/2016.
 */
public enum TipoWodEnum {
  FOR_TIME("For Time", new String[]{"tempo", "nivelCrossfit", "comentario"}, "ORDER BY nivelCrossfit DESC, tempo"),
  FOR_REPS("For Reps and Time", new String[]{"tempo","repeticoes", "nivelCrossfit", "comentario"}, "ORDER BY nivelCrossfit DESC, repeticoes DESC, tempo"),
  FOR_WEIGHT("For Weight", new String[]{"peso", "nivelCrossfit", "comentario"},  "ORDER BY nivelCrossfit DESC, peso DESC"),
  AMRAP("Amrap", new String[]{"rounds", "repeticoes", "nivelCrossfit", "comentario"},  "ORDER BY nivelCrossfit DESC, rounds DESC, repeticoes DESC");

  private String nome;
  private String orderBy;
  private String[] camposResultado;

  TipoWodEnum(String nome, String[] camposResultado, String orderBy){
      this.camposResultado = camposResultado;
      this.nome = nome;
      this.orderBy = orderBy;
  }
  public String getNome() {
        return nome;
  }

    public String[] getCamposResultado() {
        return camposResultado;
    }

    public void setCamposResultado(String[] camposResultado) {
        this.camposResultado = camposResultado;
    }

    public void setNome(String nome) {
        this.nome = nome;
  }

    public static TipoWodEnum obterPorString(String val){
      for(TipoWodEnum tipoWod : values()){
          if(val.toUpperCase().contains(tipoWod.getNome().toUpperCase())){
              return tipoWod;
          }
      }
      return null;
   }

   public static TipoWodEnum obterPorOrdinal(Integer ord){
      for(TipoWodEnum tipoWod : values()){
          if(tipoWod.ordinal() == ord){
              return tipoWod;
          }
      }
      return null;
   }

   public boolean campo(String c){
       return Arrays.asList(camposResultado).contains(c);
   }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }
}
