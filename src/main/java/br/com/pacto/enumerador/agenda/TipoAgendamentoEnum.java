/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.agenda;

/**
 *
 * <AUTHOR>
 */
public enum TipoAgendamentoEnum {
   
    CONTATO_INTERPESSOAL(0,"Contato interpessoal", "contatoInterpessoal","Contato"),
    PRESCRICAO_TREINO(1,"Prescrição de treino", "prescricaoTreino", "Prescrição"),
    REVISAO_TREINO(2,"Revisão de treino", "revisaoTreino", "Revisão"),
    RENOVAR_TREINO(3,"Renovar treino", "renovarTreino", "Renovação"),
    AVALIACAO_FISICA(4,"Avaliação física", "avaliacaoFisica", "Av. física");
    
    private Integer id;
    private String descricao;
    private String descricaoSimples;
    private String css;
    
    private TipoAgendamentoEnum(Integer id, String descricao, String css, String descricaoSimples){
        this.id = id;
        this.descricao = descricao;
        this.css = css;
        this.descricaoSimples = descricaoSimples;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public static TipoAgendamentoEnum getFromId(Integer id){
        for(TipoAgendamentoEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
    public static TipoAgendamentoEnum getFromDescricaoSimples(String descr){
        for(TipoAgendamentoEnum tipo : values()){
            if(tipo.getDescricaoSimples().equals(descr)){
                return tipo;
            }
        }
        return null;
    }

    public String getDescricaoSimples() {
        return descricaoSimples;
    }

    public void setDescricaoSimples(String descricaoSimples) {
        this.descricaoSimples = descricaoSimples;
    }
    
    
}
