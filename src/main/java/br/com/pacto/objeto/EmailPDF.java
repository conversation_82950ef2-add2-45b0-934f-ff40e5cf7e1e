package br.com.pacto.objeto;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.Message;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import com.lowagie.text.*;
import com.lowagie.text.pdf.PdfWriter;


public class EmailPDF {
        /**
         * Sends an email with a PDF attachment.
         */
        public void email() {
            String smtpHost = "smtplw.com.br"; //replace this with a valid host

            int smtpPort = 587;

//            smtpEmailRobo=<EMAIL>
//                    smtpEmailNoReply=<EMAIL>
//                    smtpLoginRobo=pactosolucoes2017
//            smtpSenhaRobo=RlOHgUGw8905
//            smtpConexaoSeguraRobo=true
//            smtpServerRobo=smtplw.com.br
            String sender = "<EMAIL>"; //replace this with a valid sender email address
            String recipient = "<EMAIL>"; //replace this with a valid recipient email address
            String content = "dummy content"; //this will be the text of the email
            String subject = "dummy subject"; //this will be the subject of the email
            String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";
            Properties properties = new Properties();
            Properties props = new Properties();
            props.put("mail.smtp.host", smtpHost);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.port", smtpPort);
            props.put("mail.smtp.socketFactory.port", smtpPort);
            props.put("mail.smtp.connectiontimeout", 5000);
            props.put("mail.smtp.timeout", 5000);
            props.put("mail.smtp.writetimeout", 5000);

            if (true) {
                props.put("mail.smtp.starttls.enable", "true");
            }
            //caso usar SSL
            if (true) {
                props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
            }

            Session session = Session.getInstance(props,
                    new javax.mail.Authenticator() {
                        protected PasswordAuthentication getPasswordAuthentication() {
                            return new PasswordAuthentication("pactosolucoes2017".trim(),
                                    "RlOHgUGw8905".trim());
                        }
                    });

            ByteArrayOutputStream outputStream = null;

            try {
                //construct the text body part
                MimeBodyPart textBodyPart = new MimeBodyPart();
                textBodyPart.setText(content);

                //now write the PDF content to the output stream
                outputStream = new ByteArrayOutputStream();
                byte[] bytes = outputStream.toByteArray();

                //construct the pdf body part
                DataSource dataSource = new ByteArrayDataSource(bytes, "application/pdf");
                MimeBodyPart pdfBodyPart = new MimeBodyPart();
                pdfBodyPart.setDataHandler(new DataHandler(dataSource));
                pdfBodyPart.setFileName("test.pdf");

                //construct the mime multi part
                MimeMultipart mimeMultipart = new MimeMultipart();
                mimeMultipart.addBodyPart(textBodyPart);
                mimeMultipart.addBodyPart(pdfBodyPart);

                //create the sender/recipient addresses
                InternetAddress iaSender = new InternetAddress(sender);
                InternetAddress iaRecipient = new InternetAddress(recipient);

                //construct the mime message
                MimeMessage mimeMessage = new MimeMessage(session);
                mimeMessage.setSender(iaSender);
                mimeMessage.setSubject(subject);
                mimeMessage.setRecipient(Message.RecipientType.TO, iaRecipient);
                mimeMessage.setContent(mimeMultipart);

                //send off the email
                Transport.send(mimeMessage);

                System.out.println("sent from " + sender +
                        ", to " + recipient +
                        "; server = " + smtpHost + ", port = " + smtpPort);
            } catch(Exception ex) {
                ex.printStackTrace();
            } finally {
                //clean off
                if(null != outputStream) {
                    try { outputStream.close(); outputStream = null; }
                    catch(Exception ex) { }
                }
            }
        }

        /**
         * Writes the content of a PDF file (using iText API)
         * to the {@link OutputStream}.
         * @param outputStream {@link OutputStream}.
         * @throws Exception
         */


        /**
         * Main method.
         * @param args No args required.
         */
        public static void main(String[] args) {
            EmailPDF demo = new EmailPDF();
            demo.email();
        }

}
