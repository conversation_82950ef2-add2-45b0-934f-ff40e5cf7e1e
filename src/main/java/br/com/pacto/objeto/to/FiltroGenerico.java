/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto.to;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class FiltroGenerico implements Serializable{
    
    private String nome;
    private Integer codigo;
    private String entidade;
    private String nomeAtributo;
    private boolean selecionado;

    public FiltroGenerico(String nome, Integer codigo, String entidade,String nomeAtributo, boolean selecionado) {
        this.nome = nome;
        this.codigo = codigo;
        this.entidade = entidade;
        this.selecionado = selecionado;
        this.nomeAtributo = nomeAtributo;
    }

    public String getNomeAtributo() {
        return nomeAtributo;
    }

    public void setNomeAtributo(String nomeAtributo) {
        this.nomeAtributo = nomeAtributo;
    }
    
    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
    
    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }
    
}
