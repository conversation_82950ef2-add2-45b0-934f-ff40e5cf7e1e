package br.com.pacto.objeto;

import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Mascara {

    public static final String MASCARA_MOEDA = "R$ #,##0.00";
    public static final String MASCARA_MOEDA_SEM_CIFRAO = "#,##0.00";

    public static String formatarValorSegundoMascara(Double valor, String mascara) {
        DecimalFormat formatador = new DecimalFormat();
        mascara.trim();
        try {
            //retirar aspas se houver
            String REPLACE = "";
            Pattern p = Pattern.compile("[\"]");
            Matcher m = p.matcher(mascara);
            mascara = m.replaceAll(REPLACE);
            //			
            System.out.println("Formatando valor...utilizando a mascara: " + mascara);
            formatador.applyPattern(mascara);
            System.out.println("Valor: " + formatador.format(valor));
            return formatador.format(valor);
        } catch (IllegalArgumentException e) {
            System.out.println("ERRO: Mascara invalida.");
            System.out.println(e.getMessage());
            return "";
        }

        /*String RegEx = "[a-zA-Z[-]]";
         String Str = "NE - 99999".trim();
         String REPLACE = "";
		
         Pattern p = Pattern.compile(RegEx);
         Matcher m = p.matcher(Str);
         Str = m.replaceAll(REPLACE);
		
         System.out.println(Str);*/
    }
}
