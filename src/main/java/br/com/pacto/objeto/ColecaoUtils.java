/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import java.util.Collection;
import java.util.NoSuchElementException;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;

/**
 *
 * <AUTHOR>
 */
public final class ColecaoUtils {

    public static Object find(Collection lista, Predicate predicado) {
        return CollectionUtils.find(lista, predicado);
    }

    public static void filter(Collection lista, Predicate predicado) {
        CollectionUtils.filter(lista, predicado);
    }

    public static boolean exists(Collection lista, Predicate predicado) {
        try {
            return CollectionUtils.exists(lista, predicado);
        }catch (NoSuchElementException e){
            return false;
        }
    }

    public static Collection select(Collection lista, Predicate predicado) {
        return CollectionUtils.select(lista, predicado);
    }
}
