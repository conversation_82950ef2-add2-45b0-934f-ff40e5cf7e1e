package br.com.pacto.objeto;

import java.text.DecimalFormat;
import java.text.NumberFormat;

/**
 * @Classe: Numero.java
 * @Finalidade: utilitario para trabalhar com numeros
 *
 * <AUTHOR>
 * @date 19/03/2008
 */
public class Numero {

    public static int ASCII_0 = 48;

    /**
     * @Metodo: formatDouble
     * @Finalidade: formata um double para decimal
     *
     * @param number
     * @return String
     *
     * <AUTHOR>
     * @date 19/03/2008
     */
    public static String formatDouble(double number) {
        NumberFormat formatter = new DecimalFormat("0.00");

        return formatter.format(number);
    }

    /**
     * @Metodo: doubleBanco
     * @Finalidade: formata um double para passar par o banco
     *
     * @param number - Object
     * @return String
     *
     * <AUTHOR>
     * @date 19/03/2008
     */
    public static String doubleBanco(Object number) {
        if (number == null || number.toString().equals("")) {
            return "0.0";
        }
        String temp = number.toString().replaceAll("\\.", "").replaceAll("\\,",
                "\\.");
        Double d = new Double(temp);
        return d.toString();
    }

    /**
     * @Metodo: getValor
     * @Finalidade: retorna o valor do string
     *
     * @param number
     * @return double
     *
     * <AUTHOR>
     * @date 19/03/2008
     */
    public static double getValor(String number) {
        if (number == null || number.toString().equals("")) {
            return 0;
        }
        String temp = number.toString().replaceAll("\\.", "").replaceAll("\\,",
                "\\.");
        Double d = new Double(temp);
        return d.doubleValue();
    }
    
    public static Integer getInteiro(String number) {
        if (number == null || number.isEmpty()) {
            return 0;
        }
        return new Integer(number);
    }

    /**
     *
     * @Metodo: valorChar
     * @Finalidade: calcula o valor numerico de um tipo char.
     *
     * @param value char
     * @return Integer.parseInt(String.valueOf(value)) int
     *
     * <AUTHOR>
     * @date 30/06/2008
     */
    public static int valorChar(char value) {
        return Integer.parseInt(String.valueOf(value));
    }

    public static String formatCurrency(double valor) {

        NumberFormat nf = NumberFormat.getCurrencyInstance();
        String temp = nf.format(valor);
        return temp.substring(3, temp.length());
    }

    public static double getDouble(String moeda) {

        double b = Double.parseDouble(moeda.replaceAll("\\.", "").replaceAll(",", "\\."));
        return b;
    }
}
