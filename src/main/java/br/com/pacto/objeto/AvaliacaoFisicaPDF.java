package br.com.pacto.objeto;

import br.com.pacto.bean.anamnese.AgrupamentoAvaliacaoIntegradaEnum;
import br.com.pacto.bean.avaliacao.ItemImpressao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.util.ViewUtils;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.*;
import com.lowagie.text.pdf.events.PdfPCellEventForwarder;
import org.apache.commons.lang3.StringUtils;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.servlet.ServletContext;
import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;

public class AvaliacaoFisicaPDF extends UteisPDF {

    public static void criarPDFAvaliacao(String key, Usuario usuario, ItemImpressao item,
                                                  ViewUtils viewUtils, ServletContext servletContext,
                                                  final ByteArrayOutputStream baos) throws Exception {

        // criação do documento
        Document document = new Document();
        PdfWriter pdfWriter = PdfWriter.getInstance(document, baos);
        document.open();
        GrayColor background = new GrayColor(0.8f);

        //icone treino
        float[] columns = {1, 7, 1};

        PdfPTable cabecalho = cabecalho(columns, usuario, key, document);
        PdfPCell titulo = addCell(criarTexto(18, "bold",
                viewUtils.getLabel("AVALIACAO_FISICA")), 3, 0,
                Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, background, Rectangle.NO_BORDER);
        titulo.setPaddingTop(10);
        titulo.setPaddingBottom(15);
        titulo.setBorder(Rectangle.TOP);
        titulo.setBorderColorRight(Color.BLACK);
        cabecalho.addCell(titulo);
        ClienteSintetico cliente = item.getAvaliacaoFisica().getCliente();
        PdfPTable dadosAlunos = new PdfPTable(2);

        PdfPCell nome = addCell(criarTexto(14, "bold",
                StringUtils.capitalize(cliente.getNome().toLowerCase())), 2, 0,
                Element.ALIGN_LEFT, Element.ALIGN_TOP, null, Rectangle.NO_BORDER);

        PdfPCell idade = addCell(criarTexto(12, "bold",
                "Idade: ".concat(cliente.getIdade().toString())), 0, 0,
                Element.ALIGN_LEFT, Element.ALIGN_TOP, null, Rectangle.NO_BORDER);

        PdfPCell cellFotoAluno = new PdfPCell();
        if (usuario.getEmpresaDefault() != null) {
            String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_PESSOA, cliente.getCodigoPessoa().toString());
            String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
            cellFotoAluno = createImageCell(document, urlFoto);

        }

        dadosAlunos.addCell(nome);

        cellFotoAluno.setBorder(Rectangle.NO_BORDER);
        adicionarPaddingFull(cellFotoAluno, 5);
        cabecalho.addCell(cellFotoAluno);
        PdfPCell dados = new PdfPCell();
        dados.addElement(dadosAlunos);
        dados.setColspan(2);
        cabecalho.addCell(dados);


        document.add(cabecalho);
        document.close();

    }
}
