package br.com.pacto.objeto;

import java.io.Serializable;

/**
 * Created by <PERSON> on 09/02/2017.
 */

public class ColumnModel implements Serializable {

    private String header;
    private String property;
    private String value;
    private boolean link = false;
    private String sort;

    public ColumnModel(String header, String property, String value) {
        this.header = header;
        this.property = property;
        this.value = value;
    }
    public ColumnModel(String header, String property, String value,boolean link,String sort) {
        this.header = header;
        this.property = property;
        this.value = value;
        this.link = link;
        this.sort = sort;
    }

    public String getHeader() {
        return header.replace(" ", "");
//        isso é necessário pois no momento de criar o xml não pode existir espaço pois ele ultiliza esta informação como TAG
    }

    public String getProperty() {
        return property;
    }

    public boolean isLink() {
        return link;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSort() {
        return sort == null || sort == "" ? property : sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}