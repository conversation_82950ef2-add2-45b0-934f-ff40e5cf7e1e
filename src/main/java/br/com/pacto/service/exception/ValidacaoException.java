/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.exception;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ValidacaoException extends ServiceException{
    
    private List<String> mensagens = new ArrayList<String>();
    
    public ValidacaoException(String mensagem) {
        super(mensagem);
        mensagens.add(mensagem);
    }

    public ValidacaoException(List<String> msgs) {
        super("");
        mensagens = msgs;
        
    }
    
    public ValidacaoException(Throwable causa) {
        super(causa);
    }

    public ValidacaoException(String mensagem, Throwable causa) {
        super(mensagem, causa);
    }

    public List<String> getMensagens() {
        return mensagens;
    }

    public void setMensagens(List<String> mensagens) {
        this.mensagens = mensagens;
    }
    
}
