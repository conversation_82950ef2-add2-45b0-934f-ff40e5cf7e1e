package br.com.pacto.service.impl.retiraficha;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.controller.json.retiraFicha.ConfiguracaoRetiraFichaJson;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.retiraFicha.ConfiguracaoRetiraFichaLogService;
import br.com.pacto.service.intf.retiraFicha.ConfiguracaoRetiraFichaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Qualifier(value = "configRetiraFichaService")
public class ConfiguracaoRetiraFichaServiceImpl implements ConfiguracaoRetiraFichaService {

    @Autowired
    private ConfiguracaoRetiraFichaDao configuracaoRetiraFichaDao;

    @Autowired
    private EmpresaService empresaService;

    @Autowired
    private ConfiguracaoRetiraFichaLogService configuracaoRetiraFichaLogService;

    @Override
    public List<ConfiguracaoRetiraFicha> obterTodos(String ctx) throws ServiceException {
        try {
            return configuracaoRetiraFichaDao.findListByAttributes(ctx, null, null, "codigo", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ConfiguracaoRetiraFicha obterPorId(String ctx, Integer id) throws ServiceException {
        if (id == null || id < 1) {
            throw new ServiceException("Id da Configuração Retira Ficha informada não encontrada");
        }

        try {
            return configuracaoRetiraFichaDao.findById(ctx, id);
        } catch (Exception e) {
            throw new ServiceException("Ocorreu um erro ao consultar a configuração do retira ficha",e);
        }
    }

    @Override
    public ConfiguracaoRetiraFicha cadastrarConfiguracaoRetiraFicha(String ctx, ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson, Integer codigoUsuario) throws ServiceException {
        try {
            Empresa empresa = empresaService.obterPorId(ctx, configuracaoRetiraFichaJson.getCodigoEmpresa());

            if (empresa == null) {
                throw new ServiceException("Ocorreu um erro ao consultar a configuração do retira ficha");
            }

            ConfiguracaoRetiraFicha configuracaoRetiraFicha = new ConfiguracaoRetiraFicha(configuracaoRetiraFichaJson, empresa);

            ConfiguracaoRetiraFicha insert = configuracaoRetiraFichaDao.insert(ctx, configuracaoRetiraFicha);
            configuracaoRetiraFichaJson.setCodigo(configuracaoRetiraFicha.getCodigo());
            configuracaoRetiraFichaLogService.cadastrarLogConfiguracaoRetiraFicha(ctx,configuracaoRetiraFichaJson,codigoUsuario);
            return insert;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void alterarConfiguracaoRetiraFicha(String ctx, ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson, Integer codigoUsuario) throws ServiceException {
        try {
            Empresa empresa = empresaService.obterPorId(ctx, configuracaoRetiraFichaJson.getCodigoEmpresa());

            if (empresa == null) {
                throw new ServiceException("Ocorreu um erro ao consultar a configuração do retira ficha");
            }

            try {
                ConfiguracaoRetiraFicha configuracaoRetiraFicha = new ConfiguracaoRetiraFicha(configuracaoRetiraFichaJson, empresa);
                configuracaoRetiraFichaDao.update(ctx, configuracaoRetiraFicha);
                configuracaoRetiraFichaLogService.cadastrarLogConfiguracaoRetiraFicha(ctx, configuracaoRetiraFichaJson, codigoUsuario);
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }

    }

}
