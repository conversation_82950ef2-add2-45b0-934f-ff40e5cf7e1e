/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.agendatotal;

import br.com.pacto.bean.tvgestor.AlunoFavorito;
import br.com.pacto.bean.tvgestor.TVGestorItem;
import br.com.pacto.bean.tvgestor.TVGestorItemFavorito;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.tvgestor.AlunoFavoritoDao;
import br.com.pacto.dao.intf.tvgestor.TVGestorItemDao;
import br.com.pacto.dao.intf.tvgestor.TVGestorItemFavoritoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.agendatotal.TVGestorService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.DiaSemanaAgendaTO;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import br.com.pacto.util.bean.LinhaSemanaTO;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

/**
 *
 * <AUTHOR>
 */
@Service
public class TVGestorServiceImpl implements TVGestorService {

    @Autowired
    public TVGestorItemDao tvgestordao;
    @Autowired
    public AlunoFavoritoDao alunoFavoritoDao;
    @Autowired
    public TVGestorItemFavoritoDao itemFavoritoDao;
    @Autowired
    public AgendaTotalService agendaTotalService;

    public void addFavorito(TVGestorItem item, Date dt, Map<String, List<AlunoFavorito>> mapaFavoritos) {
        List<AlunoFavorito> favoritos = (List<AlunoFavorito>) Uteis.iniciarMapa(mapaFavoritos,
                dt == null ? Uteis.getDataAplicandoFormatacao(item.getDia(), "ddMMyy") : item.getHora().toString(),
                new ArrayList<AlunoFavorito>());
        for (TVGestorItemFavorito al : item.getFavoritos()) {
            favoritos.add(al.getFavorito());
        }
    }

    @Override
    public String montarGrafico(List<TVGestorItem> itens, Date dt,
            Map<String, List<AlunoFavorito>> mapaFavoritos) throws Exception {
        JSONArray array = new JSONArray();
        Map<Date, Map<Integer, TVGestorItem>> mapaGrafico = new HashMap<Date, Map<Integer, TVGestorItem>>();
        for (TVGestorItem item : itens) {
            Map<Integer, TVGestorItem> mapaHora = (Map<Integer, TVGestorItem>) Uteis.iniciarMapa(mapaGrafico, item.getDia(), new HashMap<Integer, TVGestorItem>());
            if (dt == null) {
                TVGestorItem get = mapaHora.get(0);
                if (get == null) {
                    get = new TVGestorItem();
                    get.setAcessos(item.getAcessos());
                    get.setAlunosEsperados(item.getAlunosEsperados());
                    get.setDia(item.getDia());
                    get.setFavoritos(item.getFavoritos());
                    mapaHora.put(0, get);
                } else {
                    get.setAlunosEsperados(get.getAlunosEsperados() + item.getAlunosEsperados());
                    get.setAcessos(get.getAcessos() + item.getAcessos());
                    get.setFavoritos(get.getFavoritos() == null ? new ArrayList<TVGestorItemFavorito>() : get.getFavoritos());
                    get.getFavoritos().addAll(item.getFavoritos());
                }
                if (get.getFavoritos() != null && !get.getFavoritos().isEmpty()) {
                    get.setContemFavoritos(true);
                }
                addFavorito(item, dt, mapaFavoritos);
            } else {
                if (Calendario.igual(dt, item.getDia())) {
                    mapaHora.put(item.getHora(), item);
                    addFavorito(item, dt, mapaFavoritos);
                }

            }

        }
        List<Date> datas = new ArrayList<Date>(mapaGrafico.keySet());
        Collections.sort(datas);
        for (Date data : datas) {
            Map<Integer, TVGestorItem> mapaHoras = mapaGrafico.get(data);
            List<Integer> horas = new ArrayList<Integer>(mapaHoras.keySet());
            Collections.sort(horas);
            for (Integer hr : horas) {
                TVGestorItem objHr = mapaHoras.get(hr);
                JSONObject obj = new JSONObject();
                if (dt == null) {
                    obj.put("date", Uteis.getDataAplicandoFormatacao(data, "dd/MM"));
                    obj.put("d", "t");
                } else {
                    obj.put("date", hr + "hr");
                    obj.put("d", "f");
                }
                obj.put("datef", Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
                obj.put("esperados", objHr.getAlunosEsperados());
                obj.put("acessaram", objHr.getAcessos());
                obj.put("chave", dt == null ? Uteis.getDataAplicandoFormatacao(data, "ddMMyy") : hr.toString());
                if (!objHr.getFavoritos().isEmpty()) {
                    obj.put("bulletClass", "lastBullet");
                }
                array.put(obj);
            }
        }
        return array.toString();
    }

    @Override
    public List<AgendadoTO> obterAcessaram(String ctx, Integer empresa,
            Map<String, AgendaTotalTO> agenda,
            Map<String, List<AgendadoTO>> agendados,
            List<AlunoFavorito> favoritos,
            FiltrosAgendaTO filtrosAgendaTO, Date diaSelecionado) throws Exception {
        Map<Integer, AlunoFavorito> mapaFavoritos = new HashMap<Integer, AlunoFavorito>();
        for (AlunoFavorito aluno : favoritos) {
            mapaFavoritos.put(aluno.getCodigoPessoa(), aluno);
        }
        Map<Date, Map<Integer, List<AgendadoTO>>> mapaAgendadoDia = new HashMap<Date, Map<Integer, List<AgendadoTO>>>();
        for (String key : agendados.keySet()) {
            AgendaTotalTO agendamento = agenda.get(key);
            if (!filtrar(filtrosAgendaTO, agendamento)) {
                continue;
            }
            List<AgendadoTO> lista = agendados.get(key);
            for (AgendadoTO agdd : lista) {
                Map<Integer,  List<AgendadoTO>> mapaCodigos = (Map<Integer,  List<AgendadoTO>>) Uteis.iniciarMapa(mapaAgendadoDia, Calendario.getDataComHoraZerada(agendamento.getStartDate()),
                        new HashMap<Integer,  List<AgendadoTO>>());
                List<AgendadoTO> list = (List<AgendadoTO>) Uteis.iniciarMapa(mapaCodigos, agdd.getCodigoCliente(), new ArrayList<AgendadoTO>());
                list.add(new AgendadoTO(agdd, agendamento));
            }
        }
        JSONArray arr = null;
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        if(diaSelecionado == null){
            arr = integracaoWS.consultarAcessos(ctx, empresa);
        }else{
            arr = integracaoWS.consultarAcessosDia(ctx, empresa, diaSelecionado);
        }
        Map<String, AgendadoTO> mapaAcessaram = new HashMap<String, AgendadoTO>();
        for (int i = 0; i < arr.length(); i++) {
            Date data = Calendario.getDataComHoraZerada(Uteis.getDate(arr.getJSONObject(i).getString("dthrentrada"), "dd/MM/yyyy HH:mm"));
            Map<Integer, List<AgendadoTO>> get = mapaAgendadoDia.get(data);
            if (get != null) {
                try {
                    List<AgendadoTO> list = get.get(arr.getJSONObject(i).getInt("cliente"));
                    Ordenacao.ordenarLista(list,"dataAgendamento");
                    Collections.reverse(list);
                    for (AgendadoTO agendado : list) {
                        AgendadoTO agendadoAcesso = mapaAcessaram.get(agendado.getCodigoCliente()+"_"+Uteis.getDataAplicandoFormatacao(Uteis.getDate(arr.getJSONObject(i).getString("dthrentrada"), "dd/MM/yyyy HH:mm"), "ddMMHHmm"));
                        if(agendadoAcesso == null && agendado.getDataAcesso() == null){
                            if(Calendario.horasMenor(Uteis.getDataAplicandoFormatacao(Uteis.getDate(arr.getJSONObject(i).getString("dthrentrada"), "dd/MM/yyyy HH:mm"),"HH:mm"), Uteis.getDataAplicandoFormatacao(agendado.getFim(), "HH:mm"))){
                                agendado.setDataAcesso(Uteis.getDate(arr.getJSONObject(i).getString("dthrentrada"), "dd/MM/yyyy HH:mm"));
                                gerarDescricaoAcesso(agendado);
                                if (mapaFavoritos.get(agendado.getCodigoPessoa()) != null) {
                                    agendado.setFavorito(true);
                                    favoritoAcessou(ctx, mapaFavoritos.get(agendado.getCodigoPessoa()),
                                            agendado.getDataAcesso());
                                }
                                mapaAcessaram.put(agendado.getCodigoCliente()+"_"+Uteis.getDataAplicandoFormatacao(agendado.getDataAcesso(), "ddMMHHmm"), agendado);
                            }
                        }
//                        else{
//                            Date dataHoraAcesso = Uteis.getDate(arr.getJSONObject(i).getString("dthrentrada"), "dd/MM/yyyy HH:mm");
//                            long minutosEntreDatasAcessoNovo = Uteis.minutosEntreDatas(dataHoraAcesso, agendado.getDataAgendamento());
//                            long minutosEntreDatasAcessoSetado = Uteis.minutosEntreDatas(dataHoraAcesso, agendadoAcesso.getDataAgendamento());
//                            minutosEntreDatasAcessoNovo = minutosEntreDatasAcessoNovo < 0 ? -1 * minutosEntreDatasAcessoNovo : minutosEntreDatasAcessoNovo;
//                            minutosEntreDatasAcessoSetado = minutosEntreDatasAcessoSetado < 0 ? -1 * minutosEntreDatasAcessoSetado : minutosEntreDatasAcessoSetado;
//                            if (minutosEntreDatasAcessoNovo < minutosEntreDatasAcessoSetado) {
//                                agendadoAcesso.setDataAcesso(dataHoraAcesso);
//                                if (mapaFavoritos.get(agendadoAcesso.getCodigoPessoa()) != null) {
//                                    agendado.setFavorito(true);
//                                    favoritoAcessou(ctx, mapaFavoritos.get(agendadoAcesso.getCodigoPessoa()),
//                                            agendadoAcesso.getDataAcesso());
//                                }
//                                mapaAcessaram.put(agendadoAcesso.getCodigoCliente()+"_"+Uteis.getDataAplicandoFormatacao(agendadoAcesso.getDataAcesso(), "ddMMHHmm"), agendadoAcesso);
//                            }
//                        }
                        
                    }
                } catch (Exception e) {
                }
            }
        }
        List<AgendadoTO> ordenarLista = Ordenacao.ordenarLista(new ArrayList<AgendadoTO>(mapaAcessaram.values()), "dataAcesso");
        Collections.reverse(ordenarLista);
        return ordenarLista;
    }
    
    public void gerarDescricaoAcesso(AgendadoTO agendado) {
        String dia = Calendario.igual(agendado.getDataAcesso(), Calendario.hoje()) ? "Hoje, "
                : Calendario.igual(agendado.getDataAcesso(), Uteis.somarDias(Calendario.hoje(), -1)) ? "Ontem, "
                : Uteis.getData(agendado.getDataAcesso(), "dd/MM") + " - ";
        agendado.setHoraAcesso(dia + Uteis.getDataAplicandoFormatacao(agendado.getDataAcesso(), "HH:mm"));
    }

    @Override
    public Map<String, List<AgendadoTO>> montarMapaPresencas(String ctx, Integer empresa) throws Exception {
        Map<String, List<AgendadoTO>> mapaPresencas = new HashMap<String, List<AgendadoTO>>();
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<AgendadoTO> presencas = integracaoWS.consultarPresencas( ctx, empresa);
        for (AgendadoTO pr : presencas) {
            String identificador = pr.getIdAgendamento() + "_" + Uteis.getData(pr.getInicio(), "ddMMyy");
            List<AgendadoTO> lista = mapaPresencas.get(identificador);
            if (lista == null) {
                lista = new ArrayList<AgendadoTO>();
                mapaPresencas.put(identificador, lista);
            }
            lista.add(pr);
        }
        return mapaPresencas;
    }

    public List<Integer> verificarExisteFavorito(Map<Integer, AlunoFavorito> mapaFavoritos, List<AgendadoTO> presencas) {
        List<Integer> lista = new ArrayList<Integer>();
        if (presencas == null) {
            return lista;
        }
        for (AgendadoTO agendado : presencas) {
            if (mapaFavoritos.get(agendado.getCodigoPessoa()) != null) {
                lista.add(agendado.getCodigoPessoa());
            }
        }
        return lista;
    }

    @Override
    public List<TVGestorItem> processarItens(final String ctx, Integer empresa, Date limite,
            List<AgendadoTO> acessaram,
            Map<String, AgendaTotalTO> agenda,
            List<AlunoFavorito> favoritos, boolean reprocessar,
            FiltrosAgendaTO filtros) throws Exception {
        Map<Integer, AlunoFavorito> mapaFavoritos = new HashMap<Integer, AlunoFavorito>();
        for (AlunoFavorito aluno : favoritos) {
            mapaFavoritos.put(aluno.getCodigoPessoa(), aluno);
        }
        List<TVGestorItem> itens = new ArrayList<TVGestorItem>();
        if (reprocessar) {
            deleteItensTVGestor(ctx);
        } else {
            itens = consultarItens(ctx, empresa, limite);
        }
        List<TVGestorItem> itensGravar = new ArrayList<TVGestorItem>();
        Date ultimoDiaProcessado = limite;
        if (!itens.isEmpty()) {
            itens = Ordenacao.ordenarLista(itens, "dia");
            ultimoDiaProcessado = itens.get(itens.size() - 1).getDia();
        }
        //montar o mapa com o identificador da aula e quem acessou
        Map<String, List<AgendadoTO>> acessaramPorTurma = new HashMap<String, List<AgendadoTO>>();
        for (AgendadoTO aluno : acessaram) {
            List<AgendadoTO> acessoTurma = (List<AgendadoTO>) Uteis.iniciarMapa(acessaramPorTurma, aluno.getIdAgendamento(), new ArrayList<AgendadoTO>());
            acessoTurma.add(aluno);
        }
        //montar o mapa dos dias - horas - itens
        Map<Date, Map<Integer, AgendaTotalTO>> mapaGrafico = new HashMap<Date, Map<Integer, AgendaTotalTO>>();
        Date agora = Calendario.getDataComHora(Calendario.hoje(), "23:59");
        for (AgendaTotalTO agendamento : agenda.values()) {
            if (Calendario.maior(agendamento.getStartDate(), agora)
                    || Calendario.menorOuIgual(agendamento.getStartDate(), ultimoDiaProcessado)
                    || !filtrar(filtros, agendamento)) {
                continue;
            }
            Map<Integer, AgendaTotalTO> horas = mapaGrafico.get(Calendario.getDataComHoraZerada(agendamento.getStartDate()));
            if (horas == null) {
                horas = new HashMap<Integer, AgendaTotalTO>();
                mapaGrafico.put(Calendario.getDataComHoraZerada(agendamento.getStartDate()), horas);
            }
            AgendaTotalTO obj = horas.get(Uteis.gethoraHH(agendamento.getStartDate()));
            if (obj == null) {
                obj = new AgendaTotalTO();
                obj.setNrVagasPreenchidas(0);
                obj.setCompareceram(0);
                horas.put(Uteis.gethoraHH(agendamento.getStartDate()), obj);
            }
            obj.setNrVagasPreenchidas(agendamento.getNrVagasPreenchidas() + obj.getNrVagasPreenchidas());
            if (agendamento.getStartDate().before(agora)) {
                List<AgendadoTO> presencas = acessaramPorTurma.get(agendamento.getIdentificador());
                if(presencas != null){
                    for(AgendadoTO ags : presencas){
                        if(!obj.getAcessaramcods().contains(ags.getCodigoCliente())){
                            obj.setCompareceram(obj.getCompareceram() + 1);
                            obj.getAcessaramcods().add(ags.getCodigoCliente());
                        }
                    }
                }
                obj.getFavoritoscods().addAll(verificarExisteFavorito(mapaFavoritos, presencas));
            }
        }

        List<Date> datas = new ArrayList<Date>(mapaGrafico.keySet());
        Collections.sort(datas);
        for (Date data : datas) {
            Map<Integer, AgendaTotalTO> mapaHoras = mapaGrafico.get(data);
            List<Integer> horas = new ArrayList<Integer>(mapaHoras.keySet());
            Collections.sort(horas);
            for (Integer hr : horas) {
                AgendaTotalTO objHr = mapaHoras.get(hr);
                TVGestorItem obj = new TVGestorItem();
                obj.setDia(data);
                obj.setHora(hr);
                obj.setDataProcessamento(Calendario.hoje());
                obj.setEmpresa(empresa);
                obj.setAlunosEsperados(objHr.getNrVagasPreenchidas());
                obj.setAcessos(objHr.getCompareceram());
                obj.setFavoritoscods(objHr.getFavoritoscods());
                itensGravar.add(obj);
            }
        }
        gravarItens(ctx, itensGravar, mapaFavoritos);
        itens.addAll(itensGravar);
        return Ordenacao.ordenarLista(itens, "dia");
    }

    public void deleteItensTVGestor(String key) throws Exception {
        itemFavoritoDao.executeNativeSQL(key, "DELETE FROM tvgestoritemfavorito;");
        tvgestordao.executeNativeSQL(key, "DELETE FROM tvgestoritem;");
    }

    public List<TVGestorItem> consultarItens(final String ctx, final Integer empresa, final Date limite) throws Exception {
        String q = "SELECT obj FROM TVGestorItem obj WHERE obj.dia >= :limite"
                + " AND obj.empresa = :empresa";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("empresa", empresa);
        params.put("limite", limite);
        return tvgestordao.findByParam(ctx, q, params);
    }

    public void gravarItens(String ctx, List<TVGestorItem> itens, Map<Integer, AlunoFavorito> mapaFavoritos) throws Exception {
        for (TVGestorItem i : itens) {
            if (!Calendario.igual(Calendario.hoje(), i.getDia())) {
                List<Integer> favoritoscods = i.getFavoritoscods();
                i = tvgestordao.insert(ctx, i);
                i.setFavoritos(new ArrayList<TVGestorItemFavorito>());
                for (Integer codigo : favoritoscods) {
                    TVGestorItemFavorito tvif = new TVGestorItemFavorito();
                    tvif.setItem(i);
                    tvif.setFavorito(mapaFavoritos.get(codigo));
                    i.getFavoritos().add(itemFavoritoDao.insert(ctx, tvif));
                }
            }
        }
    }

    @Override
    public AlunoFavorito gravarFavorito(final String ctx, AlunoFavorito af) throws ServiceException {
        try {
            return alunoFavoritoDao.insert(ctx, af);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void removerFavorito(final String ctx, AlunoFavorito af) throws ServiceException {
        try {
            itemFavoritoDao.deleteComParam(ctx, new String[]{"favorito.codigo"}, new Object[]{af.getCodigo()});
            alunoFavoritoDao.delete(ctx, af);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AlunoFavorito> obterAlunosFavoritos(final String ctx,
            final Usuario usuario) throws ServiceException {
        try {
            String q = "SELECT obj FROM AlunoFavorito obj WHERE obj.usuario.codigo = :usuario";
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("usuario", usuario.getCodigo());
            return alunoFavoritoDao.findByParam(ctx, q, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void favoritoAcessou(String ctx, AlunoFavorito favorito, Date acesso) throws ServiceException {
        try {
            if (favorito.getAcesso() == null || !Calendario.igual(acesso, favorito.getAcesso())) {
                favorito.setAcesso(acesso);
                favorito.setOk(Boolean.FALSE);
                alunoFavoritoDao.update(ctx, favorito);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean filtrar(FiltrosAgendaTO filtros, AgendaTotalTO agendamento) {
//        System.out.println(agendamento.getTitulo() + " - "+agendamento.getCodigoResponsavel());
        return filtros == null
                || ((filtros.getAmbientesSelecionados().isEmpty() || filtros.getAmbientesSelecionados().contains(agendamento.getCodigoLocal().toString()))
                && (filtros.getTiposSelecionadosCods().isEmpty() || filtros.getTiposSelecionadosCods().contains(agendamento.getCodigotipo().toString()))
                && (filtros.getProfessoresSelecionadosCods().isEmpty() || filtros.getProfessoresSelecionadosCods().contains(agendamento.getCodigoResponsavel().toString())));

    }
    
    @Override
    public Integer nrTotalEsperadosDia(List<LinhaSemanaTO> listaSemana, Date dia){
        Integer nr = 0;
        for(LinhaSemanaTO l : listaSemana){
            for(DiaSemanaAgendaTO d : l.getDias()){
                if(Calendario.igual(d.getDia(), dia)){
                    nr += d.getNrAlunos();
                }
            }
        }
        return nr;
    }

    @Override
    public List<LinhaSemanaTO> montarSemanaTVGestor(String ctx, Date inicio, Date fim,
            Integer empresa,
            List<Date> dias,
            List<Integer> horas,
            Map<String, AgendaTotalTO> agenda,
            Map<String, List<AgendadoTO>> mapaAgendados,
            FiltrosAgendaTO filtros,
            Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram) throws Exception {

        Map<Integer, LinhaSemanaTO> mapa = new HashMap<Integer, LinhaSemanaTO>();
        Map<Integer, Map<Integer, DiaSemanaAgendaTO>> mapaHoraDia = new HashMap<Integer, Map<Integer, DiaSemanaAgendaTO>>();
        for (Integer hora : horas) {
            mapa.put(hora, new LinhaSemanaTO(hora));
            Map<Integer, DiaSemanaAgendaTO> mapadias = new HashMap<Integer, DiaSemanaAgendaTO>();
            for (Date dia : dias) {
                mapadias.put(Uteis.getDiaMesData(dia), new DiaSemanaAgendaTO(dia, hora));
            }
            mapaHoraDia.put(hora, mapadias);
        }

        for (AgendaTotalTO agendamento : agenda.values()) {
            if ((!Calendario.entre(agendamento.getStartDate(), inicio, fim)
                    && !Calendario.igual(agendamento.getStartDate(), inicio)
                    && !Calendario.igual(agendamento.getStartDate(), fim))
                    || !filtrar(filtros, agendamento)) {
                continue;
            }
            Map<Integer, DiaSemanaAgendaTO> linha = mapaHoraDia.get(Uteis.gethoraHH(agendamento.getStartDate()));
            Integer dia = Uteis.getDiaMesData(agendamento.getStartDate());
            DiaSemanaAgendaTO diaSemanaTO = linha.get(dia);
            List<AgendadoTO> agendados = mapaAgendados.get(agendamento.getIdentificador());
            for (AgendadoTO agendado : agendados) {
                if(!agendado.isDesmarcado()){
                    agendado.setAgendamento(agendamento);
                    Map<Integer, Map<Integer, AgendadoTO>> mapaHora = mapaAcessaram.get(Calendario.getDataComHoraZerada(agendamento.getStartDate()));
                    if(mapaHora != null && mapaHora.get(Uteis.gethoraHH(agendamento.getStartDate())) != null
                            && mapaHora.get(Uteis.gethoraHH(agendamento.getStartDate())).get(agendado.getCodigoCliente()) != null){
                        diaSemanaTO.setNrAlunosAcessaram(diaSemanaTO.getNrAlunosAcessaram()+ 1);
                    }
                    diaSemanaTO.setNrAlunos(diaSemanaTO.getNrAlunos() + 1);
                    diaSemanaTO.getAgendados().add(agendado);
                }
                
            }
        }
        for (Integer key : mapaHoraDia.keySet()) {
            LinhaSemanaTO linha = mapa.get(key);
            Map<Integer, DiaSemanaAgendaTO> mapahora = mapaHoraDia.get(key);
            linha.getDias().addAll(Ordenacao.ordenarLista(new ArrayList(mapahora.values()), "dia"));
        }
        List<LinhaSemanaTO> lista = new ArrayList<LinhaSemanaTO>(mapa.values());
        return Ordenacao.ordenarLista(lista, "hora");
    }

    @Override
    public List<AgendadoTO> detalharGrafico(final String chave, final boolean acessaram, Integer hora,
            Date dia, final Integer empresa, FiltrosAgendaTO filtrosAgendaTO,List<AlunoFavorito> favoritos,
            Map<String, AgendaTotalTO> agendamentosDia,
            Map<String, List<AgendadoTO>> mapaAgendados) throws ServiceException {
        try {
            List<String> alunosStr = new ArrayList<>();
            List<AgendadoTO> alunos = new ArrayList<AgendadoTO>();
            
            List<AgendadoTO> listaAcessaram = obterAcessaram(chave, empresa, agendamentosDia, mapaAgendados,favoritos, filtrosAgendaTO, dia);
            for (String k : mapaAgendados.keySet()) {
                AgendaTotalTO agendamento = agendamentosDia.get(k);
                if ((hora != null
                        && agendamentosDia.get(k) != null
                        && !Uteis.gethoraHH(agendamentosDia.get(k).getDateInicio()).equals(hora))
                            || (!Calendario.igual(agendamento.getStartDate(), dia))){
                    continue;
                }
                List<AgendadoTO> verificados = verificarAcessaram(listaAcessaram, dia, mapaAgendados.get(k), Uteis.gethoraHH(agendamentosDia.get(k).getDateInicio()));
                for (AgendadoTO a : verificados) {
                    String tk = a.getCodigoCliente() + "_" + (a.getHoraAcesso() == null ? "" : a.getHoraAcesso());
                    if((acessaram 
                            && (UteisValidacao.emptyString(a.getHoraAcesso()) || alunosStr.contains(tk)))
                            || a.isDesmarcado()
                    ){
                        continue;
                    }
                    alunos.add(a);
                    alunosStr.add(tk);
                }
            }
            return alunos;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
    
    
    private List<AgendadoTO> verificarAcessaram(List<AgendadoTO> listaAcessaram, Date dia, List<AgendadoTO> alunos, Integer hora){
        Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram = new HashMap<Date, Map<Integer, Map<Integer, AgendadoTO>>>();
        for (AgendadoTO ag : listaAcessaram) {
            Map<Integer, Map<Integer, AgendadoTO>> horaAgendados = (Map<Integer, Map<Integer, AgendadoTO>>) Uteis.iniciarMapa(mapaAcessaram, Calendario.getDataComHoraZerada(ag.getAgendamento().getStartDate()), new HashMap<Integer, Map<Integer, AgendadoTO>>());
            Map<Integer, AgendadoTO> agendados = (Map<Integer, AgendadoTO>) Uteis.iniciarMapa(horaAgendados, Uteis.gethoraHH(ag.getAgendamento().getStartDate()), new HashMap<Integer, AgendadoTO>());
            agendados.put(ag.getCodigoCliente(), ag);
        }
        Map<Integer, Map<Integer, AgendadoTO>> horaAgendados = mapaAcessaram.get(Calendario.getDataComHoraZerada(dia));
        if (horaAgendados == null) {
            return alunos;
        }
        Map<Integer, AgendadoTO> agendados = horaAgendados.get(hora);
        if (agendados == null) {
            return alunos;
        }
        for (AgendadoTO ag : alunos) {
            AgendadoTO agendado = agendados.get(ag.getCodigoCliente());
            if (agendado == null) {
                continue;
            }
            ag.setHoraAcesso(Uteis.getDataAplicandoFormatacao(agendado.getDataAcesso(), "HH:mm"));
        }
        return alunos;
    }
}
