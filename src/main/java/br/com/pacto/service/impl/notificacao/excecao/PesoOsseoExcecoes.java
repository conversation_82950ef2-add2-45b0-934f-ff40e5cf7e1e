package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by paulo
 */
public enum PesoOsseoExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_PESO_OSSEO("erro_buscar_peso_Osseo", "Erro ao buscar peso osseo")
    ;

    private String chave;
    private String descricao;

    PesoOsseoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
