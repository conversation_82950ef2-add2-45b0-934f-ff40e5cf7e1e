package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON> Alcides on 06/06/2017.
 */
public class ProtocoloPenroe {


    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){

//        public static Double massaMagraCorporalPenroe(double peso, double abdome, double punho, Double pesoGordura, char sexo) {


//        Double percGordura = percentualGorduraFaulkner(avaliacao.getTriceps(), avaliacao.getSubescapular(), avaliacao.getSupraIliaca(), avaliacao.getAbdominal());
//        Double massaGorda = pesoGorduraFaulkner(avaliacao.getPeso(), percGordura);
//        Double massaMagra = massaCorporalMagraFaulkner(avaliacao.getPeso(), massaGorda);
//
//
//        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
//        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
//        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));


        return avaliacao;
    }

    /**
     * Método para calcular o Percentual de Gordura Penroe
     *
     * @param quadril            - Dado equivalente ao quadril
     * @param estatura           - Dado equivalente a estatura
     * @param abdome             - Dado equivalente ao abdome
     * @param peso               - Dado equivalente ao peso
     * @param massaMagraCorporal - Dado equivalente a massa magra corporal
     * @param sexo               - Dado equivalente ao sexo
     * @return Percentual de Gordura
     */
    public static Double percentualGorduraPenroe(double quadril, double estatura, double abdome, double peso,
            Double massaMagraCorporal, char sexo) {

        // Se for feminino e se quadril, estatura e abdome forem maiores que 0
        if (sexo == 'F' && quadril > 0 && estatura > 0 && abdome > 0) {
            return (0.55 * quadril) - (0.24 * estatura) + (0.28 * abdome) - 8.43;
        }
        // se for masculino e se peso e quadril forem maiores que 0
        else if (sexo == 'M' && peso > 0 && quadril > 0) {
            return ((peso - massaMagraCorporal) / peso) * 100;
        }
        return null;
    }

    /**
     * Método para calcular o peso gordura Penroe
     *
     * @param peso              - Dado equivalente ao peso
     * @param percentualGordura - Dado equivalente ao percentual de gordura
     * @return peso gordura
     */
    public static Double pesoGorduraPenroe(double peso, Double percentualGordura) {
        // se peso for maior que 0 e percentual de gordura tiver alguma coisa
        if (peso > 0 && percentualGordura != null) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método que calcula a massa corporal magra <b>Penroe</b><br>
     * através do peso, abdome, punho e o peso gordura Penroe
     *
     * @param peso        - Dado equivalente ao peso
     * @param abdome      - Dado equivalente ao abdome
     * @param punho       - Dado equivalente ao punho
     * @param pesoGordura - Dado equivalente ao peso de gordura
     * @param sexo        - Dado equivalente ao sexo
     * @return massa corporal magra
     */
    public static Double massaMagraCorporalPenroe(double peso, double abdome, double punho, Double pesoGordura, char sexo) {
        // Se for feminino e se peso for maior que 0 e peso gordura tiver alguma coisa
        if (sexo == 'F' && peso > 0 && pesoGordura != null) {
            return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
        }
        // Se for masculino e se abdome, punho e peso forem maiores que 0
        else if (sexo == 'M' && abdome > 0 && punho > 0 && peso > 0) {
            return (41.955 + (1.038786 * peso)) - (0.82816 * (abdome - punho));
        }
        return 0.0;
    }
}
