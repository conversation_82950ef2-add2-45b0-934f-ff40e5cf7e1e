/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.configuracoes;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.jpa.dao.intf.VersaoDao;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.bean.avaliacao.DobrasEnum;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.*;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gogood.ConfigGoGood;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.ConfiguracaoIntegracoesDTO;
import br.com.pacto.controller.json.base.ConfiguracoesIaDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.configuracao.ConfigJSON;
import br.com.pacto.controller.json.configuracao.ConfiguracaoSistemaJSON;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.controller.json.mgb.ConfigMgb;
import br.com.pacto.controller.json.mqv.MqvDTO;
import br.com.pacto.controller.json.totalpass.TotalPassDTO;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.cliente.ClientePesquisaDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoDobrasDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoPerimetroDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaUsuarioDao;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.perfil.permissao.PermissaoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.ConfiguracaoSistemaExcecoes;
import br.com.pacto.service.impl.programa.ProgramaTreinoServiceImpl;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gogood.ConfigGoGoodService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Service
public class ConfiguracaoSistemaServiceImpl implements ConfiguracaoSistemaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoSistemaDao;
    @Autowired
    private ConfiguracaoSistemaUsuarioDao configuracaoSistemaUsuarioDao;
    @Autowired
    private ConfiguracaoPerimetroDao cfgPerimetroDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private VersaoDao versaoDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConfiguracaoDobrasDao cfgDobrasDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ClientePesquisaDao clientePesquisaDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private GymPassBookingService gymPassBookingService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private PermissaoDao permissaoDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AtividadeService atividadeService;

    @Autowired
    private ProgramaTreinoDao programaDao;
    @Autowired
    private ProfessorSinteticoDao professorsinteticoDao;
    private static ConcurrentHashMap<String, ConfiguracaoSistemaJSON> CONFIG_CACHE = new ConcurrentHashMap<>();
    private static final long TEMPO_CACHE_HORAS = 1 /*HORAS*/ * 60 /*MINUTOS*/ * 60 /*SEGUNDOS*/ * 1000 /*MILISEGUNDOS*/;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ConfiguracaoSistemaDao getConfiguracaoSistemaDao() {
        return this.configuracaoSistemaDao;
    }

    public void setConfiguracaoSistemaDao(ConfiguracaoSistemaDao configuracaoSistemaDao) {
        this.configuracaoSistemaDao = configuracaoSistemaDao;
    }

    public ConfiguracaoSistema alterar(final String ctx, ConfiguracaoSistema object) throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    public void refresh(final String ctx, ConfiguracaoSistema object) throws ServiceException {
        try {
            getConfiguracaoSistemaDao().refresh(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ConfiguracaoSistema object) throws ServiceException {
        try {
            getConfiguracaoSistemaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoSistema inserir(final String ctx, ConfiguracaoSistema object) throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoSistema obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoSistema obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoSistema> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoSistema> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoSistema> obterTodos(final String ctx) throws ServiceException {
        try {
            return getConfiguracaoSistemaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoSistema consultarPorTipo(String ctx, ConfiguracoesEnum cfg) throws ServiceException {
        try {
            ConfiguracaoSistema conf = null;
            try (ResultSet rs = getConfiguracaoSistemaDao().createStatement(ctx, "SELECT * FROM configuracaosistema WHERE configuracao = '" + cfg.name() + "'")) {
                if (rs.next()) {
                    conf = new ConfiguracaoSistema();
                    conf.setCodigo(rs.getInt("codigo"));
                    conf.setConfiguracao(ConfiguracoesEnum.getFromName(rs.getString("configuracao")));
                    conf.setTipoNotificacao(rs.getString("tiponotificacao") != null ? TipoNotificacaoEnum.obterPorID(rs.getInt("tiponotificacao")) : null);
                    conf.setValor(rs.getString("valor"));
                }
            }
            if (conf == null) {
                return povoar(ctx, cfg);
            } else {
                return conf;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }
    
    public List<ConfiguracaoSistema> consultarPorTipos(String ctx, ConfiguracoesEnum... cfgs) throws ServiceException {
        try {
            StringBuilder where = new StringBuilder("where configuracao in :listCfg");
            Map<String, Object> p = new HashMap();
            p.put("listCfg", Arrays.asList(cfgs));
            List<ConfiguracaoSistema> lista = getConfiguracaoSistemaDao().findByParam(ctx, 
                    where, p);
            return lista;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String consultarVersaoBd(String ctx) throws ServiceException {
        try {
            String lista = "";
            try (ResultSet rs = versaoDao.createStatement(ctx, "SELECT codigo, numero FROM versao")) {
                while (rs.next()) {
                    lista += "codigo registro: " + rs.getInt("codigo") + " - versao: " + rs.getString("numero") + "; ";
                }
            }
            return lista;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ConfigsEmail obterConfiguracoes(String ctx) throws ServiceException {
        try {
            return obterConfiguracoes(obterTodos(ctx));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ConfigsEmail obterConfiguracoes(List<ConfiguracaoSistema> configuracoes) throws ServiceException {
        try {
            ConfigsEmail cfgEmail = new ConfigsEmail();
            for (ConfiguracaoSistema cfg : configuracoes) {
                switch (cfg.getConfiguracao()) {
                    case LOGIN:
                        cfgEmail.setLogin(cfg.getValor());
                        break;
                    case SENHA:
                        cfgEmail.setSenha(cfg.getValor());
                        break;
                    case REMETENTE:
                        cfgEmail.setRemetente(cfg.getValor());
                        break;
                    case EMAIL_PADRAO:
                        cfgEmail.setEmail_padrao(cfg.getValor());
                        break;
                    case MAIL_SERVER:
                        cfgEmail.setMail_server(cfg.getValor());
                        break;
                    case CONEXAO_SEGURA:
                        cfgEmail.setConexao_segura(cfg.getValorAsBoolean());
                        break;
                    case INICIAR_TLS:
                        cfgEmail.setIniciarTLS(cfg.getValorAsBoolean());
                        break;
                }
            }
            return cfgEmail;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private ConfiguracaoSistema povoar(final String ctx, ConfiguracoesEnum cfg) throws Exception {
       return povoar(ctx, cfg, false);
    }

    private ConfiguracaoSistema povoar(final String ctx, ConfiguracoesEnum cfg, boolean usarAulaCheia) throws Exception {
        ConfiguracaoSistema cfgInsert = new ConfiguracaoSistema(cfg);
        tratarConfigsAulaCheia(usarAulaCheia, cfgInsert);
        cfgInsert = getConfiguracaoSistemaDao().insert(ctx, cfgInsert);
        return cfgInsert;
    }

    private ConfiguracaoSistema povoar(final String ctx, TipoNotificacaoEnum tipo) throws Exception {
        ConfiguracaoSistema cfgInsert = new ConfiguracaoSistema(tipo);
        cfgInsert = getConfiguracaoSistemaDao().insert(ctx, cfgInsert);
        return cfgInsert;
    }

    private boolean usaAulaCheia(String ctx) throws Exception {
        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfgUsarSalaCheia = css.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_SALA_CHEIA);
        refresh(ctx, cfgUsarSalaCheia);
        return cfgUsarSalaCheia != null && cfgUsarSalaCheia.getValorAsBoolean();
    }

    @Override
    public List<ConfiguracaoSistema> consultarPovoando(String ctx) throws ServiceException {
        List<ConfiguracaoSistema> lista = new ArrayList<ConfiguracaoSistema>();
        try {
            boolean usarAulaCheia = usaAulaCheia(ctx);
            for (ConfiguracoesEnum cfg : ConfiguracoesEnum.values()) {
                if (cfg.equals(ConfiguracoesEnum.NOTIFICACAO)) {
                    for (TipoNotificacaoEnum tpNot : TipoNotificacaoEnum.getAConfigurar()) {
                        lista.add(consultarPorTipoNotifacao(ctx, tpNot));
                    }
                } else {
                    ConfiguracaoSistema cfgObj = getConfiguracaoSistemaDao().findObjectByAttribute(ctx, "configuracao", cfg);
                    if (cfgObj == null) {
                        cfgObj = povoar(ctx, cfg, usarAulaCheia);
                    }
                    if (cfgObj.isApresentar()){
                        refresh(ctx, cfgObj);
                        lista.add(cfgObj);
                    }
                }
            }
            return lista;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void tratarConfigsAulaCheia(boolean usarAulaCheia, ConfiguracaoSistema cfg) {
        if (usarAulaCheia && (cfg.getConfiguracao().equals(ConfiguracoesEnum.MODULO_AULAS) ||
                              cfg.getConfiguracao().equals(ConfiguracoesEnum.MODULO_AULAS_ABA_TURMAS) ||
                              cfg.getConfiguracao().equals(ConfiguracoesEnum.MODULO_AULAS_ABA_AULAS_COLETIVAS))) {
            cfg.setValor("true");
        }
    }

    @Override
    public ConfiguracaoSistema consultarPorTipoNotifacao(final String ctx, final TipoNotificacaoEnum tipoNotificacao) throws ServiceException {
        try {
            ConfiguracaoSistema cfgObj = getConfiguracaoSistemaDao().findObjectByAttributes(ctx,
                    new String[]{"configuracao", "tipoNotificacao"},
                    new Object[]{ConfiguracoesEnum.NOTIFICACAO, tipoNotificacao}, null);
            if (cfgObj == null) {
                return povoar(ctx, tipoNotificacao);
            }
            refresh(ctx, cfgObj);
            return cfgObj;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public boolean notificacaoConfigurada(final String ctx, final TipoNotificacaoEnum tipoNotificacao) throws ServiceException {
        try {
            ConfiguracaoSistema cfgObj = getConfiguracaoSistemaDao().findObjectByAttributes(ctx,
                    new String[]{"configuracao", "tipoNotificacao"},
                    new Object[]{ConfiguracoesEnum.NOTIFICACAO, tipoNotificacao}, null);
            if (cfgObj == null) {
                cfgObj = povoar(ctx, tipoNotificacao);
            }
            return cfgObj.getValorAsBoolean();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
 
    @Override
    public ConfiguracaoSistemaUsuario gravarConfiguracaoUsuario(final String ctx, final ConfiguracaoSistemaUsuario config) throws ServiceException{
        try {
            if(UteisValidacao.emptyNumber(config.getCodigo())){
                return configuracaoSistemaUsuarioDao.insert(ctx, config);
            }else{
                return configuracaoSistemaUsuarioDao.update(ctx, config);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public ConfiguracaoSistemaUsuario obterConfiguracaoUsuario(final String ctx, final Usuario usuario, 
            ConfiguracoesUsuarioEnum config) throws ServiceException{
        try {
             ConfiguracaoSistemaUsuario cfg = configuracaoSistemaUsuarioDao.findObjectByAttributes(ctx,
                    new String[]{"usuario.codigo", "configuracao"},
                    new Object[]{usuario.getCodigo(), config}, null);
             if(cfg == null){
                 cfg = new ConfiguracaoSistemaUsuario();
                 cfg.setConfiguracao(config);
                 cfg.setValor("");
                 cfg.setUsuario_codigo(usuario.getCodigo());
                 return cfg;
             }
             return cfg;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Date obterDataUltimaAtualizacao(String ctx){
        try {
            ConfiguracaoSistema config = consultarPorTipo(ctx, ConfiguracoesEnum.DATA_ULTIMA_ATUALIZACAO_ALUNO_ZW);
            if(UteisValidacao.emptyString(config.getValor())){
                return null;
            }
            return Uteis.getDate(config.getValor(), "yyyy-MM-dd HH:mm");
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void atualizarDataUltimaAtualizacao(String ctx) throws Exception {
        ConfiguracaoSistema config = consultarPorTipo(ctx, ConfiguracoesEnum.DATA_ULTIMA_ATUALIZACAO_ALUNO_ZW);
        config.setValor(Uteis.getDataAplicandoFormatacao(Calendario.hoje(),"yyyy-MM-dd HH:mm"));
        alterar(ctx, config);
    }

    @Override
    public ConfigsEmail obterConfiguracoesPadrao() throws ServiceException {
        try {
            ConfigsEmail cfgEmail = new ConfigsEmail();
            cfgEmail.setLogin(Aplicacao.getProp(Aplicacao.smtpLoginRobo));
            cfgEmail.setSenha(Aplicacao.getProp(Aplicacao.smtpSenhaRobo));
            cfgEmail.setRemetente("");
            cfgEmail.setMail_server(Aplicacao.getProp(Aplicacao.smtpServerRobo));
            cfgEmail.setEmail_padrao(Aplicacao.getProp(Aplicacao.smtpEmailNoReply));
            cfgEmail.setConexao_segura(Boolean.getBoolean(Aplicacao.getProp(Aplicacao.smtpConexaoSeguraRobo)));
            return cfgEmail;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ConfiguracaoPerimetro> obterCfgsPerimetro(final String ctx) throws Exception{
        List<ConfiguracaoPerimetro> all = cfgPerimetroDao.findAll(ctx);
        if(UteisValidacao.emptyList(all)){
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.pescoco", 0, PerimetriaEnum.PESCOCO, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.ombro", 1, PerimetriaEnum.OMBRO, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.torax_bustoRelaxado", 2, PerimetriaEnum.TORAX, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.bracoRelaxado", 3, PerimetriaEnum.BRACO_RELAXADO_ESQ, PerimetriaEnum.BRACO_RELAXADO_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.bracoContraido", 4, PerimetriaEnum.BRACO_CONTRAIDO_ESQ, PerimetriaEnum.BRACO_CONTRAIDO_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.anteBraco", 5, PerimetriaEnum.ANTEBRACO_ESQ, PerimetriaEnum.ANTEBRACO_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.cintura", 6, PerimetriaEnum.CINTURA, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.circunferencia", 7, PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.quadril", 8, PerimetriaEnum.QUADRIL, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.gluteo", 9, PerimetriaEnum.GLUTEO, null)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.coxa_proximal", 10, PerimetriaEnum.COXA_PROXIMAL_ESQ, PerimetriaEnum.COXA_PROXIMAL_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.coxaMedia", 11, PerimetriaEnum.COXA_MEDIAL_ESQ, PerimetriaEnum.COXA_MEDIAL_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.coxa_distal", 12, PerimetriaEnum.COXA_DISTAL_ESQ, PerimetriaEnum.COXA_DISTAL_DIR)));
            all.add(cfgPerimetroDao.insert(ctx, new ConfiguracaoPerimetro("cadastros.aluno.panturrilha", 13, PerimetriaEnum.PANTURRILHA_ESQ, PerimetriaEnum.PANTURRILHA_DIR)));
        }
        return Ordenacao.ordenarLista(all, "ordem");
    }

    public void gravarNovaOrdemPerimetros(final String ctx, List<ConfiguracaoPerimetro> cfgs) throws Exception {
        for (ConfiguracaoPerimetro cfg : cfgs) {
            cfg.setOrdem(cfgs.indexOf(cfg));
            cfgPerimetroDao.update(ctx, cfg);
        }
    }

    public String obterTokenIntegracaoOlympia(final String ctx) {
        try {

            String mascaraData = "dd/MM/yyyy HH:mm:ss";

            String token;
            ConfiguracaoSistema cfgUsuario = consultarPorTipo(ctx, ConfiguracoesEnum.USUARIO_SISTEMA_OLYMPIA);
            ConfiguracaoSistema cfgSenha = consultarPorTipo(ctx, ConfiguracoesEnum.SENHA_SISTEMA_OLYMPIA);
            ConfiguracaoSistema cfgData = consultarPorTipo(ctx, ConfiguracoesEnum.DATA_GERACAO_TOKEN_SISTEMA_OLYMPIA);
            ConfiguracaoSistema cfgToken = consultarPorTipo(ctx, ConfiguracoesEnum.TOKEN_SISTEMA_OLYMPIA);


            Date dataExp = null;
            if (!UteisValidacao.emptyString(cfgData.getValor())) {
                dataExp = Uteis.getDate(cfgData.getValor(), mascaraData);
                dataExp = Uteis.somarCampoData(dataExp, Calendar.HOUR, 7);
            }

            if (dataExp != null && Calendario.menorComHora(Calendario.hoje(), dataExp)) {
                //não está vencido retorna o token
                return cfgToken.getValor();
            } else {

                String url = "http://www.sistemafieg.org.br/auth/realms/SistemaFIEG-Services/protocol/openid-connect/token";

                Map<String, String> paramsHeader = new HashMap<String, String>();
                paramsHeader.put("Content-Type", "application/x-www-form-urlencoded");

                Map<String, String> paramsBody = new HashMap<String, String>();
                paramsBody.put("grant_type", "password");
                paramsBody.put("client_id", "olympia-service-app");
                paramsBody.put("response_type", "token");
                paramsBody.put("username", cfgUsuario.getValor());
                paramsBody.put("password", cfgSenha.getValor());
                paramsBody.put("client_secret", "39811715-c877-42a1-bd1d-aab689679293");


                String retorno = ExecuteRequestHttpService.executeHttpRequestGenerico(url, paramsHeader, paramsBody, ExecuteRequestHttpService.METODO_POST, "UTF-8", "UTF-8");
                JSONObject jsonObject = new JSONObject(retorno);

                token = jsonObject.getString("access_token");
                configuracaoSistemaDao.executeNative(ctx, "update configuracaosistema set valor = '" + token + "' where configuracao = '" + ConfiguracoesEnum.TOKEN_SISTEMA_OLYMPIA.name() + "'");
                cfgData.setValor(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), mascaraData));
                alterar(ctx, cfgData);
                return token;
            }
        } catch (Exception e) {
            return "";
        }
    }

    public void atualizarQuantidadeAulasExperimentais(final String ctx, Integer aulasExperimentaisAntesAlteracao, Integer valorNovo) throws Exception {
        String sql = "UPDATE clientesintetico \n" +
                "SET nraulasexperimentais = \n" +
                "(CASE \n" +
                "   WHEN ((nraulasexperimentais - " + aulasExperimentaisAntesAlteracao + ") + " + valorNovo + ") < 0 THEN 0 \n" +
                "   ELSE ((nraulasexperimentais - " + aulasExperimentaisAntesAlteracao + ") + " + valorNovo + ") \n" +
                "END);";

        configuracaoSistemaDao.executeNative(ctx, sql);
    }

    public void atualizarQuantidadeAulasExperimentaisTodos(final String ctx, Integer valorNovo) throws Exception {
        String sql = "UPDATE clientesintetico \n" +
                "SET nraulasexperimentais = " + valorNovo + ";";

        configuracaoSistemaDao.executeNative(ctx, sql);
    }

    public void incrementarVersaoProgramasSimples(String key) throws Exception {
        configuracaoSistemaDao.executeNativeSQL(key, "update programatreino set versao = (versao + 1);");
    }

    public void incrementarVersaoProgramasSets(String key) throws Exception {
        List<ProgramaTreinoFicha> programas = programaTreinoFichaDao.findByParam(key, "SELECT DISTINCT obj.ficha.programas FROM AtividadeFicha obj WHERE obj.metodoExecucao IN (5,6) ", new HashMap<String, Object>());
        ProgramaTreinoService programaTreinoService = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
        List<Integer> codigosProgramasAtualizar = new ArrayList<Integer>();
        List<ProgramaTreinoFicha> programasAtualizar = new ArrayList<ProgramaTreinoFicha>();
        for (ProgramaTreinoFicha ptf : programas) {
            if (!codigosProgramasAtualizar.contains(ptf.getPrograma().getCodigo())) {
                if (null != ptf.getPrograma().getDataTerminoPrevisto()
                    && Calendario.maiorOuIgual(ptf.getPrograma().getDataTerminoPrevisto(), Calendario.hoje())) {
                    codigosProgramasAtualizar.add(ptf.getPrograma().getCodigo());
                    programasAtualizar.add(ptf);
                }
            }
        }

        if (!codigosProgramasAtualizar.isEmpty()) {
            programaTreinoService.atualizarVersaoProgramas(key, codigosProgramasAtualizar);
        }

        if (!programasAtualizar.isEmpty()) {
            for (ProgramaTreinoFicha ptf : programasAtualizar) {
                programaTreinoService.notificarOuvintes(key, ptf.getPrograma());
            }
        }

    }

    public Object configsDTO(Class classe, Object obj) throws ServiceException {
        return configDTO(null, classe, obj);
    }

    public Object configsDTO(String chave, Class classe, Object obj) throws ServiceException {
        return configDTO(chave, classe, obj);
    }

    public Object configDTO(String chave, Class classe, Object obj) throws ServiceException {
        try {
            String ctx = "";
            if (isBlank(chave)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            } else {
                ctx = chave;
            }
            List<ConfiguracaoSistema> todas = obterTodosNativo(ctx);
            Map<String, String> mapa = new HashMap<>();
            for (ConfiguracaoSistema config : todas) {
                if (config.getTipoNotificacao() == null) {
                    mapa.put(config.getNome(), config.getValor());
                } else {
                    mapa.put(config.getNomeNotificacao(), config.getValor());
                }
            }
            List<String> listAttributes = UtilReflection.getListAttributes(classe);
            for (String a : listAttributes) {
                if (a.equals("permitir_visualizar_lei_parq")) {
                    String currentVal = (String) UtilReflection.getValor(obj, a);
                    if (currentVal == null || currentVal.equalsIgnoreCase("false")) {
                        List<Empresa> empresas = empresaService.obterTodos(ctx);
                        boolean permitir = false;
                        for (Empresa emp : empresas) {
                            if (emp.getCodZW() != null) {
                                String queryEmp = String.format("SELECT * FROM EMPRESA WHERE codigo = %d", emp.getCodZW());
                                try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                                     ResultSet rsEmp = ConexaoZWServiceImpl.criarConsulta(queryEmp, conZW)) {
                                    while (rsEmp.next()) {
                                        String estadoCodigo = rsEmp.getString("estado");
                                        if (estadoCodigo != null && !estadoCodigo.isEmpty()) {
                                            String queryEstado = String.format("SELECT * FROM estado WHERE codigo = %s", estadoCodigo);
                                            try (Connection conEstado = conexaoZWService.conexaoZw(ctx);
                                                 ResultSet rsEstado = ConexaoZWServiceImpl.criarConsulta(queryEstado, conEstado)) {
                                                if (rsEstado.next()) {
                                                    String sigla = rsEstado.getString("sigla");
                                                    if (sigla != null) {
                                                        String siglaU = sigla.toUpperCase();
                                                        if (siglaU.equals("RJ") || siglaU.equals("GO")) {
                                                            permitir = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            } catch (Exception e) {
                                                Uteis.logar(e, ("Erro ao consultar estado para empresa com codZW " + emp.getCodZW()).getClass());
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    Uteis.logar(e, ("Erro ao consultar Empresa via ZW para codZW " + emp.getCodZW()).getClass());
                                }
                            }
                            if (permitir) {
                                break;
                            }
                        }
                        if (!permitir) {
                            permitir = true;
                        }
                        ConfiguracoesEnum config = ConfiguracoesEnum.valueOf(a.toUpperCase());
                        ConfiguracaoSistema configuracaoSistema = consultarPorTipo(ctx, config);
                        getConfiguracaoSistemaDao().refresh(ctx, configuracaoSistema);
                        configuracaoSistema.setValor("true");
                        alterar(ctx, configuracaoSistema);
                        UtilReflection.setValor(obj, "true", a);
                    } else {
                        UtilReflection.setValor(obj, "true", a);
                    }
                } else {
                    String key = a.equals("usarIntegracaoMyzone") ? "USAR_INTEGRACAO_MYZONE" : a.toUpperCase();
                    String v = mapa.get(key) == null ? "" : mapa.get(key);
                    UtilReflection.setValor(obj, v, a);
                }
            }
            return obj;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void tratarConfigsIaAoHabilitarConfig(String ctx, Class classe, Object obj) {
        // foi definido que ao habilitar a configuração permitir_criar_treino_automatizado_ia:
        // - a permitir_aluno_criar_treino_ia_app deve ser habilitada também
        // - atividades IA inativas devem ser reativadas (se existirem)
        // e ao desabilitar, todas as atividades IA devem ser inativadas
        try {
            if (classe.equals(ConfiguracoesIaDTO.class)) {
                String permitirCriarTreinoAutomatizadoIa = (String) UtilReflection.getValor(obj, "permitir_criar_treino_automatizado_ia");

                ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
                boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

                if ("true".equalsIgnoreCase(permitirCriarTreinoAutomatizadoIa) && !configAntesPermitirCriarTreinoIa) {
                    UtilReflection.setValor(obj, "true", "permitir_aluno_criar_treino_ia_app");
                    reativarAtividadesIaAoHabilitarConfig(ctx);
                } else if ("false".equalsIgnoreCase(permitirCriarTreinoAutomatizadoIa) && configAntesPermitirCriarTreinoIa) {
                    inativarAtividadesIaAoDesabilitarConfig(ctx);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.tratarConfigsIaAoHabilitarPermissao] Erro ao tratar configs ia ao habilitar permissao: " + e.getMessage());
        }
    }

    private boolean isHabilitarPermissaoPrescricaoTreinoIA(String ctx, Class classe, Object obj) {
        try {
            if (classe.equals(ConfiguracoesIaDTO.class)) {
                String permitirCriarTreinoAutomatizadoIa = (String) UtilReflection.getValor(obj, "permitir_criar_treino_automatizado_ia");

                ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
                boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

                if ("true".equalsIgnoreCase(permitirCriarTreinoAutomatizadoIa) && !configAntesPermitirCriarTreinoIa) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.isHabilitarPermissaoPrescricaoTreinoIA] Erro ao tratar configs ia ao habilitar permissao: " + e.getMessage());
        }
        return false;
    }

    private void habilitarPermissaoPrescricaoTreinoIA(String ctx) {
        // foi definido que ao habilitar a configuração permitir_criar_treino_automatizado_ia habilitar permissão de "permitir prescrição de treino por IA" para todos perfis de acesso
        try {
            List<Perfil> todosPerfis = perfilDao.listarPerfis(ctx);
            RecursoEnum recurso = RecursoEnum.PRESCRICAO_DE_TREINO_POR_IA;
            for (Perfil perfil : todosPerfis) {
                Permissao permissaoExistente = permissaoDao.obterPermissaoPorPerfilRecurso(ctx, perfil.getCodigo(), recurso);
                if (permissaoExistente == null || permissaoExistente.getCodigo() == null) {
                    Permissao permissao = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                    permissaoDao.insert(ctx, permissao);
                } else if (!permissaoExistente.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL)) {
                    HashSet<TipoPermissaoEnum> tipoPermissao = new HashSet<>();
                    tipoPermissao.add(TipoPermissaoEnum.TOTAL);
                    permissaoExistente.setTipoPermissoes(tipoPermissao);
                    permissaoDao.update(ctx, permissaoExistente);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.habilitarPermissaoPrescricaoTreinoIA] Erro ao tratar configs ia ao habilitar permissao: " + e.getMessage());
        }
    }

    private void baixarAtividadesIa(String ctx) {
        // Quando a configuração PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA for habilitada pela primeira vez e não existir nenhuma atividade gerada pela IA, a importação deverá ocorrer automaticamente;
        try {
            Thread thread = new Thread(() -> {
                boolean existeAtividadeIA = atividadeDao.existeAtividadeIA(ctx);
                if (!existeAtividadeIA) {
                    try {
                        programaTreinoService.atualizarBancoAtividadesIA(ctx);
                    } catch (Exception ex) {
                        Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.baixarAtividadesIa] Erro ao atualizar o banco de atividades da IA: " + ex.getMessage());
                    }
                }
            });
            thread.start();
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.baixarAtividadesIa] Erro ao tentar baixar as atividades da IA: " + e.getMessage());
        }
    }

    private void inativarAtividadesIaAoDesabilitarConfig(String ctx) {
        // Quando a configuração PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA for desabilitada, todas as atividades IA devem ser inativadas
        try {
            Thread thread = new Thread(() -> {
                try {
                    String resultado = atividadeService.desativarAtividadesComIdiaPreenchidoComContexto(ctx);
                    Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.inativarAtividadesIaAoDesabilitarConfig] " + resultado);
                } catch (Exception ex) {
                    Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.inativarAtividadesIaAoDesabilitarConfig] Erro ao inativar atividades da IA: " + ex.getMessage());
                    Uteis.logar(ex, ConfiguracaoSistemaServiceImpl.class);
                }
            });
            thread.start();
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.inativarAtividadesIaAoDesabilitarConfig] Erro ao processar inativação de atividades da IA: " + e.getMessage());
        }
    }

    private void reativarAtividadesIaAoHabilitarConfig(String ctx) {
        // Quando a configuração PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA for habilitada, verifica se existem atividades IA inativas e as reativa
        try {
            Thread thread = new Thread(() -> {
                try {
                    boolean existeAtividadeIA = atividadeDao.existeAtividadeIA(ctx);
                    if (existeAtividadeIA) {
                        String resultado = atividadeService.reativarAtividadesComIdiaPreenchidoComContexto(ctx);
                        Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.reativarAtividadesIaAoHabilitarConfig] " + resultado);
                    } else {
                        Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.reativarAtividadesIaAoHabilitarConfig] Nenhuma atividade IA encontrada para reativação.");
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.reativarAtividadesIaAoHabilitarConfig] Erro ao reativar atividades da IA: " + ex.getMessage());
                    Uteis.logar(ex, ConfiguracaoSistemaServiceImpl.class);
                }
            });
            thread.setDaemon(true);
            thread.start();
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[ConfiguracaoSistemaServiceImpl.reativarAtividadesIaAoHabilitarConfig] Erro ao processar reativação de atividades da IA: " + e.getMessage());
        }
    }

    public void gravarCfgsDTO(String chave, Class classe, Object obj) throws ServiceException{
        try {
            String ctx = "";
            if (isBlank(chave)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            } else {
                ctx = chave;
            }
            tratarConfigsIaAoHabilitarConfig(ctx, classe, obj);
            boolean habilitarPermissaoPrescricaoTreinoIAParaPerfis = isHabilitarPermissaoPrescricaoTreinoIA(ctx, classe, obj);
            List<String> listAttributes = UtilReflection.getListAttributes(classe);
            validarDados(obj, listAttributes);

            ConfiguracaoSistema valorAntesConfigQtFichasHabilitarTrIa = consultarPorTipo(ctx, ConfiguracoesEnum.QUANTIDADE_FICHAS_HABILITAR_TREINO_IA);
            boolean configAntesQtFichasHabilitarTrIa = valorAntesConfigQtFichasHabilitarTrIa != null && Boolean.parseBoolean(valorAntesConfigQtFichasHabilitarTrIa.getValor());

            for (String a : listAttributes) {

                if (a.equals("produtos") || a.equals("ordens_dobras")){
                    continue;
                }
                ConfiguracoesEnum config;
                if(a.equals("usarIntegracaoMyzone")){
                    config = ConfiguracoesEnum.USAR_INTEGRACAO_MYZONE;
                } else {
                    try {
                        config = ConfiguracoesEnum.valueOf(a.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        continue;
                    }
                }
                ConfiguracaoSistema configuracaoSistema = consultarPorTipo(ctx, config);
                getConfiguracaoSistemaDao().refresh(ctx, configuracaoSistema);
                String valorAntesAlteracao = configuracaoSistema.getValor();
                String v = (String) UtilReflection.getValor(obj, a);
                configuracaoSistema.setValor(v == null ? "" : v);
                alterar(ctx, configuracaoSistema);

                if (config.equals(ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA)) {
                    if (habilitarPermissaoPrescricaoTreinoIAParaPerfis && "true".equalsIgnoreCase(v)) {
                        habilitarPermissaoPrescricaoTreinoIA(ctx);
                        baixarAtividadesIa(ctx);
                    }
                }

                if (config.equals(ConfiguracoesEnum.NR_AULA_EXPERIMENTAL_ALUNO) && isNotBlank(v) && !valorAntesAlteracao.equals(v)) {
                    atualizarQuantidadeAulasExperimentais(ctx, configuracaoSistema.getValorAsInteger(), Integer.parseInt(v));

                    IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                    integracaoWS.atualizarConfiguracaoNrAulasExperimentais(ctx, Integer.parseInt(valorAntesAlteracao), Integer.parseInt(v));
                }

                if (config.equals(ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET) && !valorAntesAlteracao.equals(v)) {
                    try {
                        incrementarVersaoProgramasSimples(ctx);
                    }catch (Exception e){
                        Uteis.logar(e, ConfiguracaoSistemaService.class);
                    }

                }
                if (config.equals(ConfiguracoesEnum.NR_VALIDAR_VEZES_MODALIDADE) && isNotBlank(v) && !valorAntesAlteracao.equals(v)) {
                    atualizarQuantidadeAulasModalidade(ctx, configuracaoSistema.getValorAsInteger(), Integer.parseInt(v));
                }

                if (a.equals("quantidade_fichas_habilitar_treino_ia")) {
                    Boolean habilitarTreinoIa = Boolean.valueOf(v);

                    if (habilitarTreinoIa && !configAntesQtFichasHabilitarTrIa) {
                        String nivelInicianteStr = (String) UtilReflection.getValor(obj, "nivel_iniciante");
                        String nivelIntermediarioStr = (String) UtilReflection.getValor(obj, "nivel_intermediario");
                        String nivelAvancadoStr = (String) UtilReflection.getValor(obj, "nivel_avancado");

                        Integer nivelIniciante = nivelInicianteStr != null && !nivelInicianteStr.isEmpty() ? Integer.valueOf(nivelInicianteStr) : 0;
                        Integer nivelIntermediario = nivelIntermediarioStr != null && !nivelIntermediarioStr.isEmpty() ? Integer.valueOf(nivelIntermediarioStr) : 0;
                        Integer nivelAvancado = nivelAvancadoStr != null && !nivelAvancadoStr.isEmpty() ? Integer.valueOf(nivelAvancadoStr) : 0;

                        programaTreinoService.registrarWorkoutTreinoIa(ctx, nivelIniciante, nivelIntermediario, nivelAvancado);
                    }
                }

            }
        } catch (Exception ex) {
            if (ex instanceof ServiceException) {
                Integer codigoErro = ((ServiceException) ex).getCodigoError();
                if (codigoErro == 400) {
                    throw new ServiceException(ConfiguracaoSistemaExcecoes.ERRO_SALVAR_CONFIGURACOES_VALOR_ZERO_POSITIVO.getDescricaoExcecao() + ex.getMessage(), ex, HttpStatus.BAD_REQUEST.value());
                }
            }
            throw new ServiceException(ConfiguracaoSistemaExcecoes.ERRO_SALVAR_CONFIGURACOES, ex);
        }
    }

    private void atualizarQuantidadeAulasModalidade(final String ctx, Integer aulasModalidadesAntesAlteracao, Integer valorNovo) throws Exception {
        String sql = "UPDATE configuracaosistema \n" +
                "SET valor = \n" +
                "(CASE \n" +
                "   WHEN (CAST(NULLIF(valor, '') AS INTEGER) - " + aulasModalidadesAntesAlteracao + ") + " + valorNovo + " < 0 THEN '0' \n" +
                "   ELSE CAST((CAST(NULLIF(valor, '') AS INTEGER) - " + aulasModalidadesAntesAlteracao + " + " + valorNovo + ") AS VARCHAR) \n" +
                "END) \n" +
                "WHERE configuracao = 'NR_VALIDAR_VEZES_MODALIDADE'";
        configuracaoSistemaDao.executeNative(ctx, sql);
    }


    public void validarDados(Object obj, List<String> listAttributes) throws ServiceException{
        String mensagemErro = "";
        for (String atributo : listAttributes) {
            if (atributo.equals("periodo_usado_bi")) {
                String valor = (String) UtilReflection.getValor(obj, atributo);
                if(!UteisValidacao.somenteNumeroZeroPositivo(valor)){
                    mensagemErro = "Período abrangido no BI (em dias)";
                }
            }
            if (atributo.equals("inativos_a_x_dias")) {
                String valor = (String) UtilReflection.getValor(obj, atributo);
                if(!UteisValidacao.somenteNumeroZeroPositivo(valor)){
                    mensagemErro = mensagemErro.length() > 0 ? (mensagemErro + ", ") : mensagemErro;
                    mensagemErro = mensagemErro + "Inativos a X dias (Deixar '0' para mostrar todos)";
                }
            }
        }
        if(mensagemErro.length() > 0){
            throw new ServiceException(mensagemErro, HttpStatus.BAD_REQUEST.value());
        }
    }

    public void atualizarManutencao(Integer valorNovo) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            atualizarQuantidadeAulasExperimentaisTodos(ctx, valorNovo);

            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            integracaoWS.atualizarConfiguracaoNrAulasExperimentais(ctx, -1, valorNovo);
        } catch (Exception ex) {
            throw new ServiceException(ConfiguracaoSistemaExcecoes.ERRO_SALVAR_CONFIGURACOES, ex);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ConfiguracaoDobras> obterCfgsDobras(final String ctx) throws Exception{
        List<ConfiguracaoDobras> all = cfgDobrasDao.findAll(ctx);
        if(UteisValidacao.emptyList(all)){
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(0, DobrasEnum.ABDOMINAL)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(1, DobrasEnum.SUPRA_ILIACA)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(2, DobrasEnum.PEITORAL)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(3, DobrasEnum.TRICEPS)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(4, DobrasEnum.COXA_MEDIAL)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(5, DobrasEnum.BICEPS)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(6, DobrasEnum.SUBESCAPULAR)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(7, DobrasEnum.AXILARMEDIA)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(8, DobrasEnum.SUPRA_ESPINHAL)));
            all.add(cfgDobrasDao.insert(ctx, new ConfiguracaoDobras(9, DobrasEnum.PANTURRILHA)));
        }
        return Ordenacao.ordenarLista(all, "ordem");
    }

    public void gravarCfgsDobras(final String ctx, List<ConfiguracaoDobras> cfgs) throws Exception {
        for (ConfiguracaoDobras cfg : cfgs) {
            cfg.setOrdem(cfgs.indexOf(cfg));
            cfgDobrasDao.update(ctx, cfg);
        }
    }

    public void sincronizarBooking(final String ctx, final Integer empresa) throws Exception {
        gymPassBookingService.normalizar(ctx, empresa);
    }
    public void executarExclusaoCliNaoExisteZw(final String ctx, final Integer empresa) throws ServiceException {
        try {
            Map<Integer, Integer> clientesTR = listarCodigoClienteClientesTR(ctx, empresa);
            List<Integer> clientesZW = listarCodigoClientesZW(ctx, empresa);
            clientesTR.forEach((codigoCliente, matricula) -> {
                if (!clientesZW.contains(codigoCliente)) {
                    try {
                        deletaNotificacoesAgendadasDeClientesQueNaoExistemZW(ctx, codigoCliente);
                        clientePesquisaDao.delete(ctx, matricula);
                        clienteSinteticoService.excluirAluno(ctx, codigoCliente, null);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void deletaNotificacoesAgendadasDeClientesQueNaoExistemZW(String ctx, Integer codigoCliente) {
        try {
            configuracaoSistemaDao.executeNative(ctx, "DELETE FROM notificacaoaulaagendada WHERE " +
                    "cliente = (SELECT codigo FROM clientesintetico WHERE codigocliente = " + codigoCliente + ")");
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
        }
    }

    private Map<Integer, Integer> listarCodigoClienteClientesTR(String ctx, Integer empresaZW) throws Exception {
        Connection conTR = clientePesquisaDao.getConnection(ctx);
        Map<Integer, Integer> mapaClientes = new HashMap<>();
        try {
            try (ResultSet rs = conTR.prepareStatement(
                    "select codigocliente, matricula from clientesintetico c where empresa = " + empresaZW).executeQuery()) {
                while (rs.next()) {
                    mapaClientes.put(rs.getInt("codigocliente"), rs.getInt("matricula"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
        }

        return mapaClientes;
    }

    private List<Integer> listarCodigoClientesZW(String ctx, Integer empresaZW) throws Exception {
        List<Integer> clientes;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            clientes = new ArrayList<>();
            try {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        "select codigo from cliente where empresa = " + empresaZW, conZW)) {
                    while (rs.next()) {
                        clientes.add(rs.getInt("codigo"));
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
            }
        }

        return clientes;
    }

    public List<ConfigMgb> obterConfigsMGB(final String ctx) throws ServiceException {
        try {
            List<Empresa> emp = empresaService.obterTodos(ctx);
            List<ConfigMgb> listaConfigMGB = new ArrayList<>();
            for (Empresa e : emp) {
                ConfigMgb configM = new ConfigMgb();
                configM.setNome(e.getNome());
                configM.setEmpresa(e.getCodZW()); // setEmpresa deverá ser preenchido com o codZw da empresa
                listaConfigMGB.add(configM);
            }
            Map<String, String> params = new HashMap<>();
            params.put("chave", ctx);
            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
            String urlParams = ExecuteRequestHttpService.
                    obterUrlComParams(url + "/prest/mgb/tokensEmpresas", params, "UTF-8");
            String ret = ExecuteRequestHttpService.executeRequestGET(urlParams);
            JSONArray retornoArray = new JSONArray(ret);
            List<ConfigMgb> jsonMGB = JSONMapper.getList(retornoArray, ConfigMgb.class);
            for (ConfigMgb lista : listaConfigMGB) {
                for (ConfigMgb config : jsonMGB) {
                    if (lista.getEmpresa().equals(config.getEmpresa())) {
                        lista.setToken(config.getToken());
                    }
                }
            }
            return listaConfigMGB;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void persistirIntegracoesMGB(String ctx, List<ConfigMgb> configMGB) {
        try {
            ConfiguracaoSistema configuracaoSistema = consultarPorTipo(ctx, ConfiguracoesEnum.MGB);
            JSONObject json = new JSONObject();
            for(ConfigMgb c : configMGB){
                json.put("MGB - " + c.getNome(), c.getToken());
            }
            configuracaoSistema.setValor(json.toString());
            alterar(ctx, configuracaoSistema);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void inserirConfigsMGB(final String ctx, List<ConfigMgb> configMGB) throws ServiceException {
        try {
            Map<String, String> paramsObter = new HashMap<>();
            paramsObter.put("chave", ctx);
            String urlObter = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
            String urlParams = ExecuteRequestHttpService.
                    obterUrlComParams(urlObter + "/prest/mgb/tokensEmpresas", paramsObter, "UTF-8");
            String ret = ExecuteRequestHttpService.executeRequestGET(urlParams);
            JSONArray retornoArray = new JSONArray(ret);
            List<ConfigMgb> listaMGB = JSONMapper.getList(retornoArray, ConfigMgb.class);
            for (ConfigMgb config : configMGB) {
                Map<String, String> params = new HashMap<>();
                params.put("chave", ctx);
                params.put("empresa", String.valueOf(config.getEmpresa())); //getEmpresa() = empresa.codZw
                params.put("token", config.getToken());

                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
                if (!isEmpresaIntegradaMGB(config, listaMGB)) {
                    executeRequestHttpService.
                            executeRequestInner(url + "/prest/mgb/insertToken", params, "UTF-8");
                } else {
                    executeRequestHttpService.
                            executeRequestInner(url + "/prest/mgb/updateToken", params, "UTF-8");
                }
            }
            persistirIntegracoesMGB(ctx, configMGB);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private boolean isEmpresaIntegradaMGB(ConfigMgb config, List<ConfigMgb> listaMGB) {
        for (ConfigMgb c : listaMGB) {
            if (c.getEmpresa().equals(config.getEmpresa())) {
                return true;
            }
        }
        return false;
    }

    private void deletarClientesPesquisa(final String ctx, final Integer empresa, Integer[] index) throws ServiceException {
        try {
            List<ClienteSintetico> clientes = clienteSinteticoService.obterTodos(ctx, empresa, index);
            List<ClientePesquisa> clientesPesq = clientePesquisaDao.findListByAttributes(ctx, new String[]{"empresa"}, new Object[]{empresa}, null, 0, 0);
            boolean deletar = true;
            for (ClientePesquisa clipesq : clientesPesq) {
                deletar = true;
                for (ClienteSintetico cli : clientes) {
                    if (clipesq.getMatricula().equals(cli.getMatricula())) {
                        deletar = false;
                        break;
                    }
                }
                if (deletar) {
                    clientePesquisaDao.delete(ctx, clipesq.getMatricula());
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void preencherPerimetriasAutomatico(boolean esquerdaDireita, List<ConfiguracaoPerimetro> lista) {

        int i = 1;
        for (ConfiguracaoPerimetro conf : lista) {
            if (esquerdaDireita) {
                if (conf.getDireita() != null) {
                    conf.setTabIndexEsq(i++);
                } else {
                    conf.setTabIndexEsq(i++);
                }
            } else {
                conf.setTabIndexEsq(i++);
                if (conf.getDireita() != null) {
                    conf.setTabIndexDir(i++);
                }
            }
        }

        if (esquerdaDireita) {
            for (ConfiguracaoPerimetro conf : lista) {
                if (conf.getDireita() != null) {
                    conf.setTabIndexDir(i++);
                }
            }
        }
    }
    public List<ConfiguracaoSistema> obterTodosNativo(final String ctx) throws ServiceException {
        try {
            List<ConfiguracaoSistema> configs = new ArrayList();
            try (ResultSet resultSet = getConfiguracaoSistemaDao().createStatement(ctx, "select * from ConfiguracaoSistema")) {
                while (resultSet.next()) {
                    ConfiguracaoSistema config = new ConfiguracaoSistema();
                    config.setCodigo(resultSet.getInt("codigo"));
                    config.setConfiguracao(ConfiguracoesEnum.getFromName(resultSet.getString("configuracao")));
                    config.setValor(resultSet.getString("valor"));
                    config.setTipoNotificacao(resultSet.getString("tiponotificacao") != null ? TipoNotificacaoEnum.obterPorID(resultSet.getInt("tiponotificacao")) : null);
                    configs.add(config);
                }
            }
            return configs;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoIntegracoesDTO obterConfigsBookingGympass(final String ctx, Integer empresaId, ConfiguracaoIntegracoesDTO configs) throws ServiceException {
        try {
            ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
            ConfigGymPass configGymPass = configService.obterPorEmpresaTR(ctx, empresaId);
            configs.setUsar_gympass_booking(configGymPass.getCodigoGymPass() == null || UteisValidacao.emptyString(configGymPass.getCodigoGymPass()) ? "false" : "true");
            configs.setCodigo_gympass_booking(configGymPass.getCodigoGymPass() == null ? "" : configGymPass.getCodigoGymPass());
            return configs;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGoGoodDTO obterConfigsGoGood(final String ctx, Integer empresaId, ConfiguracaoIntegracoesDTO configs) throws ServiceException {
        try {
            ConfigGoGoodService configService = UtilContext.getBean(ConfigGoGoodService.class);
            ConfigGoGood configGoGood = configService.obterPorEmpresaTR(ctx, empresaId);
            configs.setToken_academy_gogood(configGoGood.getTokenAcademyGoGood());
            return new ConfigGoGoodDTO(configGoGood);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void gravarCfgsNotificacaoDTO(String chave, Class classe, Object obj) throws ServiceException{
        try {
            String ctx = "";
            if (isBlank(chave)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            } else {
                ctx = chave;
            }
            List<String> listAttributes = UtilReflection.getListAttributes(classe);
            ConfiguracaoSistema configuracaoSistema;
            for (String a : listAttributes) {
                if (a.equals("numero_dias_notificar_treino_vencido") || a.equals("sms_notificacao")) {
                    ConfiguracoesEnum config = ConfiguracoesEnum.valueOf(a.toUpperCase());
                    configuracaoSistema = consultarPorTipo(ctx, config);
                } else {
                    TipoNotificacaoEnum config = TipoNotificacaoEnum.valueOf(a.toUpperCase());
                    configuracaoSistema = consultarPorTipoNotificacao(ctx, config);
                }
                String v = (String) UtilReflection.getValor(obj, a);
                configuracaoSistema.setValor(v == null ? "" : v);
                alterar(ctx, configuracaoSistema);
            }
        } catch (Exception ex) {
            throw new ServiceException(ConfiguracaoSistemaExcecoes.ERRO_SALVAR_CONFIGURACOES, ex);
        }
    }

    public ConfiguracaoSistema consultarPorTipoNotificacao(String ctx, TipoNotificacaoEnum cfg) throws ServiceException {
        try {
            ConfiguracaoSistema conf = null;
            try (ResultSet rs = getConfiguracaoSistemaDao().createStatement(ctx, "SELECT * FROM configuracaosistema WHERE tiponotificacao = " + cfg.ordinal())) {
                if (rs.next()) {
                    conf = new ConfiguracaoSistema();
                    conf.setCodigo(rs.getInt("codigo"));
                    conf.setConfiguracao(ConfiguracoesEnum.getFromName(rs.getString("configuracao")));
                    conf.setTipoNotificacao(rs.getString("tiponotificacao") != null ? TipoNotificacaoEnum.obterPorID(rs.getInt("tiponotificacao")) : null);
                    conf.setValor(rs.getString("valor"));
                }
            }
            if (conf == null) {
                return povoar(ctx, cfg);
            } else {
                return conf;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void normalizarImportacao(final String ctx, Integer empresa) throws ServiceException {
        new Thread() {
            @Override
            public void run() {
                try {
                    List<Integer> codigosExcluir = new ArrayList<>();
                    List<ClienteSintetico> matriculas = clienteSinteticoService.matriculas(ctx, empresa);
                    for(ClienteSintetico cs : matriculas){
                        Map<String, String> params = new HashMap<>();
                        params.put("key", ctx);
                        params.put("token", "6vt9biFTxEwgjqYnmj2JbUN74EbHd|CHAVENEOZW");
                        params.put("operacao", "consultarPorMatricula");
                        params.put("parametro", cs.getMatriculaString());
                        String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                        IntegracaoCadastrosWSConsumer integracaoCadastrosWSConsumer = new IntegracaoCadastrosWSConsumer();
                        ClienteSintetico retorno = integracaoCadastrosWSConsumer.consultarClienteSintetico(url, ctx, cs.getCodigoCliente());
                        if(!cs.getMatricula().equals(retorno.getMatricula())){
                            codigosExcluir.add(cs.getCodigoCliente());
                            clienteSinteticoService.excluirAluno(ctx, null, cs);
                            Uteis.logar(null, "Codigo de cliente errado encontrado no ZW. \nExcluindo cliente do treino:" + cs.getCodigoCliente() + " - " + cs.getNome());
                        }
                    }
                    deletarClientesPesquisa(ctx, empresa, codigosExcluir.toArray(new Integer[codigosExcluir.size()]));
                } catch (Exception e) {
                    Uteis.logar(e, this.getClass());
                } finally {
                    Aplicacao.putProp(ctx, Aplicacao.importandoClientesZw + empresa, Boolean.FALSE.toString());
                }
            }
        }.start();
    }

    public List<MqvDTO> obterIntegracoesMQV(final String ctx) throws ServiceException {
        List<Empresa> empresas = empresaService.obterTodos(ctx);
        List<MqvDTO> mqvDTOS = new ArrayList<>();

        empresas.forEach(n -> {
            MqvDTO mqvDTO = new MqvDTO();
            mqvDTO.setEmpresa(n.getCodigo());
            mqvDTO.setNome(n.getNome());
            mqvDTO.setToken(n.getTokenMqv() != null ? n.getTokenMqv() : "");
            mqvDTOS.add(mqvDTO);
        });

        return mqvDTOS;
    }

    public List<TotalPassDTO> obterIntegracoesTotalPass(final String ctx) throws ServiceException {
        List<Empresa> empresas = empresaService.obterTodos(ctx);
        List<TotalPassDTO> totalPassDTOS = new ArrayList<>();

        empresas.forEach(n -> {
            TotalPassDTO totalPassDTO = new TotalPassDTO();
            totalPassDTO.setEmpresa(n.getCodigo());
            totalPassDTO.setNome(n.getNome());
            totalPassDTOS.add(totalPassDTO);
        });

        return totalPassDTOS;
    }

    @Override
    public void salvarIntegracoesTotalPass(String ctx, List<TotalPassDTO> totalPassDTOList) throws ServiceException {
        try {
            for (TotalPassDTO totalPassDTO : totalPassDTOList) {
                Empresa empresa = empresaService.obterPorId(ctx, totalPassDTO.getEmpresa());
                empresa = empresaService.alterar(ctx, empresa);
                if (!SuperControle.independente(ctx)) {
                    JSONObject resultJson = sincronizarTokenTotalPassEmpresaZw(ctx, empresa);
                    if (!resultJson.optBoolean("sucesso")) {
                        throw new RuntimeException("erro_ao_salvar_tokenmqv_no_zw");
                    }
                }
            }
            persistirIntegracoesLog(ctx);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void persistirIntegracoesLog(String ctx) {
        try {
            ConfiguracaoSistema configuracaoSistema = consultarPorTipo(ctx, ConfiguracoesEnum.INTEGRACOES);
            JSONObject json = new JSONObject();
            try (ResultSet rs = configuracaoSistemaDao.createStatement(ctx, "select tokenmqv, codigo, nome from empresa e")) {
                while (rs.next()) {
                    json.put("MQV " + rs.getString("nome"), rs.getString("tokenmqv"));
                }
            }
            try (ResultSet rsGympass = configuracaoSistemaDao.createStatement(ctx, "select rev, codigogympass, usargympassbooking, nome " +
                    "from configgympass_aud ca order by rev ")) {
                while (rsGympass.next()) {
                    if (UteisValidacao.emptyString(rsGympass.getString("nome"))) {
                        continue;
                    }
                    JSONObject gympass = new JSONObject();
                    gympass.put("codigogympass", rsGympass.getString("codigogympass"));
                    gympass.put("usargympassbooking", rsGympass.getBoolean("usargympassbooking"));
                    json.put("Gympass " + rsGympass.getString("nome"), gympass);
                }
            }
            configuracaoSistema.setValor(json.toString());
            alterar(ctx, configuracaoSistema);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void salvarIntegracoesMQV(final String ctx, List<MqvDTO> mqvDTOList) throws ServiceException {
        try {
            for (MqvDTO mqvDTO : mqvDTOList) {
                Empresa empresa = empresaService.obterPorId(ctx, mqvDTO.getEmpresa());
                empresa.setTokenMqv(mqvDTO.getToken());
                empresa = empresaService.alterar(ctx, empresa);
                if (!SuperControle.independente(ctx)) {
                    JSONObject resultJson = sincronizarTokenMqvEmpresaZw(ctx, empresa);
                    if (!resultJson.optBoolean("sucesso")) {
                        throw new RuntimeException("erro_ao_salvar_tokenmqv_no_zw");
                    }
                }
            }
            persistirIntegracoesLog(ctx);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public JSONObject sincronizarTokenTotalPassEmpresaZw (String ctx, Empresa empresa) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/crm/ConfigTokenTotalPass");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.getCodZW().toString()));
        params.add(new BasicNameValuePair("token", empresa.getTokenMqv()));

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    public JSONObject sincronizarTokenMqvEmpresaZw (String ctx, Empresa empresa) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/crm/ConfigTokenMqv");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.getCodZW().toString()));
        params.add(new BasicNameValuePair("token", empresa.getTokenMqv()));

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private Map<String, String> montarMapaLogConfig(ViewUtils view, ResultSet rsRevs, ConfiguracoesEnum configuracao, String campo) throws Exception{
         return new HashMap(){{
            switch (configuracao){
                case MGB:
                case INTEGRACOES:
                    try {
                        JSONObject jsonMGB = new JSONObject(rsRevs.getString("valor"));
                        for(String k : jsonMGB.keySet()){
                            JSONObject valores = jsonMGB.optJSONObject(k);
                            if(valores == null){
                                put(k, jsonMGB.get(k).toString());
                            } else {
                                for(String jk : valores.keySet()){
                                    put(k + "-" + jk, valores.get(jk).toString());
                                }
                            }

                        }
                    }catch (Exception e){
                        put(campo, rsRevs.getString("valor"));
                        Uteis.logar(e, this.getClass());
                    }

                    break;
                default:
                    if(configuracao.getTipo().equals(ConfiguracaoTipoEnum.BOOLEAN)){
                        String v = rsRevs.getString("valor");
                        put(campo, Boolean.valueOf(v) ? view.getLabel("cadastros.sim") : view.getLabel("cadastros.nao"));
                    }else {
                        put(campo, rsRevs.getString("valor"));
                    }

            }
        }};
    }

    private ResultSet descobrirQuemFoiAlteradoNoMomento(String chave, Long momento, String username, Integer revtype, String configs) throws Exception{
        return configuracaoSistemaDao.createStatement(chave,
                "select ca.configuracao, ca.valor, ca.rev from configuracaosistema_aud ca\n" +
                        " inner join customrevisionentity c on c.id = ca.rev \n" +
                        " where ca.revtype = " + revtype +
                        " and c.username = '" + username +
                        "' and ca.configuracao in (" + configs +
                        ") and c.\"timestamp\"/5000 = " + momento);
    }

    public void purgeCache(final String key) {
        CONFIG_CACHE.remove(key);
    }

    public boolean isOnCache(final String key) {
        long agora = System.currentTimeMillis();
        if (CONFIG_CACHE.get(key) != null && ((agora - CONFIG_CACHE.get(key).getLastUpdate()) < TEMPO_CACHE_HORAS)){
            return true;
        }
        return false;
    }

    public ConfiguracaoSistemaJSON getCache(final String key) {
        Uteis.logarDebug(String.format("Retornado configs %s from cache. LastUpdate: %s",
                key, new Date(CONFIG_CACHE.get(key).getLastUpdate())));
        return CONFIG_CACHE.get(key);
    }

    public void putsOnCache(final String key, ConfiguracaoSistemaJSON json) {
        json.setLastUpdate(System.currentTimeMillis());
        CONFIG_CACHE.put(key, json);
    }

    @Override
    public void notificarOuvintes(String ctx, HttpServletRequest request) {
        try {
            if (request != null) {
                URL u = new URL(request.getRequestURL().toString());
                String url = String.format("%s://%s:%s%s/prest/config/%s/reload",
                        u.getProtocol(),
                        u.getHost(),
                        u.getPort() == -1 ? 80 : u.getPort(),
                        request.getContextPath(),
                        ctx);
                Propagador.doExecuteRequestInstances(url, null, 500);
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    @Override
    public String getDescricaoParaLog(Object obj, Object obj2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(obj, obj2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    @Override
    public Boolean verificaPermiteDependente(String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                String sql = "select exists(select * from configuracaosistema where usaplanorecorrentecompartilhado = true)";
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW);
                if (rs.next()) {
                    return rs.getBoolean("exists");
                }
            } catch (Exception e) {
                Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
            }
        }
        return false;
    }

    @Override
    public Boolean verificaPermiteRedirecionarAulaExperimentalCRMAGENDA(String ctx) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "select exists(select * from configuracaosistemacrm where direcionaragendamentosexperimentaisagenda = true)";
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                if (rs.next()) {
                    return rs.getBoolean("exists");
                }
            }
        }
        return false;
    }

    public String sincronizarAlunosMgb(final String ctx, final Integer empresa) throws ServiceException {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("chave", ctx);
            params.put("empresa", empresa.toString());
            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(
                    url + "/prest/mgb/sincronizar-alunos-ativos",
                    params,
                    "UTF-8"
            );
            String retorno = ExecuteRequestHttpService.executeRequestGET(urlParams);
            return retorno;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String setAlunoParqFalse(final String ctx, final Integer matricula) throws Exception {
        Connection conTR = clientePesquisaDao.getConnection(ctx);
        String sqlSelectCli = "select codigo from clientesintetico c where matricula = " + matricula;
        try (ResultSet rs = conTR.prepareStatement(sqlSelectCli).executeQuery()) {
            if (rs.next()) {
                Integer codigoCliente = rs.getInt("codigo");
                String sqlExisteParq = "select exists (select * from respostaclienteparq where cliente_codigo = " + codigoCliente + ")";
                try (ResultSet rsParq = conTR.prepareStatement(sqlExisteParq).executeQuery()) {
                    if (rsParq.next() && !rsParq.getBoolean("exists")) {
                        return "Não foi localizado parq respondido para a matrícula informada!";
                    }
                }
                try {
                    // remover resposta do parq do aluno:
                    String sqlUpdateCli = "update clientesintetico set parq = false where codigo = " + codigoCliente;
                    configuracaoSistemaDao.executeNative(ctx, sqlUpdateCli);

                    String sqlDeleteRC =
                            "delete from respostacliente r where cliente_codigo = " + codigoCliente +
                            " and respostaclienteparq_codigo = (select codigo from respostaclienteparq where cliente_codigo = " + codigoCliente + ")";
                    configuracaoSistemaDao.executeNative(ctx, sqlDeleteRC);

                    String sqlDeleteRCP = "delete from respostaclienteparq where cliente_codigo = " + codigoCliente;
                    configuracaoSistemaDao.executeNative(ctx, sqlDeleteRCP);

                    // setar parq false para aluno no zw:
                    Map<String, String> params = new HashMap<>();
                    params.put("chave", ctx);
                    params.put("matricula", matricula.toString());
                    String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                    String urlParams = ExecuteRequestHttpService.obterUrlComParams(
                            url + "/prest/manutencao/set-aluno-parq-false",
                            params,
                            "UTF-8"
                    );
                    String retorno = ExecuteRequestHttpService.executeRequestGET(urlParams);
                    if (new JSONObject(retorno).has("content") && new JSONObject(retorno).getString("content").equals("ok")) {
                        return "Removido com sucesso o parq do aluno!";
                    } else {
                        return "Ocorreu um problema ao remover o parq do aluno!";
                    }
                } catch (Exception e) {
                    throw new ServiceException(e.getMessage());
                }
            } else {
                return "Não foi localizado aluno para a matrícula informada!";
            }
        }
    }

    public String sincronizarProfessoresTW(final String ctx, final Integer empresaZW) throws ServiceException {
        if (!SuperControle.independente(ctx)) {
            Integer sucesso = 0;
            Integer erro = 0;
            try {
                Map<Integer, String> zwProfessoresTW = zwListarProfessoresTW(ctx, empresaZW);
                List<ProfessorSintetico> listProfessorTR = professorSinteticoService.obterTodos(ctx, empresaZW, false);
                boolean alterar = false;
                for (ProfessorSintetico professorTR : listProfessorTR) {
                    Integer codigoColaborador = professorTR.getCodigoColaborador();
                    if (zwProfessoresTW.containsKey(codigoColaborador)) {
                        if (professorTR.getProfessorTW() == null || !professorTR.getProfessorTW()) {
                            professorTR.setProfessorTW(true);
                            alterar = true;
                        }
                    } else if (professorTR.getProfessorTW() == null || professorTR.getProfessorTW()) {
                        // Se não está na lista zwProfessoresTW então é porque não deve estar como professorTW no treino
                        professorTR.setProfessorTW(false);
                        alterar = true;
                    }

                    if(professorTR.getNome() == null || UteisValidacao.emptyString(professorTR.getNome())) {
                        if(zwProfessoresTW.get(codigoColaborador) != null && !UteisValidacao.emptyString(zwProfessoresTW.get(codigoColaborador))) {
                            professorTR.setNome(zwProfessoresTW.get(codigoColaborador));
                            alterar = true;
                        }
                    }
                    if (alterar) {
                        try {
                            professorSinteticoService.alterar(ctx, professorTR);
                            sucesso++;
                        } catch (Exception e) {
                            erro++;
                        }
                    }
                    alterar = false;
                }
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
            return sucesso + " professores não estavam sincronizados como professorTW e foram atualizados com sucesso. E " + erro + " professores tiveram algum problema ao sincronizar!";
        }
        return "A chave informada é treino independente";
    }

    private Map<Integer, String> zwListarProfessoresTW(String ctx, Integer empresaZW) throws Exception {
        Map<Integer, String> professores = new HashMap<>();
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                String sql = "select distinct c.codigo,p.nome \n" +
                        "from colaborador c \n" +
                        "inner join tipocolaborador t on c.codigo = t.colaborador \n" +
                        "inner join pessoa p on c.pessoa = p.codigo \n" +
                        "where t.descricao = 'TW' \n" +
                        "and c.empresa = " + empresaZW + " \n" +
                        "order by c.codigo";
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                    while (rs.next()) {
                        professores.put(rs.getInt("codigo"), rs.getString("nome"));
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
            }
        }
        return professores;
    }

    @Override
    public String sincronizarAtividadesProfessoresTW(final String ctx, final Integer empresaZW) throws ServiceException {
        if (!SuperControle.independente(ctx)) {
            Integer sucesso = 0;
            String erro = "";
            try {
                Map<String, Object> parametros = new HashMap<>();
                parametros.put("empresaZW", empresaZW);
                StringBuilder sql = new StringBuilder();
                sql.append(" where obj.professorCarteira.codigoPessoa = obj.professorMontou.codigoPessoa ");
                sql.append(" AND obj.professorCarteira.codigo <> obj.professorMontou.codigo ");
                sql.append(" AND obj.cliente.empresa = :empresaZW ");
                List<ProgramaTreino> programasTreino = programaDao.findByParam(ctx, sql, parametros);

                for(ProgramaTreino programa : programasTreino){
                    Map<String, Object> parametrosProfessor = new HashMap<>();
                    parametrosProfessor.put("codigoPessoa", programa.getProfessorMontou().getCodigoPessoa());
                    parametrosProfessor.put("empresaZW", empresaZW);

                    StringBuilder sqlProfessor = new StringBuilder();
                    sqlProfessor.append(" where obj.codigoPessoa = :codigoPessoa ");
                    sqlProfessor.append(" AND obj.empresa.codZW = :empresaZW ");
                    List<ProfessorSintetico> professorSintetico = professorsinteticoDao.findByParam(ctx, sqlProfessor, parametrosProfessor);

                    if(!professorSintetico.isEmpty()){
                        programa.setProfessorMontou(professorSintetico.get(0));
                        programaDao.alterar(ctx, programa);
                        sucesso++;
                    }else{
                        erro+= programa.getProfessorMontou().getCodigoPessoa()+",";
                    }
                }
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
            return sucesso + " Atividades dos professores foram atualizados com sucesso. Não foi encontrado Professor com vinculado na unidade "+empresaZW+" com esse(s) código(s) pessoa "+erro;
        }
        return "A chave informada é treino independente";
    }

    public ConfiguracaoSistemaJSON alterarConfiguracoesParaApp(ConfiguracaoSistemaJSON json) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ConfiguracaoSistemaJSON jsonParaApp = mapper.readValue(
                mapper.writeValueAsString(json),
                ConfiguracaoSistemaJSON.class
        );
        ConfigJSON permitirCriarTreinoAutomatizadoIA = jsonParaApp.getConfiguracoes().stream()
                .filter(cfg -> ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA.name().equals(cfg.getId()))
                .findFirst()
                .orElse(null);

        if (jsonParaApp.getConfiguracoes() != null && permitirCriarTreinoAutomatizadoIA != null
                && permitirCriarTreinoAutomatizadoIA.getValor() != null
                && permitirCriarTreinoAutomatizadoIA.getValor().equals("false")) {

            jsonParaApp.getConfiguracoes().stream()
                    .filter(cfg -> ConfiguracoesEnum.PERMITIR_ALUNO_CRIAR_TREINO_IA_APP.name().equals(cfg.getId()))
                    .findFirst()
                    .ifPresent(cfg -> cfg.setValor("false"));

            jsonParaApp.getConfiguracoes().stream()
                    .filter(cfg -> ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR.name().equals(cfg.getId()))
                    .findFirst()
                    .ifPresent(cfg -> cfg.setValor("false"));

        }

        return jsonParaApp;
    }

}
