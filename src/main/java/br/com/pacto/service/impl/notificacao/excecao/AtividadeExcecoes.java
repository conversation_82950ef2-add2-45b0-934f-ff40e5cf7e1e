package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 22/08/2018.
 */
public enum AtividadeExcecoes implements ExcecaoSistema {

    ATIVIDADE_NAO_ENCONTRADA("atividade_nao_encontrada", "Atividade informada não encontrada"),
    EMPRESA_NAO_ENCONTRADA("empresa_nao_encontrada", "Empresa informada não encontrada"),
    CATEGORIA_NAO_ENCONTRADA("categoria_nao_encontrada", "Categoria Atividade informada não encontrada"),
    APARELHO_NAO_ENCONTRADO("aparelho_nao_encontrado", "Aparelho informado não encontrado"),
    GRUPO_MUSCULAR_NAO_ENCONTRADO("grupo_muscular_nao_encontrado", "Grupo Muscular informado não encontrado"),
    MUSCULO_NAO_ENCONTRADO("musculo_nao_encontrado", "Músculo informado não encontrado"),
    NIVEL_NAO_ENCONTRADO("nivel_nao_encontrado", "Nível informado não encontrado"),
    ERRO_BUSCAR_ATIVIDADES("erro_buscar_atividades", "Ocorreu um erro ao buscar as Atividades"),
    ERRO_BUSCAR_ATIVIDADE("erro_buscar_atividade", "Ocorreu um erro ao buscar a Atividade informada"),
    ERRO_BUSCAR_CATEGORIA_ATIVIDADE("erro_buscar_categoria_atividade", "Ocorreu um erro ao buscar a Categoria de Atividade informada"),
    ERRO_ATUALIZAR_ATIVIDADE("erro_atualizar_atividade", "Ocorreu um erro ao atualizar a Atividade informada"),
    ERRO_ATUALIZAR_CATEGORIA_ATIVIDADE("erro_atualizar_categoria_atividade", "Ocorreu um erro ao atualizar a Categoria de Atividade informada"),
    ERRO_INCLUIR_ATIVIDADE("erro_incluir_atividade", "Ocorreu um erro ao incluir a Atividade informada"),
    ERRO_EXCLUIR_ATIVIDADE("erro_excluir_atividade", "Ocorreu um erro ao excluir a Atividade informada"),
    ERRO_INCLUIR_CATEGORIA_ATIVIDADE("erro_incluir_categoria_atividade", "Ocorreu um erro ao incluir a Categoria de Atividade informada"),
    ERRO_EXCLUIR_CATEGORIA_ATIVIDADE("erro_excluir_categoria_atividade", "Ocorreu um erro ao excluir a Categoria de Atividade informada"),
    VALIDACAO_ATIVIDADE_JA_EXISTE("validacao_atividade_ja_existe", "Categoria de Atividade já existente."),
    VALIDACAO_NOME_OBRIGATORIO("campos_obrigatorios", "Nome da atividade é obrigatório"),
    VALIDACAO_CATEGORIA_OBRIGATORIO("campos_obrigatorios", "Categoria é obrigatório"),
    VALIDACAO_UNIDADE_MEDIDA_OBRIGATORIO("campos_obrigatorios", "Unidade de medida é obrigatório");

    private String chave;
    private String descricao;

    AtividadeExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
