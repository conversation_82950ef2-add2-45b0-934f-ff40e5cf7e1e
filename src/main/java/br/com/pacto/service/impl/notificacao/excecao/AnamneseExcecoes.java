package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 04/08/2018.
 */
public enum AnamneseExcecoes implements ExcecaoSistema{

    ERRO_ALTERAR_ANAMNESE("erro_alterar_anamnese", "Ocorreu um erro ao alterar a anamnese"),
    ANAMNESE_NAO_ENCONTRADA("anamnese_nao_encontrada", "Anamnese informada não foi encontrada"),
    ANAMNESE_PERGUNTA_NAO_ENCONTRADA("anamnese_pergunta_nao_encontrada", "Anamnese Pergunta informada não foi encontrada"),
    ERRO_BUSCAR_ANAMNESE("erro_buscar_anamnese", "Ocorreu um erro ao pesquisar a Anamnese informada"),
    ERRO_BUSCAR_ANAMNESE_PERGUNTA("erro_buscar_anamnese_pergunta", "Ocorreu um erro ao pesquisar a Anamnese Pergunta informada"),
    ERRO_INCLUIR_ANAMNESE("erro_incluir_anamnese", "Ocorreu um erro ao incluir a anamnese informada"),
    ERRO_BUSCAR_ANAMNESES("erro_buscar_anamneses", "Ocorreu um erro ao pesquisar as anamneses"),
    ERRO_EXCLUIR_ANAMNESE("erro_excluir_anamnese", "Ocorreu um erro ao excluir a anamnese informada"),
    ERRO_VALIDACAO_DADOS_ANAMNESE("erro_validacao_dados_anamnese", "Erro de validação de dados."),
    VALIDACAO_DESCRICAO_ANAMNESE("validacao_descricao_anamnese", "Nome da anamnese não informado"),
    VALIDACAO_ANAMNESE_JA_EXISTE("validacao_anamnese_ja_existe", "Já existe uma anamnese cadastrada com este nome");

    private String chave;
    private String descricao;

    AnamneseExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
