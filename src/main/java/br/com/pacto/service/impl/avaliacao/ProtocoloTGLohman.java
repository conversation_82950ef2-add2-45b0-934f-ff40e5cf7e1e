package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by Glauco
 */
public class ProtocoloTGLohman {

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao) {
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        avaliacao.setTotalDobras(Uteis.forcarCasasDecimais(2, new BigDecimal(somatorio(avaliacao.getTriceps(), avaliacao.getCoxaMedial()))));

        Double percGordura = percentualGorduraTGLohman(avaliacao);
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = pesoGorduraTGLohman(avaliacao.getPeso(), percGordura);
        Double massaMagra = massaCorporalMagraTGLohman(avaliacao.getPeso(), massaGorda);


        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));


        return avaliacao;
    }

    public static Double somatorio(double triceps, double coxaMedial) {
        return triceps + coxaMedial;
    }


    /**
     * Método para calcular o percentual de gordura TGLohman
     *
     * @return Percentual de gordura
     */
    public static Double percentualGorduraTGLohman(AvaliacaoFisica avaliacaoFisica) {
        if (avaliacaoFisica.getTotalDobras() > 0) {
            if (avaliacaoFisica.getCliente().isSexoMasculino()) {
                //G% Homens= 0,735 (soma das dobras cutâneas) + 1,0
                return (avaliacaoFisica.getTotalDobras()) * 0.735 + 1;
            } else {
                //G% Mulheres= 0,735 (soma das dobras cutâneas) +5,1
                return (avaliacaoFisica.getTotalDobras()) * 0.735 + 5.1;
            }
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso gordura TGLohman
     *
     * @param peso              - Dado equivalente ao peso
     * @param percentualGordura - Dado equivalente ao Percentual de gordura
     * @return Peso gordura
     */
    public static Double pesoGorduraTGLohman(double peso, Double percentualGordura) {
        // Se peso maior que 0 e percentual de gordura possuir alguma coisa
        if ((peso > 0) && (percentualGordura != 0.0)) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular a Massa corporal magra TGLohman
     *
     * @param peso        - Dado equivalente ao peso
     * @param pesoGordura - Dado equivalente ao peso da gordura
     * @return Massa Corporal Magra
     */
    public static Double massaCorporalMagraTGLohman(double peso, Double pesoGordura) {
        // Se peso maior que 0 e peso de gordura possui alguma coisa
        if ((peso > 0) && (pesoGordura != null)) {
            return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular o Peso Muscular TGLohman
     *
     * @param peso         - Dado equivalente ao peso
     * @param pesoResidual - Dado equivalente ao peso residual
     * @param pesoOsseo    - Dado equivalente ao peso ósseo
     * @param pesoGordura  - Dado equivalente ao peso de gordura
     * @return Peso Muscular
     */
    public static Double pesoMuscularTGLohman(double peso, Double pesoResidual, Double pesoOsseo, Double pesoGordura) {
        // Se peso for maior que 0 e peso residual, peso ósseo e peso de gordura tiver alguma coisa
        if ((peso > 0) && (pesoResidual != null) && (pesoOsseo != null) && (pesoGordura != null)) {
            return Uteis.calculaPesoMuscular(peso, pesoGordura, pesoOsseo, pesoResidual);
        }
        return 0.0;
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
        return a.getTriceps() > 0.0 && a.getCoxaMedial() > 0.0;
    }
}
