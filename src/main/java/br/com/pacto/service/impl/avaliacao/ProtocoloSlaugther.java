package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

public class ProtocoloSlaugther {


    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        avaliacao.setTotalDobras(avaliacao.getTriceps() + avaliacao.getPanturrilha());

        Double percGordura;

        if (avaliacao.getCliente().getSexo().equalsIgnoreCase("M")){
            percGordura = 0.735 * (avaliacao.getTriceps() + avaliacao.getPanturrilha()) + 1.0;
        }else{
            percGordura = 0.61 * (avaliacao.getTriceps() + avaliacao.getPanturrilha()) + 5.1;
        }

        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = (avaliacao.getPeso() * percGordura) /100;
        Double massaMagra = avaliacao.getPeso() - massaGorda;


        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));



        return avaliacao;
    }


    public static boolean isCalcularDados(AvaliacaoFisica a) {
        return a.getTriceps() > 0.0 && a.getPanturrilha() > 0.0;
    }

}
