package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by <PERSON>ao Alcides on 05/06/2017.
 */
public class ProtocoloGuedes3Dobras {



    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }
//        somatorioGuedes(double tricipital, double suprailiaca, double abdominal)
        boolean homem = avaliacao.getCliente().getSexo() == null || avaliacao.getCliente().getSexo().equals("M");
        avaliacao.setTotalDobras(
                Uteis.forcarCasasDecimais(2, new BigDecimal(
                                homem ?
                                somatorioGuedes(avaliacao.getTriceps(), avaliacao.getSupraIliaca(), avaliacao.getAbdominal()) :
                                somatorioGuedes(avaliacao.getSubescapular(), avaliacao.getSupraIliaca(), avaliacao.getCoxaMedial()))));

        Double percGordura = percentualGorduraGuedes(avaliacao.getTotalDobras(), homem);
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = pesoGorduraGuedes(avaliacao.getPeso(), percGordura);
        Double massaMagra = massaCorporalMagraGuedes(avaliacao.getPeso(), massaGorda);

        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));


        return avaliacao;
    }


    /**
     * Método para calcular o somatório de guedes 3 dobras
     *
     * @param tricipital  - Dado equivalente a tricipital
     * @param suprailiaca - Dado equivalente a suprailiaca
     * @param abdominal   - Dado equivalente ao abdominal
     * @return somatório de guedes
     */
    public static Double somatorioGuedes(double tricipital, double suprailiaca, double abdominal) {
        if ((tricipital > 0) && (suprailiaca > 0) && (abdominal > 0)) {
            // Fazendo o cálculo
            return tricipital + suprailiaca + abdominal;
        }
        return 0.0;
    }

    /**
     * Método para calcular o percentual de gordura guedes
     *
     * @param somatorioGuedes - Dado equivalente ao somatório de guedes
     * @return percentual de gordura
     */
    public static Double percentualGorduraGuedes(Double somatorioGuedes, Boolean homem) {
        // Se o somatório possuir alguma coisa
        if (somatorioGuedes != null) {
            double fazerLogaritmo = 
                    homem ?
                    1.17136 - ((Math.log10(somatorioGuedes)) * 0.06706) :
                    1.16650 - ((Math.log10(somatorioGuedes)) * 0.07063) 
                    ;
            return ((4.95 / fazerLogaritmo) - 4.5) * 100;
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso gordura guedes
     *
     * @param percentualGordura - Dado equivalente ao percentual de gordura
     * @param peso              - Dado equivalente ao peso
     * @return peso gordura
     */
    public static Double pesoGorduraGuedes(Double percentualGordura, double peso) {
        // Se percentual de gordura tiver alguma coisa e peso for maior que 0
        if ((percentualGordura != null) && (peso > 0)) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular Massa corporal magra de guedes
     *
     * @param peso        - Dado equivalente ao peso
     * @param pesoGordura - Dado equivalente ao peso de gordura
     * @return massa corporal magra
     */
    public static Double massaCorporalMagraGuedes(double peso, Double pesoGordura) {
        // Se peso for maior que 0 e peso gordura tiver alguma coisa
        if ((peso > 0) && (pesoGordura != null)) {
            return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso muscular de guedes
     *
     * @param peso         - Dado equivalente ao peso
     * @param pesoResidual - Dado equivalente ao peso residual
     * @param pesoOsseo    - Dado equivalente ao peso ósseo
     * @param pesoGordura  - Dado equivalente ao peso de gordura
     * @return Peso Muscular
     */
    public static Double pesoMuscularGuedes(double peso, Double pesoResidual, Double pesoOsseo, Double pesoGordura) {
        // Se peso for maior que 0 e peso residual, peso ósseo e peso gordura possuírem alguma coisa
        if ((peso > 0) && (pesoResidual != null) && (pesoOsseo != null) && (pesoGordura != null)) {
            return Uteis.calculaPesoMuscular(peso, pesoGordura, pesoOsseo, pesoResidual);
        }
        return null;
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
        if (a.getCliente().isSexoMasculino()) {
            return a.getTriceps() > 0.0 && a.getSupraIliaca() > 0.0 && a.getAbdominal() > 0.0;
        } else {
            return a.getSubescapular() > 0.0 && a.getSupraIliaca() > 0.0 && a.getCoxaMedial() > 0.0;
        }
    }
}
