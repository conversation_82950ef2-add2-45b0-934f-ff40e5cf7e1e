package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum ModalidadeExcecoes implements ExcecaoSistema {

    ERRO_INCLUIR_MODALIDADE("erro_incluir_modalidade", "Ocorreu um erro ao incluir a Modalidade informado"),
    ERRO_OBTER_CORES("erro_obter_cores", "Ocorreu um erro ao obter a cores"),
    ERRO_BUSCAR_MODALIDADE("erro_buscar_modalidade", "Ocorreu um erro ao obter a modalidade"),
    ERRO_lISTAR_MODALIDADES("erro_listar_modalidades", "Ocorreu um erro ao listar as modalidades"),
    ERRO_ALTERAR_MODALIDADE("erro_alterar_modalidade", "Ocorreu um erro ao alterar as modalidade"),
    ERRO_EXCLUIR_MODALIDADE("erro_excluir_modalidade", "Ocorreu um erro ao excluir a modalidade"),
    ERRO_ACAO_PROIBIDA("erro_acao_proibida", "Ação não pode ser requirida quando se tem integração com o ZW"),

    ERRO_ID_NAO_INFORMADA("erro_id_nao_informada", "O id da modalidade não foi informado!"),
    ERRO_NOME_NAO_INFORMADO("erro_nome_nao_informado", "O nome da modalidade não foi informado!"),
    ERRO_COR_NAO_INFORMADA("erro_cor_nao_informada", "A cor da modalidade não foi informada!"),
    ERRO_COR_NAO_EXISTE("erro_cor_nao_existe", "A cor informada não existe"),
    ERRO_MODALIDADE_DUPLICADO("erro_modalidade_duplicado", "registro_duplicado"),
    ERRO_MODALIDADE_NAO_EXISTE("erro_modalidade_nao_existe", "A modalidade informada não existe");

    private String chave;
    private String descricao;

    ModalidadeExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
