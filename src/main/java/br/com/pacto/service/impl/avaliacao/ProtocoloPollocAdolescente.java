package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.CategoriaAvaliacaoIMC;
import br.com.pacto.bean.avaliacao.CategoriaPercentualGordura;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.math.BigDecimal;

/**
 * Created by alcides on 24/10/2017.
 */
public class ProtocoloPollocAdolescente {

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        Double massa_gorda;
        Double massa_magra;
        Double perc_gordura;

        Double imc = 0.0;
        if(!UteisValidacao.emptyNumber(avaliacao.getPeso()) && !UteisValidacao.emptyNumber(avaliacao.getAltura())) {
            imc = (avaliacao.getPeso() / (avaliacao.getAltura() * avaliacao.getAltura()));
        }

        Double densidadecorporal = 0.0;
        Double parametros;
        /**
         * Calculando a Densidade corporal Masculina
         *
         * */
        parametros = avaliacao.getCoxaMedial() + avaliacao.getTriceps();


        if (avaliacao.getCliente().getSexo().equalsIgnoreCase("M")){
            densidadecorporal =  Uteis.forcarCasasDecimais(10,new BigDecimal(1.10938-(0.0008267 * (parametros))+(0.0000016 * Math.pow(parametros, 2))
                    - (0.0002574 * avaliacao.getCliente().getIdade())));
        } else {
            densidadecorporal =  Uteis.forcarCasasDecimais(10, new BigDecimal(1.099421 - (0.0009929 * (parametros))+(0.0000023 * Math.pow(parametros, 2))
                    - (0.0001393 * avaliacao.getCliente().getIdade())));
        }

        avaliacao.setTotalDobras(parametros);
        perc_gordura = percGordura(new BigDecimal(densidadecorporal).doubleValue());
        perc_gordura = perc_gordura < 0.0 ? 0.0 : perc_gordura;
        massa_gorda = (avaliacao.getPeso() * perc_gordura) /100;
        massa_magra = avaliacao.getPeso() - massa_gorda;

        avaliacao.setPercentualGordura(perc_gordura);
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massa_gorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massa_magra)));
        avaliacao.setImc(Uteis.forcarCasasDecimais(2, new BigDecimal(imc)));

        return avaliacao;
    }

    public static Double percGordura(Double dc){
        Double perc;

        perc = ((4.95 / dc ) - 4.5) * 100;

        return Uteis.forcarCasasDecimais(2, new BigDecimal(perc));
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
            return a.getCoxaMedial() > 0.0 && a.getTriceps() > 0.0;

    }

}
