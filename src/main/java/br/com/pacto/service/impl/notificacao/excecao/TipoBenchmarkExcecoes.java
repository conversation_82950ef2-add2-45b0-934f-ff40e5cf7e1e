package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 24/08/2018.
 */
public enum TipoBenchmarkExcecoes implements ExcecaoSistema {

    ERRO_ALTERAR_TIPOBENCHMARK("ERRO_ALTERAR_TIPOBENCHMARK", "Ocorreu um erro ao alterar o tipo  Benchmark"),
    TIPOBENCHMARK_NAO_ENCONTRADO("TIPOBENCHMARK_NAO_ENCONTRADO", "Tipo  Benchmark informado não foi encontrado"),
    ERRO_BUSCAR_TIPOBENCHMARK("ERRO_BUSCAR_TIPOBENCHMARK", "Ocorreu um erro ao pesquisar os tipos de Benchmark"),
    ERRO_INCLUIR_TIPOBENCHMARK("ERRO_INCLUIR_TIPOBENCHMARK", "Ocorreu um erro ao incluir o tipo  Benchmark"),
    ERRO_EXCLUIR_TIPOBENCHMARK("ERRO_EXCLUIR_TIPOBENCHMARK", "registro_esta_sendo_usado"),
    VALIDACAO_NOME_TIPOBENCHMARK("validacao_nome_tipobenchmark", "Nome do Benchmark não informado"),
    VALIDACAO_TIPOBENCHMARK_JA_EXISTE("validacao_tipobenchmark_ja_existe", "registro_duplicado"),
    VALIDACAO_ORIGEM_TIPOBENCHMARK("validacao_origem_tipobenchmark", "Origem do Benchmark não informado");



    TipoBenchmarkExcecoes(String chave, String descricao){
        this.chave = chave;
        this.descricao = descricao;
    }

    private String chave;
    private String descricao;

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
