package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 02/08/2018.
 */
public enum GrupoMuscularExcecoes implements ExcecaoSistema {

    ERRO_ALTERAR_GRUPO_MUSCULAR("erro_alterar_grupo_muscular", "Ocorreu um erro ao alterar o grupo muscular"),
    GRUPO_MUSCULAR_NAO_ENCONTRADO("grupo_muscular_nao_encontrado", "Grupo Muscular informado não foi encontrado"),
    ERRO_BUSCAR_GRUPO_MUSCULAR("erro_buscar_grupo_muscular", "Ocorreu um erro ao pesquisar o grupo muscular informado"),
    ERRO_INCLUIR_GRUPO_MUSCULAR("erro_incluir_grupo_muscular", "Ocorreu um erro ao incluir o grupo muscular informado"),
    ERRO_BUSCAR_GRUPOS_MUSCULARES("erro_buscar_grupos_musculares", "Ocorreu um erro ao pesquisar os grupos musculares"),
    ERRO_EXCLUIR_GRUPO_MUSCULAR("erro_excluir_grupo_muscular", "Ocorreu um erro ao excluir o grupo muscular informado"),
    ERRO_VALIDACAO_DADOS_GRUPO_MUSCULAR("erro_validacao_dados_grupo_muscular", "Erro de validação de dados."),
    VALIDACAO_NOME_GRUPO_MUSCULAR("validacao_nome_grupo_muscular", "Nome do grupo muscular não informado"),
    VALIDACAO_GRUPO_MUSCULAR_JA_EXISTE("validacao_grupo_muscular_ja_existe", "Já existe um grupo muscular cadastrado com este nome"),
    ERRO_BUSCAR_GRUPOMUSCULO_MUSCULO("erro_buscar_grupomusculo_musculo", "Ocorreu um erro ao pesquisar o múcusculo do grupo muscular"),
    GRUPOMUSCULO_MUSCULO_NAO_ENCONTRADO("grupomusculo_musculo_nao_encontrado", "Músculo do grupo muscular informado não foi encontrado"),
    ERRO_BUSCAR_GRUPOMUSCULO_ATIVIDADE("erro_buscar_grupomusculo_atividade", "Ocorreu um erro ao pesquisar a atividade do grupo muscular"),
    GRUPOMUSCULO_ATIVIDADE_NAO_ENCONTRADO("grupomusculo_atividade_nao_encontrado", "Atividade do grupo muscular informada não foi encontrada"),
    ERRO_BUSCAR_GRUPOMUSCULO_PERIMETRO("erro_buscar_grupomusculo_perimetro", "Ocorreu um erro ao pesquisar o perímetro do grupo muscular"),
    GRUPOMUSCULO_PERIMETRO_NAO_ENCONTRADO("grupomusculo_perimetro_nao_encontrado", "Perímetro do grupo muscular informado não foi encontrado"),

    ERRO_GRUPOMUSCULO_JA_EXISTE("erro_grupomusculo_ja_existe", "registro_duplicado");

    private String chave;
    private String descricao;

    GrupoMuscularExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
