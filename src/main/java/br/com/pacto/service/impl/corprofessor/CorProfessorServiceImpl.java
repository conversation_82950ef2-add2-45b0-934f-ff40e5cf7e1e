package br.com.pacto.service.impl.corprofessor;

import br.com.pacto.bean.corprofessor.CorProfessor;
import br.com.pacto.dao.intf.corprofessor.CorProfessorDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.corprofessor.CorProfessorService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 18/09/2015.
 */
@Service
public class CorProfessorServiceImpl  implements CorProfessorService{

    @Autowired
    private CorProfessorDao corProfessorDao;

    @Override
    public CorProfessor inserir(String ctx, CorProfessor object) throws ServiceException {
        try {
            return getCorProfessorDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public CorProfessor obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return getCorProfessorDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public CorProfessor alterar(String ctx, CorProfessor object) throws ServiceException {
        try {
            return getCorProfessorDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(String ctx, CorProfessor object) throws ServiceException {
        try {
            getCorProfessorDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CorProfessor> obterTodos(String ctx) throws ServiceException {
        try {
            return getCorProfessorDao().findListByAttributes(ctx, null, null, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CorProfessor> obterPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getCorProfessorDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CorProfessor> obterPorParam(String ctx, String query, Map<String, Object> params, int max, int index) throws ServiceException {
        try {
            return getCorProfessorDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public CorProfessor obterPorProfessor(String ctx, Integer codigoProfessor) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("codigoProfessor", codigoProfessor);
            return obterObjetoPorParam(ctx, "SELECT obj FROM CorProfessor obj where obj.professor.codigo =:codigoProfessor", params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public CorProfessor obterObjetoPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getCorProfessorDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

       public CorProfessorDao getCorProfessorDao() {
        return corProfessorDao;
    }

    public void setCorProfessorDao(CorProfessorDao corProfessorDao) {
        this.corProfessorDao = corProfessorDao;
    }
}
