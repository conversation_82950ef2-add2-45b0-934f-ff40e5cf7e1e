package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;
import br.com.pacto.util.UteisValidacao;

/**
 * Created by paulo on 30/10/2018
 */
public enum TipoEventoExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_TODOS_TIPOS_EVENTO("erro_buscar_todos_tipos_evento", "Erro ao buscar todos os tipos evento"),
    VALIDACAO_JA_EXISTE_UM_TIPO_AGENDAMENTO("validacao_ja_existe_um_tipo_agendamento", "registro_duplicado"),

    ERRO_TENTAR_INCLUIR_TIPO_AGENDAMENTO("erro_tentar_incluir_tipo_agendamento", "Erro ao tentar incluir um tipo de agendamento"),
    ERRO_TENTAR_ATUALIZAR_TIPO_AGENDAMENTO("erro_tentar_atualizar_tipo_agendamento", "Erro ao tentar atualizar um tipo de agendamento"),
    ERRO_EXISTE_AGENDAMENTOS("exite_agendamento_futuros", "registro_esta_sendo_utilizado"),
    ERRO_EXCLUIR_EXISTE_AGENDAMENTOS("erro_excluir_existe_agendamentos", "erro_registro_esta_sendo_utilizado");

    private String chave;
    private String descricao;

    TipoEventoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

    public static TipoEventoExcecoes getFromMessage(Exception ex, TipoEventoExcecoes def){
        try {
            return valueOf(ex.getMessage());
        }catch (Exception e){
            return def;
        }
    }

}
