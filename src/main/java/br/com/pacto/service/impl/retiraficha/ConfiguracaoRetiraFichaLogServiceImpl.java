package br.com.pacto.service.impl.retiraficha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.retiraFicha.ConfiguracaoRetiraFichaJson;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaLogDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.retiraFicha.ConfiguracaoRetiraFichaLogService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.json.JSONObject;
import org.apache.poi.ss.formula.functions.Now;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Service
public class ConfiguracaoRetiraFichaLogServiceImpl  implements ConfiguracaoRetiraFichaLogService {

    @Autowired
    private ConfiguracaoRetiraFichaLogDao configuracaoRetiraFichaLogDao;

    @Autowired
    private EmpresaService empresaService;

    @Autowired
    private UsuarioService usuarioService;

    @Override
    public List<ConfiguracaoRetiraFichaLog> obterTodos(String ctx) throws ServiceException {
        try {
            return configuracaoRetiraFichaLogDao.findListByAttributes(ctx, null, null, "codigo", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void cadastrarLogConfiguracaoRetiraFicha(String ctx, ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson, Integer codigoUsuario) throws ServiceException {
        try {
            Usuario usuario;
            try {
                usuario = usuarioService.obterPorId(ctx,codigoUsuario);
            }catch (Exception e){
                throw new Exception(e);
            }

            ConfiguracaoRetiraFichaLog configuracaoRetiraFichaLog = new ConfiguracaoRetiraFichaLog(usuario,configuracaoRetiraFichaJson);
            configuracaoRetiraFichaLogDao.insert(ctx, configuracaoRetiraFichaLog);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ConfiguracaoRetiraFichaLog> listarLogsAlteracoes(String ctx, Integer codigoConfiguracaoRetiraFicha) throws Exception {
        List<ConfiguracaoRetiraFichaLog> listaConfiguracaoRetiraFichaLog =     configuracaoRetiraFichaLogDao.listarLogsAlteracoes(ctx, codigoConfiguracaoRetiraFicha);
        return listaConfiguracaoRetiraFichaLog;
    }
}
