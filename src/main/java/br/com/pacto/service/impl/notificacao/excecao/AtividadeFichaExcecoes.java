package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 15/08/2018.
 */
public enum AtividadeFichaExcecoes implements ExcecaoSistema {

    ATIVIDADE_NAO_ENCONTRADA("atividade_nao_encontrada", "Atividade informada não encontrada"),
    FICHA_NAO_ENCONTRADA("ficha_nao_encontrada", "Ficha informada não encontrada"),
    ERRO_INCLUIR_ATIVIDADE_FICHA("erro_incluir_atividade_ficha", "Ocorreu um erro ao incluir a Atividade Ficha informada"),
    ERRO_ATUALIZAR_ATIVIDADE_FICHA("erro_atualizar_atividade_ficha", "Ocorreu um erro ao atualizar a Atividade Ficha informada"),
    ERRO_EXCLUIR_ATIVIDADE_FICHA("erro_excluir_atividade_ficha", "Ocorreu um erro ao excluir a Atividade Ficha informada"),
    ATIVIDADE_FICHA_NAO_ENCONTRADA("atividade_ficha_nao_encontrada", "Atividade Ficha informada não encontrada"),
    ERRO_BUSCAR_ATIVIDADES_FICHA("erro_buscar_atividades_ficha", "Erro ao buscar atividades ficha"),
    ERRO_PADRONIZAR_SERIES("erro_padronizar_series", "Erro ao pradronizar as series, das atividades da ficha")
    ;

    /*
    PROGRAMA_TREINO_NAO_ENCONTRADO("programa_treino_nao_encontrado", "Programa Treino informado não foi encontrado"),
    ERRO_BUSCAR_PROGRAMA_TREINO("erro_buscar_programa_treino", "Ocorreu um erro ao pesquisar o Programa Treino informado"),
    ERRO_BUSCAR_PROGRAMAS_TREINO_PREDEFINIDOS("erro_buscar_programas_treino_predefinidos", "Ocorreu um erro ao pesquisar os Programas Treino Pré-definidos"),
    ERRO_MONTAR_PROGRAMA_TREINO("erro_montar_programa_treino", "Ocorreu um erro ao montar o Programa Treino informado"),
    ERRO_BUSCAR_ALUNO("erro_buscar_aluno", "Ocorreu um erro ao pesquisar o Aluno informado"),
    ERRO_BUSCAR_PROFESSOR("erro_buscar_professor", "Ocorreu um erro ao pesquisar o Professor informado"),
    ERRO_INCLUIR_PROGRAMA_TREINO("erro_incluir_programa_treino", "Ocorreu um erro ao incluir o Programa Treino informado"),
    ERRO_ALTERAR_PROGRAMA_TREINO("erro_alterar_programa_treino", "Ocorreu um erro ao alterar o Programa Treino"),
    ERRO_COLOCAR_PROGRAMA_TREINO_COMO_PREDEFINIDO("erro_colocar_programa_treino_como_predefinido", "Ocorreu um erro ao colocar o Programa Treino como Pré-definido"),
    ERRO_EXCLUIR_PROGRAMA_TREINO("erro_excluir_programa_treino", "Ocorreu um erro ao excluir o Programa Treino informado");
    */

    private String chave;
    private String descricao;

    AtividadeFichaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
