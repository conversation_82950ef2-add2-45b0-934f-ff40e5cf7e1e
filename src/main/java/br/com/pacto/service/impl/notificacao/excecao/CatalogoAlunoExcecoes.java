package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 04/08/2018.
 */
public enum CatalogoAlunoExcecoes implements ExcecaoSistema {

    PROFESSOR_NAO_ENCONTRADO("professor_nao_encontrado", "Professor informado não foi encontrado"),
    ERRO_CONSULTAR_CATALOGO_ALUNOS("erro_consultar_catalogo_alunos", "Erro ao consultar o Catálogo de Alunos");

    private String chave;
    private String descricao;

    CatalogoAlunoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
