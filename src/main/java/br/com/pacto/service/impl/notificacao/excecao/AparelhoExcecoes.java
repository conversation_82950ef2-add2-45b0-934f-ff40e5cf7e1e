package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 02/08/2018.
 */
public enum AparelhoExcecoes implements ExcecaoSistema {

    ERRO_ALTERAR_APARELHO("erro_alterar_aparelho", "Ocorreu um erro ao alterar o aparelho"),
    APARELHO_NAO_ENCONTRADO("aparelho_nao_encontrado", "Aparelho informado não foi encontrado"),
    APARELHO_AJUSTE_NAO_ENCONTRADO("aparelho_ajuste_nao_encontrado", "Aparelho Ajuste informado não foi encontrado"),
    ATIVIDADE_NAO_ENCONTRADA("atividade_nao_encontrada", "Atividade informada não foi encontrada"),
    ERRO_BUSCAR_APARELHO("erro_buscar_aparelho", "Ocorreu um erro ao pesquisar o Aparelho informado"),
    ERRO_BUSCAR_APARELHO_AJUSTE("erro_buscar_aparelho_ajuste", "Ocorreu um erro ao pesquisar o Aparelho Ajuste informado"),
    ERRO_BUSCAR_ATIVIDADE("erro_buscar_atividade", "Ocorreu um erro ao pesquisar a Atividade informada"),
    ERRO_INCLUIR_APARELHO("erro_incluir_aparelho", "Ocorreu um erro ao incluir o aparelho informado"),
    ERRO_BUSCAR_APARELHOS("erro_buscar_aparelhos", "Ocorreu um erro ao pesquisar os aparelhos"),
    ERRO_BUSCAR_APARELHOS_RESERVA_EQUIPAMENTO("erro_buscar_aparelhos_reserva_equipamento", "Ocorreu um erro ao pesquisar os aparelhos habilitados para reserva de equipamento"),
    ERRO_EXCLUIR_APARELHO("erro_excluir_aparelho", "Ocorreu um erro ao excluir o aparelho informado"),
    ERRO_VALIDACAO_DADOS_APARELHO("erro_validacao_dados_aparelho", "Erro de validação de dados."),
    VALIDACAO_NOME_APARELHO("validacao_nome_aparelho", "Nome do aparelho não informado"),
    VALIDACAO_APARELHO_JA_EXISTE("validacao_aparelho_ja_existe", "registro_duplicado"),
    VALIDACAO_APARELHO_VINCULADO_RESERVA_EQUIPAMENTO("validacao_aparelho_vinculado_reserva_equipamento", "O aparelho não pode ser removido pois está vinculado ao mapa de reserva de equipamento");

    private String chave;
    private String descricao;

    AparelhoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
