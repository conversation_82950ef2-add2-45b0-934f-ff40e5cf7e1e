package br.com.pacto.service.impl.ambiente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.ambiente.*;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.aula.AmbienteDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AmbienteExcecoes;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AmbienteServiceImpl implements AmbienteService {


    @Autowired
    private AmbienteDao ambienteDao;

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private AulaService aulaService;

    @Autowired
    private EmpresaService empresaService;

    public AmbienteDao getAmbienteDao() {
        return ambienteDao;
    }

    @Override
    public Ambiente inserir(String ctx, Ambiente object) throws ServiceException {
        try {
            return getAmbienteDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    @Override
    public Ambiente consultarPorAmbiente(String ctx, Integer id) throws ServiceException {
        try {
            return getAmbienteDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Ambiente consultarPorAmbienteZW(String ctx, Integer codigoZW) throws ServiceException {
        try {
            return getAmbienteDao().findObjectByAttribute(ctx, "codigoZW", codigoZW);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Ambiente alterar(String ctx, Ambiente ambiente) throws ServiceException {
        try {
            return getAmbienteDao().update(ctx, ambiente);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void remover(String ctx, Ambiente ambiente) throws ServiceException {
        try {
            getAmbienteDao().delete(ctx, ambiente);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Ambiente> consultarPorNome(String ctx, FiltroAmbienteJSON filtroAmbienteJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {

            return getAmbienteDao().consultarPorNome(ctx, filtroAmbienteJSON, paginadorDTO);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AmbienteResponseTO cadastroAmbiente(AmbienteTO ambienteTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (SuperControle.independente(ctx)) {
                camposObrigatorios(ambienteTO);
                validarCampos(ambienteTO);
                Ambiente ambiente = hidratarModalidade(ambienteTO, new Ambiente());
                validarAmbiente(ctx, ambiente);
                inserir(ctx, ambiente);

                return new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), false);
            } else {
                throw new ServiceException(AmbienteExcecoes.ERRO_ACAO_PROIBIDA);
            }
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_INCLUIR_AMBIENTE, e);
        }
    }

    private void validarAmbiente(String ctx, Ambiente ambiente) throws ServiceException {
        if (getAmbienteDao().exists(ctx, ambiente, "nome")) {
            throw new ServiceException(AmbienteExcecoes.ERRO_AMBIENTE_JA_EXISTE);
        }
    }

    @Override
    public List<AmbienteResponseTO> listaAmbientes(FiltroAmbienteJSON filtroAmbienteJSON, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            Empresa empresaZW = empresaService.obterPorIdZW(ctx, empresaId);
            List <Ambiente> lista = new ArrayList<>();

            List<AmbienteResponseTO> listaRet = new ArrayList<>();

            if (SuperControle.independente(ctx)) {
                lista = consultarPorNome(ctx, filtroAmbienteJSON, paginadorDTO);
            } else {
                aulaService.obterAmbientesZW(ctx, empresaZW.getCodZW());
                lista = consultarPorNome(ctx, filtroAmbienteJSON, paginadorDTO);
            }

            if (!UteisValidacao.emptyList(lista)) {
                for (Ambiente ambiente : lista) {
                    listaRet.add(new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), false));
                }
            }

            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_LISTAR_AMBIENTES, e);
        }
    }




        @Override
    public AmbienteResponseTO alterarAmbiente(Integer id, AmbienteTO ambienteTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (SuperControle.independente(ctx)) {
                camposObrigatorios(ambienteTO);
                Ambiente ambiente = validarID(ctx, id);
                validarCampos(ambienteTO);
                hidratarModalidade(ambienteTO, ambiente);
                validarAmbiente(ctx, ambiente);
                alterar(ctx, ambiente);

                return new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), false);
            } else {
                throw new ServiceException(AmbienteExcecoes.ERRO_ACAO_PROIBIDA);
            }
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_ALTERAR_AMBIENTE, e);
        }
    }

    @Override
    public void removerAmbiente(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (SuperControle.independente(ctx)) {
                Ambiente ambiente = validarID(ctx, id);
                remover(ctx, ambiente);
            } else {
                throw new ServiceException(AmbienteExcecoes.ERRO_ACAO_PROIBIDA);
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_ALTERAR_AMBIENTE, e);
        }
    }

    @Override
    public List<TipoAmbienteResponseTO> obterTodosTiposAmbiente() throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return ambienteDao.obterTodosTiposAmbiente(ctx);
    }

    @Override
    public List<NivelTurmaResponseTO> obterNiveisTurmaAmbiente() throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return ambienteDao.obterNiveisTurmaAmbiente(ctx);
    }

    @Override
    public List<ColetorResponseTO> obterColetoresAmbiente() throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return ambienteDao.obterColetoresAmbiente(ctx);
    }

    private Ambiente validarID(String ctx, Integer id) throws ServiceException {
        if (id == null || id < 1) {
            throw new ServiceException(AmbienteExcecoes.ERRO_ID_NAO_INFORMADA);
        }
        Ambiente ambiente = consultarPorAmbiente(ctx, id);
        if (ambiente == null) {
            throw new ServiceException(AmbienteExcecoes.ERRO_AMBIENTE_NAO_EXISTE);
        }
        return ambiente;
    }

    private Ambiente hidratarModalidade(AmbienteTO ambienteTO, Ambiente ambiente) {
        ambiente.setNome(ambienteTO.getNome());
        ambiente.setCapacidade(ambienteTO.getCapacidade());
        return ambiente;
    }

    private void validarCampos(AmbienteTO ambienteTO) throws ServiceException {
        if (ambienteTO.getCapacidade() < 1) {
            throw new ServiceException(AmbienteExcecoes.ERRO_CAPACIDADE_INVALIDA);
        }
    }

    private void camposObrigatorios(AmbienteTO ambienteTO) throws ServiceException {
        if (ambienteTO.getNome() == null || ambienteTO.getNome().trim().isEmpty()) {
            throw new ServiceException(AmbienteExcecoes.ERRO_NOME_NAO_INFORMADA);
        }
        if (ambienteTO.getCapacidade() == null) {
            throw new ServiceException(AmbienteExcecoes.ERRO_CAPACIDADE_NAO_INFORMADA);
        }
    }

    @Override
    public List<AmbienteResponseTO> obterTodosAtivos(FiltroAmbienteJSON filtros) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return ambienteDao.obterTodosAtivo(ctx, filtros);
    }

    @Override
    public List<AmbienteResponseTO> obterTodos(Integer empresaId, Boolean validarNoTreino) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Empresa empresaZW = empresaService.obterPorIdZW(ctx, empresaId);
            List<Ambiente> ambientes;
            if (SuperControle.independente(ctx)) {
                ambientes = ambienteDao.findAll(ctx);
            } else {
                ambientes = aulaService.obterAmbientesZW(ctx, empresaZW.getCodZW());
            }
            List<AmbienteResponseTO> listaRet = new ArrayList<>();

            if (!UteisValidacao.emptyList(ambientes)) {
                for (Ambiente ambiente : ambientes) {
                    listaRet.add(new AmbienteResponseTO(ambiente, SuperControle.independente(ctx), validarNoTreino));
                }
            }
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_LISTAR_AMBIENTES, e);
        }
    }

}
