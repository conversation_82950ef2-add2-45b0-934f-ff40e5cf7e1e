/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class ProtocoloPollock7Dobras {
    
    
    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        avaliacao.setTotalDobras(Uteis.forcarCasasDecimais(2, new BigDecimal(
                somatorioPollockSeteDobras(avaliacao.getTriceps(), avaliacao.getSubescapular(),
                avaliacao.getAxilarMedia(), avaliacao.getSupraIliaca(),
                                             avaliacao.getAbdominal(),
                                             avaliacao.getCoxaMedial(),
                                             avaliacao.getPeitoral()))));
        
        Double percGordura = percentualGorduraPollockSete(avaliacao.getCliente().getIdade(), avaliacao.getTotalDobras(),
                avaliacao.getCliente().getSexo() == null
        || avaliacao.getCliente().getSexo().equals("M"));
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = pesoGorduraPollockSete(avaliacao.getPeso(), percGordura);
        Double massaMagra = massaCorporalMagraPollockSete(avaliacao.getPeso(), massaGorda);


        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));


        return avaliacao;
    }
    
    /**
     * Método para calcular o somatório de Pollock sete dobras que sera usado no decorrer da aplicação
     *
     * @param tricipital   - Dado equivalente ao tricipital
     * @param subescapular - Dado equivalente a subescapular
     * @param axilarMedia  - Dado equivalente a axilar media
     * @param suprailiaca  - Dado equivalente a suprailica
     * @param abdominal    - Dado equivalente ao abdominal
     * @param coxaPollock  - Dado equivalente a coxa pollock
     * @param peitoral     - Dado equivalente ao peitoral
     * @return somatorio de pollock 7 dobras
     */
    public static Double somatorioPollockSeteDobras(double tricipital, double subescapular, double axilarMedia, double suprailiaca,
                                             double abdominal, double coxaPollock, double peitoral) {
        /*
         * se tricipital, subescapular, axilar media, suprailiaca, abdominal,
		 * coxa Pollock e peitoral forem maiores que 0
		 */
        if (tricipital > 0 && subescapular > 0 && axilarMedia > 0 && suprailiaca > 0 && abdominal > 0
                && coxaPollock > 0 && peitoral > 0) {
            // Fazendo o calculo
            return tricipital + peitoral + subescapular + axilarMedia + suprailiaca + abdominal + coxaPollock;
        }
        return 0.0;
    }

    /**
     * Método para calcular Percentual Gordura Pollock sete
     *
     * @param idade                - Dado equivalente a idade
     * @param somatorioPollockSete - Dado equivalente ao somatorio pollock sete
     * @return Percentual Gordura
     */
    public static Double percentualGorduraPollockSete(double idade, Double somatorioPollockSete, boolean homem) {
        // se idade for maior que 0 e somatório tiver alguma coisa
        if (idade > 0 && somatorioPollockSete != null) {
            
            
            return homem ? 
                    //homem
                    ((4.95 / ((1.112 - (0.00043499 * somatorioPollockSete)
                    + (0.00000055 * Math.pow(somatorioPollockSete, 2)) - (0.0002882 * idade))) - 4.5) * 100) :
                    //mulher
                    ((4.95 / ((1.0970 - (0.00046971 * somatorioPollockSete)
                    + (0.00000056 * Math.pow(somatorioPollockSete, 2)) - (0.00012828 * idade))) - 4.5) * 100)                   
                    ;
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso gordura Pollock sete
     *
     * @param peso              - Dado equivalente ao peso
     * @param percentualGordura - Dado equivalente ao percentual de gordura
     * @return peso gordura
     */
    public static Double pesoGorduraPollockSete(double peso, Double percentualGordura) {
        // se peso for maior que 0 e percentual de gordura tiver alguma coisa
        if (peso > 0 && percentualGordura != null) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular a massa corporal magra Pollock sete
     *
     * @param peso        - Dado equivalente ao peso
     * @param pesoGordura - Dado equivalente ao peso de gordura
     * @return massa corporal magra
     */
    public static Double massaCorporalMagraPollockSete(double peso, Double pesoGordura) {
        // se peso for maior que 0 e peso de gordura tiver alguma coisa
        return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
    }

    /**
     * Método para calcular o peso muscular Pollock sete
     *
     * @param peso         - Dado equivalente ao peso
     * @param pesoResidual - Dado equivalente ao peso residual
     * @param pesoOsseo    - Dado equivalente ao peso osseo
     * @param pesoGordura  - Dado equivalente ao peso de gordura
     * @return peso muscular
     */
    public Double pesoMuscularPollockSete(double peso, Double pesoResidual, Double pesoOsseo, Double pesoGordura) {
        /*
		 * Se peso for maior que 0 e peso residual, peso ósseo, peso gordura tiverem alguma coisa
		 */
        if (peso > 0 && pesoResidual != null && pesoOsseo != null && pesoGordura != null) {
            return Uteis.calculaPesoMuscular(peso, pesoGordura, pesoOsseo, pesoResidual);
        }
        return 0.0;
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
        return a.getAbdominal() > 0.0 && a.getCoxaMedial() > 0.0 && a.getPeitoral() > 0.0 &&
                a.getTriceps() > 0.0 && a.getAxilarMedia() > 0.0 && a.getSupraIliaca() > 0.0 &&
                a.getSubescapular() > 0.0;
    }
}
