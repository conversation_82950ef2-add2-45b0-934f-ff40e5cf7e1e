package br.com.pacto.service.impl.benchmark;

import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.benchmark.OrigemTipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.service.intf.benchmark.BenchmarkService;
import br.com.pacto.service.intf.benchmark.TipoBenchmarkService;
import br.com.pacto.util.UtilContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Rafael on 20/07/2016.
 */
public class BaseBenchmark {

    public static void montarBaseDadosBenchMark(String ctx) throws Exception{

        if(baseJaAdicionada(ctx)){
            return;
        }
        List<Benchmark> benchs1 = new ArrayList<Benchmark>();
        TipoBenchmark tipoBench1 = new TipoBenchmark("The Benchmark Girls");
        benchs1.add(inicilizarBench("Angie","100 Pull-ups,100 Push-ups,100 Sit-ups,100 Squats",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Barbara","20 Pull-ups,30 Push-ups,40 Sit-ups,50 Squats",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Chelsea","5 Pull-ups,10 Push-ups,15 Squats",TipoWodEnum.AMRAP));
        benchs1.add(inicilizarBench("Cindy","5 Pull-ups,10 Push-ups,15 Squats",TipoWodEnum.AMRAP));
        benchs1.add(inicilizarBench("Diane","Deadlift 225 lbs,Handstand push-ups",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Elizabeth","Clean 135 lbs,Ring Dips",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Fran","Thruster 95 lbs,Pull-ups",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Grace","Clean and Jerk 135 lbs",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Helen","400 meter run,1.5 pood Kettlebell swing x 21,Pull-ups 12 reps",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Isabel","Snatch 135 pounds",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Jackie","1000 meter row,Thruster 45 lbs (50 reps),Pull-ups (30 reps)",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Karen","Wall-ball 150 shots",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Linda","Deadlift 1 1/2 BW,Bench BW,Clean 3/4 BW",TipoWodEnum.FOR_TIME));
        benchs1.add(inicilizarBench("Mary","5 Handstand push-ups,10 1-legged squats,15 Pull-ups",TipoWodEnum.AMRAP));
        benchs1.add(inicilizarBench("Nancy","400 meter run,Overhead squat 95 lbs x 15",TipoWodEnum.FOR_TIME));
        salvarTipoBench(ctx,tipoBench1);
        salvarBenchs(ctx,benchs1,tipoBench1);

        List<Benchmark> benchs2 =  new ArrayList<Benchmark>();
        TipoBenchmark tipoBench2 = new TipoBenchmark("The New Girls");
        benchs2.add(inicilizarBench("Annie","Double-unders,Sit-ups","50-40-30-20 and 10 rep rounds; for time"));
        benchs2.add(inicilizarBench("Eva","Run 800 meters,2 pood KB swing, 30 reps,30 pullups","5 rounds for time."));
        benchs2.add(inicilizarBench("Kelly","Run 400 meters,30 box jump, 24 inch box,30 Wall ball shots, 20 pound ball","Five rounds for time"));
        benchs2.add(inicilizarBench("Lynne","Bodyweight bench press (e.g., same amount on bar as you weigh),pullups",TipoWodEnum.FOR_TIME));
        benchs2.add(inicilizarBench("Nicole","Run 400 meters,Max rep Pull-ups",TipoWodEnum.AMRAP));
        salvarTipoBench(ctx,tipoBench2);
        salvarBenchs(ctx,benchs1,tipoBench2);

        List<Benchmark> benchs4 =  new ArrayList<Benchmark>();
        TipoBenchmark tipoBench4 = new TipoBenchmark("The Hero Workouts");
        benchs4.add(inicilizarBench("JT","Handstand push-ups,Ring dips,Push-ups","21-15-9 reps, for time"));
        benchs4.add(inicilizarBench("MICHAEL","Run 800 meters,50 Back Extensions,50 Sit-ups","3 rounds for time"));
        benchs4.add(inicilizarBench("MURPH ","1 mile Run,100 Pull-ups,200 Push-ups,300 Squats,1 mile Run","For time.,Partition the pull-ups, push-ups, and squats as needed. Start and finish with a mile run. If you?ve got a twenty pound vest or body armor, wear it."));
        benchs4.add(inicilizarBench("DANIEL","50 Pull-ups,400 meter run,95 pound Thruster, 21 reps,800 meter run,95 pound Thruster, 21 reps,400 meter run,50 Pull-ups","For time."));
        benchs4.add(inicilizarBench("JOSH","95 pound Overhead squat, 21 reps,42 Pull-ups,95 pound Overhead squat, 15 reps,30 Pull-ups,95 pound Overhead squat, 9 reps,18 Pull-ups","For time."));
        benchs4.add(inicilizarBench("JASON","100 Squats,5 Muscle-ups,75 Squats,10 Muscle-ups,50 Squats,15 Muscle-ups,25 Squats,20 Muscle-ups","For time."));
        benchs4.add(inicilizarBench("BADGER","95 pound Squat clean, 30 reps,30 Pull-ups,Run 800 meters","3 rounds for time."));
        benchs4.add(inicilizarBench("JOSHIE","40 pound Dumbbell snatch, 21 reps, right arm,21 L Pull-ups,40 pound Dumbbell snatch, 21 reps, left arm,21 L Pull-ups","3 rounds for time."));
        benchs4.add(inicilizarBench("NATE","2 Muscle-ups,4 Handstand Push-ups,8 2-Pood Kettlebell swings","As many rounds as possible in 20 minutes"));
        benchs4.add(inicilizarBench("RANDY","75# power snatch, 75 reps","For time"));
        benchs4.add(inicilizarBench("TOMMY V","115 pound Thruster, 21 reps,15 ft Rope Climb, 12 ascents,115 pound Thruster, 15 reps,15 ft Rope Climb, 9 ascents,115 pound Thruster, 9 reps,15 ft Rope Climb, 6 ascents","For time"));
        benchs4.add(inicilizarBench("GRIFF","Run 800 meters,Run 400 meters backwards,Run 800 meters,Run 400 meters backwards","For time"));
        benchs4.add(inicilizarBench("RYAN","7 Muscle-ups,21 Burpees","For time"));
        benchs4.add(inicilizarBench("ERIN","40 pound Dumbbells split clean, 15 reps,21 Pull-ups","For time"));
        benchs4.add(inicilizarBench("MR. JOSHUA","Run 400 meters,30 Glute-ham sit-ups,250 pound Deadlift, 15 reps","For time"));
        benchs4.add(inicilizarBench("DT","155 pound Deadlift, 12 reps,155 pound Hang power clean, 9 reps,155 pound Push jerk, 6 reps","For time"));
        benchs4.add(inicilizarBench("DANNY","24? box jump, 30 reps,115 pound push press, 20 reps,30 pull-ups",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("HANSEN","30 reps, 2 pood Kettlebell swing,30 Burpees,30 Glute-ham sit-ups","For time"));
        benchs4.add(inicilizarBench("TYLER","7 Muscle-ups,21 reps 95 pound Sumo-deadlift high-pull","For time"));
        benchs4.add(inicilizarBench("LUMBERJACK 20","20 Deadlifts (275lbs),Run 400m,20 KB swings (2pood),Run 400m,20 Overhead Squats (115lbs),Run 400m,20 Burpees,Run 400m," +
                "20 Pullups (Chest to Bar),Run 400m,20 Box jumps (24″),Run 400m,20 DB Squat Cleans (45lbs each),Run 400m","For time"));
        benchs4.add(inicilizarBench("STEPHEN","GHD sit-up,Back extension,Knees to elbow,95 pound Stiff legged deadlift","For time"));
        benchs4.add(inicilizarBench("GARRETT","75 Squats,25 Ring handstand push-ups,25 L-pull-ups","For time"));
        benchs4.add(inicilizarBench("WAR FRANK","25 Muscle-ups,100 Squats,35 GHD situps","For time"));
        benchs4.add(inicilizarBench("MCGHEE","275 pound Deadlift, 5 reps,13 Push-ups,9 Box jumps, 24 inch box",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("PAUL","50 Double unders,35 Knees to elbows,185 pound Overhead walk, 20 yards","For Time"));
        benchs4.add(inicilizarBench("JERRY","Run 1 mile,Row 2K,Run 1 mile","For Time"));
        benchs4.add(inicilizarBench("NUTTS","10 Handstand push-ups,250 pound Deadlift, 15 reps,25 Box jumps, 30 inch box,50 Pull-ups,100 Wallball shots, 20 pounds, 10',200 Double-unders,Run 400 meters with a 45lb plate","For Time"));
        benchs4.add(inicilizarBench("ARNIE","21 Turkish get-ups, Right arm,50 Swings,21 Overhead squats, Left arm,50 Swings,21 Overhead squats, Right arm,50 Swings,21 Turkish get-ups, Left arm","For Time"));
        benchs4.add(inicilizarBench("THE SEVEN","7 Handstand push-ups,135 pound Thruster, 7 reps,7 Knees to elbows,245 pound Deadlift, 7 reps,7 Burpees,7 Kettlebell swings, 2 pood,7 Pull-ups","For Time"));
        benchs4.add(inicilizarBench("RJ","Run 800 meters,15 ft Rope Climb, 5 ascents,50 Push-ups","For Time"));
        benchs4.add(inicilizarBench("LUCE","Run 1K,10 Muscle-ups,100 Squats","For Time"));
        benchs4.add(inicilizarBench("JOHNSON","245 pound Deadlift, 9 reps,8 Muscle-ups,155 pound Squat clean, 9 reps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("ROY","225 pound Deadlift, 15 reps,20 Box jumps, 24 inch box,25 Pull-ups","For Time"));
        benchs4.add(inicilizarBench("ADAMBROWN","295 pound Deadlift, 24 reps,24 Box jumps, 24 inch box,24 Wallball shots, 20 pound ball,195 pound Bench press, 24 reps,24 Box jumps, 24 inch box,24 Wallball shots, 20 pound ball,145 pound Clean, 24 reps","For Time"));
        benchs4.add(inicilizarBench("COE","95 pound Thruster, 10 reps,10 Ring push-ups","For Time"));
        benchs4.add(inicilizarBench("SEVERIN","50 Strict Pull-ups,100 Push-ups, release hands from floor at the bottom,Run 5K,If you’ve got a twenty pound vest or body armor, wear it.","For Time"));
        benchs4.add(inicilizarBench("HELTON","Three rounds of:Run 800 meters,30 reps, 50 pound dumbbell squat cleans,30 Burpees","For Time"));
        benchs4.add(inicilizarBench("JACK","115 pound Push press, 10 reps,10 KB Swings, 1.5 pood,10 Box jumps, 24 inch box",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("FORREST","20 L-pull-ups,30 Toes to bar,40 Burpees,Run 800 meters","For Time"));
        benchs4.add(inicilizarBench("BULGER","Run 150 meters,7 Chest to bar pull-ups,135 pound Front squat, 7 reps,7 Handstand push-ups","For Time"));
        benchs4.add(inicilizarBench("BRENTON","Five rounds of:Bear crawl 100 feet,Standing broad-jump, 100 feetDo three Burpees after every five broad-jumps. If you’ve got a twenty pound vest or body armor, wear it.","For Time"));
        benchs4.add(inicilizarBench("BLAKE","100 foot Walking lunge with 45lb plate held overhead,30 Box jump, 24 inch box,20 Wallball shots, 20 pound ball,10 Handstand push-ups","For Time"));
        benchs4.add(inicilizarBench("COLLIN","Carry 50 pound sandbag 400 meters,115 pound Push press, 12 reps,12 Box jumps, 24 inch box,95 pound Sumo deadlift high-pull, 12 reps","For Time"));
        benchs4.add(inicilizarBench("THOMPSON","15 ft Rope Climb, 1 ascent,95 pound Back squat, 29 reps,135 pound barbells Farmer carry, 10 meters","For Time"));
        benchs4.add(inicilizarBench("WHITTEN","22 Kettlebell swings, 2 pood,22 Box jump, 24 inch box,Run 400 meters,22 Burpees,22 Wall ball shots, 20 pound ball","For Time"));
        benchs4.add(inicilizarBench("BULL","200 Double-unders,135 pound Overhead squat, 50 reps,50 Pull-ups,Run 1 mile","For Time"));
        benchs4.add(inicilizarBench("RANKEL","225 pound Deadlift, 6 reps,7 Burpee pull-ups,10 Kettlebell swings, 2 pood,Run 200 meters","AMRAP, 20 Minutes"));
        benchs4.add(inicilizarBench("HOLBROOK","115 pound Thruster, 5 reps,10 Pull-ups,100 meter Sprint,Rest 1 minute","Each Round For Time"));
        benchs4.add(inicilizarBench("LEDESMA","5 Parallette handstand push-ups,10 Toes through rings,20 pound Medicine ball cleans, 15 reps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("WITTMAN","1.5 pood Kettlebell swing, 15 reps,95 pound Power clean, 15 reps,15 Box jumps, 24? box","For Time"));
        benchs4.add(inicilizarBench("MCCLUSKEY","9 Muscle-ups,15 Burpee pull-ups,21 Pull-ups,Run 800 meters","For Time"));
        benchs4.add(inicilizarBench("WEAVER","10 L-pull-ups,15 Push-ups,15 Chest to bar Pull-ups,15 Push-ups,20 Pull-ups,15 Push-ups","For Time"));
        benchs4.add(inicilizarBench("ABBATE","Run 1 mile,155 pound Clean and jerk, 21 reps,Run 800 meters,155 pound Clean and jerk, 21 reps,Run 1 Mile","For Time"));
        benchs4.add(inicilizarBench("HAMMER","135 pound Power clean, 5 reps,135 pound Front squat, 10 reps,135 pound Jerk, 5 reps,20 Pull-ups","Each Round For Time"));
        benchs4.add(inicilizarBench("MOORE","15 ft Rope Climb, 1 ascent,Run 400 meters,Max rep Handstand push-ups",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("WILMOT","50 Squats,25 Ring dips","For Time"));
        benchs4.add(inicilizarBench("MOON","40 pound dumbbell Hang split snatch, 10 reps Right arm,15 ft Rope Climb, 1 ascent,40 pound dumbbell Hang split snatch, 10 reps Left arm,15 ft Rope Climb, 1 ascent","For Time"));
        benchs4.add(inicilizarBench("SMALL","Row 1000 meters,50 Burpees,50 Box jumps, 24? box,Run 800 meters","For Time"));
        benchs4.add(inicilizarBench("MORRISON","Wall ball shots, 20 pound ball,Box jump, 24 inch box,Kettlebell swings, 1.5 pood","For Time"));
        benchs4.add(inicilizarBench("MANION","Run 400 meters,135 pound Back squat, 29 reps","For Time"));
        benchs4.add(inicilizarBench("GATOR","185 pound Front squat, 5 reps,26 Ring push-ups","For Time"));
        benchs4.add(inicilizarBench("BRADLEY","Sprint 100 meters,10 Pull-ups,Sprint 100 meters,10 Burpees,Rest 30 seconds","For Time"));
        benchs4.add(inicilizarBench("MEADOWS","20 Muscle-ups,25 Lowers from an inverted hang on the rings, slowly, with straight body and arms,30 Ring handstand push-ups,35 Ring rows,40 Ring push-ups","For Time"));
        benchs4.add(inicilizarBench("SANTIAGO","35 pound Dumbbell hang squat clean, 18 reps,18 Pull-ups,135 pound Power clean, 10 reps,10 Handstand push-ups","For Time"));
        benchs4.add(inicilizarBench("CARSE","95 pound Squat clean,Double-under,185 pound Deadlift,24? Box jump,Begin each round with a 50 meter Bear crawl.","For Time"));
        benchs4.add(inicilizarBench("BRADSHAW","3 Handstand push-ups,225 pound Deadlift, 6 reps,12 Pull-ups,24 Double-unders","For Time"));
        benchs4.add(inicilizarBench("WHITE","15' Rope climb, 3 ascents,10 Toes to bar,21 Walking lunge steps with 45lb plate held overhead,Run 400 meters","For Time"));
        benchs4.add(inicilizarBench("SANTORA","155 pound Squat cleans, 1 minute,20' Shuttle sprints (20' forward + 20' backwards = 1 rep), 1 minute,245 pound Deadlifts, 1 minute,Burpees, 1 minute,155 pound Jerks, 1 minute,Rest 1 minute","For Reps"));
        benchs4.add(inicilizarBench("WOOD","Run 400 meters,10 Burpee box jumps, 24? box,95 pound Sumo-deadlift high-pull, 10 reps,95 pound Thruster, 10 reps,Rest 1 minute","For Time"));
        benchs4.add(inicilizarBench("HIDALGO","Run 2 miles,Rest 2 minutes,135 pound Squat clean, 20 reps,20 Box jump, 24? box,20 Walking lunge steps with 45lb plate held overhead,20 Box jump, 24? box,135 pound Squat clean, 20 reps,Rest 2 minutes,Run 2 miles","For Time"));
        benchs4.add(inicilizarBench("RICKY","10 Pull-ups,75 pound dumbbell Deadlift, 5 reps,135 pound Push-press, 8 reps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("DAE HAN","Run 800 meters with a 45 pound barbell,15 foot Rope climb, 3 ascents,135 pound Thruster, 12 reps","For Time"));
        benchs4.add(inicilizarBench("DESFORGES","225 pound Deadlift, 12 reps,20 Pull-ups,135 pound Clean and jerk, 12 reps,20 Knees to elbows","For Time"));
        benchs4.add(inicilizarBench("RAHOI","24 inch Box Jump, 12 reps,95 pound Thruster, 6 reps,6 Bar-facing burpees","AMRAP, 12 Min"));
        benchs4.add(inicilizarBench("ZIMMERMAN","11 Chest-to-bar pull-ups,2 Deadlifts, 315 pounds,10 Handstand push-ups","AMRAP 25 Min"));
        benchs4.add(inicilizarBench("KLEPTO","27 Box jumps, 24? box,20 Burpees,11 Squat cleans, 145 pounds","For Time"));
        benchs4.add(inicilizarBench("DEL","25 Burpees,Run 400 meters with a 20 pound medicine ball,25 Weighted pull-ups with a 20 pound dumbbell,Run 400 meters with a 20 pound medicine ball,25 Handstand push-ups,Run 400 meters with a 20 pound medicine ball,25 Chest-to-bar pull-ups,Run 400 meters with a 20 pound medicine ball,25 Burpees","For Time"));
        benchs4.add(inicilizarBench("PHEEZY","165 pound Front squat, 5 reps,18 Pull-ups,225 pound Deadlift, 5 reps,18 Toes-to-bar,165 pound Push jerk, 5 reps,18 Hand-release push-ups","For Time"));
        benchs4.add(inicilizarBench("J.J.","185 pound Squat clean, 1 rep,10 Parallette handstand push-ups,185 pound Squat clean, 2 reps,9 Parallette handstand push-ups,185 pound Squat clean, 3 reps,8 Parallette handstand push-ups,185 pound Squat clean, 4 reps,7 Parallette handstand push-ups,185 pound Squat clean, 5 reps,6 Parallette handstand push-ups,185 pound Squat clean, 6 reps,5 Parallette handstand push-ups,185 pound Squat clean, 7 reps,4 Parallette handstand push-ups,185 pound Squat clean, 8 reps,3 Parallette handstand push-ups,185 pound Squat clean, 9 reps,2 Parallette handstand push-ups,185 pound Squat clean, 10 reps,1 Parallette handstand push-up","For Time"));
        benchs4.add(inicilizarBench("JAG 28","Run 800 meters,28 Kettlebell swings, 2 pood,28 Strict Pull-ups,28 Kettlebell clean and jerk, 2 pood each,28 Strict Pull-ups,Run 800 meters","For Time"));
        benchs4.add(inicilizarBench("BRIAN","15 foot Rope climb, 5 ascents,185 pound Back squat, 25 reps","For Time"));
        benchs4.add(inicilizarBench("NICK","45 pound Dumbbell hang squat clean, 10 reps,6 Handstand push-ups on dumbbells","For Time"));
        benchs4.add(inicilizarBench("STRANGE","600 meter Run,1.5 pood Weighted pull-up, 11 reps,11 Walking lunge steps, carrying 1.5 pood kettlebells,1.5 pood Kettlebell thruster, 11 reps","For Time"));
        benchs4.add(inicilizarBench("TUMILSON","Run 200 meters,11 Dumbbell burpee deadlifts, 60 pound dumbbells","For Time"));
        benchs4.add(inicilizarBench("SHIP","185 pound Squat clean, 7 reps,8 Burpee box jumps, 36? box","For Time"));
        benchs4.add(inicilizarBench("JARED","Run 800 meters,40 Pull-ups,70 Push-ups","For Time"));
        benchs4.add(inicilizarBench("TULLY","Swim 200 meters,40 pound Dumbbell squat cleans, 23 reps","For Time"));
        benchs4.add(inicilizarBench("HOLLEYMAN","5 Wall ball shots, 20 pound ball,3 Handstand push-ups,225 pound Power clean, 1 rep","For Time"));
        benchs4.add(inicilizarBench("ADRIAN","3 Forward rolls,5 Wall climbs,7 Toes to bar,9 Box jumps, 30? box","For Time"));
        benchs4.add(inicilizarBench("GLEN","135 pound Clean and jerk, 30 reps,Run 1 mile,15 foot Rope climb, 10 ascents,Run 1 mile,100 Burpees","For Time"));
        benchs4.add(inicilizarBench("TOM","7 Muscle-ups,155 pound Thruster, 11 reps,14 Toes-to-bar","AMRAP in 25 min"));
        benchs4.add(inicilizarBench("RALPH","250 pound Deadlift, 8 reps,16 Burpees,15 foot Rope climb, 3 ascents,Run 600 meters","For Time"));
        benchs4.add(inicilizarBench("CLOVIS","Run 10 miles,150 Burpee pull-ups","For Time"));
        benchs4.add(inicilizarBench("WESTON","Row 1000 meters,200 meter Farmer carry, 45 pound dumbbells,45 pound dumbbell Waiter walk, 50 meters, Right arm,45 pound dumbbell Waiter walk, 50 meters, Left arm","For Time"));
        benchs4.add(inicilizarBench("LOREDO","24 Squats,24 Push-ups,24 Walking lunge steps,Run 400 meters","For Time"));
        benchs4.add(inicilizarBench("SEAN","11 Chest to bar pull-ups,75 pound Front squat, 22 reps","For Time"));
        benchs4.add(inicilizarBench("HORTMAN","Run 800 meters,80 Squats,8 Muscle-ups","AMRAP in 45 min"));
        benchs4.add(inicilizarBench("HAMILTON","Row 1000 meters,50 Push-ups,Run 1000 meters,50 Pull-ups","For Time"));
        benchs4.add(inicilizarBench("ZEUS","30 Wall ball shots, 20 pound ball,75 pound Sumo deadlift high-pull, 30 reps,30 Box jump, 20? box,75 pound Push press, 30 reps,Row 30 calories,30 Push-ups,Body weight Back squat, 10 reps","For Time"));
        benchs4.add(inicilizarBench("BARRAZA","Run 200 meters,275 pound Deadlift, 9 reps,6 ","AMRAP"));
        benchs4.add(inicilizarBench("CAMERON","50 Walking lunge steps,25 Chest to bar pull-ups,50 Box jumps, 24 inch box,25 Triple-unders,50 Back extensions,25 Ring dips,50 Knees to elbows,25 Wallball ?2-fer-1s?, 20 pound ball,50 Sit-ups,15 foot Rope climb, 5 ascents","For Time"));
        benchs4.add(inicilizarBench("JORGE","30 GHD sit-ups,155 pound Squat clean, 15 reps,24 GHD sit-ups,155 pound Squat clean, 12 reps,18 GHD sit-ups,155 pound Squat clean, 9 reps,12 GHD sit-ups,155 pound Squat clean, 6 reps,6 GHD sit-ups,155 pound Squat clean, 3 reps","For Time"));
        benchs4.add(inicilizarBench("BREHM","15 foot Rope climb, 10 ascents,225 pound Back squat, 20 reps,30 Handstand push-ups,Row 40 calories","For Time"));
        benchs4.add(inicilizarBench("OMAR","95 pound barbell Thrusters, 10,15 Bar-facing burpees,95 pound barbell Thrusters, 20,25 Bar-facing burpees,95 pound barbell Thrusters, 30,35 Bar-facing burpees","For Time"));
        benchs4.add(inicilizarBench("GALLANT","Run 1 mile with a 20 pound medicine ball,60 Burpee pull-ups,Run 800 meters with a 20 pound medicine ball,30 Burpee pull-ups,Run 400 meters with a 20 pound medicine ball,15 Burpee pull-ups","For Time"));
        benchs4.add(inicilizarBench("BRUCK","Run 400 meters,185 pound Back squat, 24 reps,135 pound Jerk, 24 reps","For Time"));
        benchs4.add(inicilizarBench("SMYKOWSKI","Run 6k,60 Burpee pull-ups","For Time"));
        benchs4.add(inicilizarBench("FALKEL","8 Handstand push-ups,8 Box jump, 30 inch box,15 foot Rope climb, 1 ascent",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("DONNY","225 pound Deadlift,Burpee","For Time"));
        benchs4.add(inicilizarBench("DOBOGAI","8 Muscle-ups,22 yard Farmer carry, 50 pound dumbbells","For Time"));
        benchs4.add(inicilizarBench("RONEY","Run 200 meters,135 pound Thruster, 11 reps,Run 200 meters,135 pound Push press, 11 reps,Run 200 meters,135 pound Bench press, 11 reps","For Time"));
        benchs4.add(inicilizarBench("DON","66 Deadlifts, 110 pounds,66 Box jump, 24 inch box,66 Kettlebell swings, 1.5 pood,66 Knees to elbows,66 Sit-ups,66 Pull-ups,66 Thrusters, 55 pounds,66 Wall ball shots, 20 pound ball,66 Burpees,66 Double-unders","For Time"));
        benchs4.add(inicilizarBench("DRAGON","Run 5k,4 minutes to find 4 rep max Deadlift,Run 5k,4 minutes to find 4 rep max Push jerk",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("WALSH","22 Burpee pull-ups,185 pound Back squat, 22 reps,Run 200 meters with a 45 pound plate overhead","For Time"));
        benchs4.add(inicilizarBench("LEE","Run 400 meters,345 pound Deadlift, 1 rep,185 pound Squat clean, 3 reps,185 pound Push jerk, 5 reps,3 Muscle-ups,15 foot Rope climb, 1 ascent","For Time"));
        benchs4.add(inicilizarBench("Willy","Run 800 meters,225 pound Front squat, 5 reps,Run 200 meters,11 Chest to bar pull-ups,Run 400 meters,12 Kettlebell swings, 2 pood","For Time"));
        benchs4.add(inicilizarBench("Coffey","Run 800 meters,135 pound Back squat, 50 reps,135 pound Bench press, 50 reps,Run 800 meters,135 pound Back squat, 35 reps,135 pound Bench press, 35 reps,Run 800 meters,135 pound Back squat, 20 reps,135 pound Bench press, 20 reps,Run 800 meters,1 Muscle-up","For Time"));
        benchs4.add(inicilizarBench("DG","8 Toes to bar,35 pound Dumbbell thruster, 8 reps,35 pound Dumbbell walking lunge, 12 steps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("TK","8 Strict Pull-ups,8 Box jumps, 36? box,12 Kettlebell swings, 2 pood",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("JUSTIN","Body-weight back squats,Body-weight bench presses,Strict pull-ups","For Time"));
        benchs4.add(inicilizarBench("NUKES","1-mile run,315-lb. deadlifts, max reps,Then, 10 minutes to complete:,1-mile run,225-lb. power cleans, max reps,Then, 12 minutes to complete:,1-mile run,135-lb. overhead squats, max reps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("ZEMBIEC","11 back squats, 185 lb.,7 strict burpee pull-ups,400-meter run","For Time"));
        benchs4.add(inicilizarBench("ALEXANDER","31 back squats, 135 lb.,12 power cleans, 185 lb.","For Time"));
        benchs4.add(inicilizarBench("BELL","185-lb. deadlifts, 21 reps,15 pull-ups,185-lb. front squats, 9 reps","For Time"));
        benchs4.add(inicilizarBench("JBO","115-lb. overhead squats, 9 reps,1 legless rope climb, 15-foot rope, beginning from seated,115-lb. bench presses, 12 reps",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("KEVIN","185-lb. deadlifts, 32 reps,32 , alternating arms,800-meter running farmer carry, 15-lb. dumbbells","For Time"));
        benchs4.add(inicilizarBench("ROCKET","50-yard swim,10 push-ups,15 squats",TipoWodEnum.AMRAP));
        benchs4.add(inicilizarBench("RILEY","Run 1.5 miles,150 burpees,Run 1.5 miles","For Time"));
        benchs4.add(inicilizarBench("FEEKS","2 x 100-meter shuttle sprint,2 squat clean thrusters, 65-lb. dumbbells,4 x 100-meter shuttle sprint,4 squat clean thrusters, 65-lb. dumbbells,6 x 100-meter shuttle sprint,6 squat clean thrusters, 65-lb. dumbbells,8 x 100-meter shuttle sprint,8 squat clean thrusters, 65-lb. dumbbells,10 x 100-meter shuttle sprint,10 squat clean thrusters, 65-lb. dumbbells,12 x 100-meter shuttle sprint,12 squat clean thrusters, 65-lb. dumbbells,14 x 100-meter shuttle sprint,14 squat clean thrusters, 65-lb. dumbbells,16 x 100-meter shuttle sprint,16 squat clean thrusters, 65-lb. dumbbells","For Time"));
        benchs4.add(inicilizarBench("NED","11 body-weight back squats,1,000-meter row","For Time"));
        benchs4.add(inicilizarBench("SHAM","11 body-weight deadlifts,100-meter sprint","For Time"));
        salvarTipoBench(ctx,tipoBench4);
        salvarBenchs(ctx,benchs4,tipoBench4);
    }

    private static Benchmark inicilizarBench(String nome,String exercicio,TipoWodEnum tipo){
        Benchmark bench = new Benchmark();
        bench.setNome(nome.toUpperCase());
        bench.setDescricaoExercicios(exercicio);
        bench.setTipoWod(tipo);
        return bench;
    }
    private static Benchmark inicilizarBench(String nome,String exercicio,String tipo){
        Benchmark bench = new Benchmark();
        bench.setNome(nome.toUpperCase());
        bench.setDescricaoExercicios(exercicio);
        bench.setTipoWod(TipoWodEnum.obterPorString(tipo));
        return bench;
    }
    private static void salvarBenchs(String ctx,List<Benchmark> benchs, TipoBenchmark tipo) throws Exception{
        for(Benchmark bench : benchs){
            bench.setTipoBenchmark(tipo);
            salvarBench(ctx,bench);
        }
    }
    private static void  salvarTipoBench(String ctx,TipoBenchmark tipo) throws Exception{
        tipo.setOrigem(OrigemTipoBenchmark.SISTEMA);
        getTipoBenchmarkService().inserir(ctx,tipo);
    }
    private static boolean baseJaAdicionada(String ctx) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select obj from TipoBenchmark obj where obj.nome = :nome and obj.origem = :origem");
        HashMap<String,Object> params = new HashMap<String, Object>();
        params.put("nome","The Benchmark Girls");
        params.put("origem",OrigemTipoBenchmark.SISTEMA);
        TipoBenchmark tipo  = getTipoBenchmarkService().obterObjetoPorParam(ctx,sql.toString(),params);
        return tipo != null;
    }
    private static void salvarBench(String ctx,Benchmark bench) throws Exception{
        getBenchmarkService().inserir(ctx,bench);
    }
    private static TipoBenchmarkService getTipoBenchmarkService(){
        return  (TipoBenchmarkService)UtilContext.getBean(TipoBenchmarkService.class);
    }
    private static BenchmarkService getBenchmarkService(){
        return  (BenchmarkService) UtilContext.getBean(BenchmarkService.class);
    }
}
