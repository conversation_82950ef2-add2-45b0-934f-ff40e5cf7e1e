package br.com.pacto.service.impl.avaliacao;

import org.json.JSONArray;

/**
 * Created by alcides on 14/10/2017.
 */
public class BIAvaliacaoFisicaTO {

    private int avaliacoes = 0;
    private int novas = 0;
    private int reavaliacoes = 0;
    private int previstas = 0;
    private int realizadas = 0;
    private int atrasadas = 0;
    private int futuras = 0;
    private int semAvaliacao = 0;
    private int ativosAtrasada= 0;
    private int perderamPeso = 0;
    private int ganharamMassaMagra = 0;
    private int perderamGordura = 0;
    private int alunosParq = 0;
    private JSONArray grafico;

    public JSONArray getGrafico() {
        return grafico;
    }

    public void setGrafico(JSONArray grafico) {
        this.grafico = grafico;
    }

    public int getAvaliacoes() {
        return avaliacoes;
    }

    public void setAvaliacoes(int avaliacoes) {
        this.avaliacoes = avaliacoes;
    }

    public int getNovas() {
        return novas;
    }

    public void setNovas(int novas) {
        this.novas = novas;
    }

    public int getReavaliacoes() {
        return reavaliacoes;
    }

    public void setReavaliacoes(int reavaliacoes) {
        this.reavaliacoes = reavaliacoes;
    }

    public int getPrevistas() {
        return previstas;
    }

    public void setPrevistas(int previstas) {
        this.previstas = previstas;
    }

    public int getRealizadas() {
        return realizadas;
    }

    public void setRealizadas(int realizadas) {
        this.realizadas = realizadas;
    }

    public int getAtrasadas() {
        return atrasadas;
    }

    public void setAtrasadas(int atrasadas) {
        this.atrasadas = atrasadas;
    }

    public int getFuturas() {
        return futuras;
    }

    public void setFuturas(int futuras) {
        this.futuras = futuras;
    }

    public int getSemAvaliacao() {
        return semAvaliacao;
    }

    public void setSemAvaliacao(int semAvaliacao) {
        this.semAvaliacao = semAvaliacao;
    }

    public int getAtivosAtrasada() {
        return ativosAtrasada;
    }

    public void setAtivosAtrasada(int ativosAtrasada) {
        this.ativosAtrasada = ativosAtrasada;
    }

    public int getPerderamPeso() {
        return perderamPeso;
    }

    public void setPerderamPeso(int perderamPeso) {
        this.perderamPeso = perderamPeso;
    }

    public int getPerderamGordura() {
        return perderamGordura;
    }

    public void setPerderamGordura(int perderamGordura) {
        this.perderamGordura = perderamGordura;
    }

    public int getAlunosParq() {
        return alunosParq;
    }

    public void setAlunosParq(int alunosParq) {
        this.alunosParq = alunosParq;
    }

    public int getGanharamMassaMagra() {
        return ganharamMassaMagra;
    }

    public void setGanharamMassaMagra(int ganharamMassaMagra) {
        this.ganharamMassaMagra = ganharamMassaMagra;
    }
}
