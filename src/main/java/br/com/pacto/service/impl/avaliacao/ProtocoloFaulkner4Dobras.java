package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by <PERSON>ao Alcides on 05/06/2017.
 */
public class ProtocoloFaulkner4Dobras {

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        avaliacao.setTotalDobras(
                Uteis.forcarCasasDecimais(2, new BigDecimal(
                                somatorio(avaliacao.getTriceps(),
                                        avaliacao.getSubescapular(),
                                        avaliacao.getSupraIliaca(),
                                        avaliacao.getAbdominal()))));


        Double percGordura = percentualGorduraFaulkner(avaliacao.getTriceps(), avaliacao.getSubescapular(), avaliacao.getSupraIliaca(), avaliacao.getAbdominal());
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = pesoGorduraFaulkner(avaliacao.getPeso(), percGordura);
        Double massaMagra = massaCorporalMagraFaulkner(avaliacao.getPeso(), massaGorda);


        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));



        return avaliacao;
    }

    public static Double somatorio(double tricipital, double subescapular, double suprailiaca, double abdominal) {
        return tricipital + suprailiaca + abdominal + subescapular;
    }


    /**
     * Método para calcular o percentual de gordura Faulkner
     *
     * @param tricipital   - Dado equivalente a tricipital
     * @param subescapular - Dado equivalente a subescapular
     * @param suprailiaca  - Dado equivalente a suprailiaca
     * @param abdominal    - Dado equivalente a abdominal
     * @return Percentual de gordura
     */
    public static Double percentualGorduraFaulkner(double tricipital, double subescapular, double suprailiaca, double abdominal) {
        if ((tricipital > 0) && (subescapular > 0) && (suprailiaca > 0) && (abdominal > 0)) {
            // Percentual de gordura = ((tricipital + subescapular + suprailiaca + abdominal)x 0,153 + 5,783)
            return (tricipital + subescapular + suprailiaca + abdominal) * 0.153 + 5.783;
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso gordura Faulkner
     *
     * @param peso              - Dado equivalente ao peso
     * @param percentualGordura - Dado equivalente ao Percentual de gordura
     * @return Peso gordura
     */
    public static Double pesoGorduraFaulkner(double peso, Double percentualGordura) {
        // Se peso maior que 0 e percentual de gordura possuir alguma coisa
        if ((peso > 0) && (percentualGordura != 0.0)) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular a Massa corporal magra Faulkner
     *
     * @param peso        - Dado equivalente ao peso
     * @param pesoGordura - Dado equivalente ao peso da gordura
     * @return Massa Corporal Magra
     */
    public static Double massaCorporalMagraFaulkner(double peso, Double pesoGordura) {
        // Se peso maior que 0 e peso de gordura possui alguma coisa
        if ((peso > 0) && (pesoGordura != null)) {
            return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular o Peso Muscular Faulkner
     *
     * @param peso         - Dado equivalente ao peso
     * @param pesoResidual - Dado equivalente ao peso residual
     * @param pesoOsseo    - Dado equivalente ao peso ósseo
     * @param pesoGordura  - Dado equivalente ao peso de gordura
     * @return Peso Muscular
     */
    public static Double pesoMuscularFaulkner(double peso, Double pesoResidual, Double pesoOsseo, Double pesoGordura) {
        // Se peso for maior que 0 e peso residual, peso ósseo e peso de gordura tiver alguma coisa
        if ((peso > 0) && (pesoResidual != null) && (pesoOsseo != null) && (pesoGordura != null)) {
            return Uteis.calculaPesoMuscular(peso, pesoGordura, pesoOsseo, pesoResidual);
        }
        return 0.0;
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
        return a.getTriceps() > 0.0 && a.getSubescapular() > 0.0 && a.getSupraIliaca() > 0.0 && a.getAbdominal() > 0.0;
    }
}
