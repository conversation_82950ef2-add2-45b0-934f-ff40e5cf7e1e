/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.agenda;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.programa.ObjetivoAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoAlunoVO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoVO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendamentoDTO;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDTO;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoPersonalDTO;
import br.com.pacto.controller.json.aluno.FrequenciaAtendimentoAlunoVO;
import br.com.pacto.controller.json.avaliacao.ConfirmacaoAgendamentoJSON;
import br.com.pacto.controller.json.avaliacao.HorarioPersonalAgendaJSON;
import br.com.pacto.controller.json.base.ConfiguracoesDiasDeBloqueioDTO;
import br.com.pacto.controller.json.gestao.IndicadorAgendaResponseDTO;
import br.com.pacto.controller.json.gestao.TreinosExecutadosEAcessosPorDiaVO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.to.ScheduleModel;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.service.exception.HorarioConcomitanteException;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.util.bean.AgendaTO;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.RecursosAgendaEnum;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AgendamentoService {

    public static final String SERVICE_NAME = "ProfessorAgendamentoService";

    public Agendamento inserir(final String ctx, Agendamento object, Usuario usuario) throws ServiceException;

    public Agendamento inserirV2(final String ctx, Agendamento object, Usuario usuario, Integer empresaId) throws ServiceException;

    public Agendamento obterPorId(final String ctx, Integer id) throws ServiceException;

    public Agendamento alterar(final String ctx, Agendamento object, Usuario usuario) throws ServiceException;

    public void excluir(final String ctx, Agendamento object, boolean aplicarAoNsu, boolean somenteMesmoTipo,
                        boolean somenteMesmoTipoPosteriores) throws ServiceException;

    void excluir(final String ctx, Agendamento object) throws Exception;

    public List<Agendamento> obterTodos(final String ctx) throws ServiceException;

    public List<Agendamento> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<IndicadorAgendaResponseDTO> obterPorProfessor(Integer empresaId, FiltroGestaoJSON filtro, String sort) throws ServiceException ;

    public List<Agendamento> obterPorParam(final String ctx, String query,
                                           Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Agendamento obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<Agendamento> consultarPorData(final String ctx, Date inicio,
                                       Date fim, final Integer empresaZW, final String professores,
                                       final String tipos, final String status, final Integer codigoCliente,
                                       Boolean disponibilidades, Boolean ignorarCancelados, String orderBy, Integer index,
                                       Integer maxResults, String nomeProfessor) throws ServiceException;


    List<Agendamento> consultarPorData(final String ctx, Date inicio,
                                       Date fim, final Integer empresaZW, final String professores,
                                       final String tipos, final String status, final Integer codigoCliente,
                                       Boolean disponibilidades, Boolean ignorarCancelados, String orderBy, Integer index,
                                       Integer maxResults, Boolean between, String nomeProfessor, boolean modeloNovo) throws ServiceException;

    List<Agendamento> consultarPorDataV2(final String ctx, Date inicio,
                                       Date fim, final Integer empresaZW, final String professores,
                                       final String tipos, final String status, final Integer codigoCliente,
                                       Boolean disponibilidades, Boolean ignorarCancelados, String orderBy, Integer index,
                                       Integer maxResults, Boolean between, String nomeProfessor, boolean modeloNovo) throws ServiceException;

    void repetirV2(String ctx, Agendamento evento,
                   Date dataLimite, Map<Date, List<Agendamento>> agendamentos,
                   boolean usarDiasSemana, String diaSemana, boolean aplicarNSU,
                   boolean apenasOutrosTipos, Usuario usuario) throws Exception;

    void repetirV2Retroativo(String ctx, Agendamento evento, Date dataInicioOriginal,
                            Date dataLimite, Map<Date, List<Agendamento>> agendamentos,
                            boolean usarDiasSemana, String diaSemana, boolean aplicarNSU,
                            boolean apenasOutrosTipos, Usuario usuario) throws Exception;

    void repetir(String ctx, Agendamento evento,
                 Date dataLimite, Map<Date, List<Agendamento>> agendamentos,
                 boolean usarDiasSemana, List<GenericoTO> diasSemana, boolean aplicarNSU,
                 boolean apenasOutrosTipos, Usuario usuario) throws Exception;

    public void adicionarAlterarAgendamento(String ctx, Agendamento agendamento,
                                            boolean coordenador, Map<Date, List<Agendamento>> agendamentos,
                                            List<GenericoTO> diasSemana, FiltrosAgendaTO filtros,
                                            final ClienteSintetico clienteEscolhido, Usuario usuario)
            throws ValidacaoException, ServiceException;

    public List<Agendamento> proximasDisponibilidadeProfessor(String ctx, Integer professor, Date data) throws ServiceException;

    public Agendamento sugerirAgendamento(String ctx, ClienteSintetico cliente) throws ServiceException;

    public Map<Date, List<Agendamento>> montarAgenda(String ctx, ScheduleModel eventModel, Usuario usuario, FiltrosAgendaTO filtros,
                                                     Boolean consultaEmBanco, Map<Date, List<Agendamento>> agendamentos, Boolean disponibilidades,
                                                     ConfiguracaoSistema cfgAbrirVazia) throws ServiceException;

    public Agendamento montarNovoAgendamento(String ctx, Date dataSelecionada, String view,
                                             FiltrosAgendaTO filtrosAgenda, Usuario usuario, ClienteSintetico clientesugerido,
                                             Date dataFim) throws ValidacaoException, ServiceException;

    public String redimensionarEvento(String ctx, AgendaTO event, Map<Date, List<Agendamento>> agendamentos, List<GenericoTO> diasSemana,
                                      Usuario usuario, FiltrosAgendaTO filtros) throws ValidacaoException, ServiceException;

    public boolean executarValidacoesUsuarioAgenda(RecursosAgendaEnum recursoAgenda, Usuario usuario, TipoEvento tipoEvento,
                                                   boolean disponibilidade, boolean lancarException) throws ServiceException;

    public boolean possuiAgendamentoAssociado(String ctx, Integer nsu) throws ServiceException;

    public String executarEscolhaUsuario(String ctx, Agendamento agendamento, boolean aplicarAoNSU,
                                         boolean apenasMesmoTipo,boolean apenasMesmoTipoPosteriores, RecursosAgendaEnum recurso, Map<Date, List<Agendamento>> agendamentos,
                                         List<GenericoTO> diasSemana, Usuario usuario) throws ServiceException;

    public boolean possuiAgendamentoAssociadoOutroTipo(String ctx, Integer nsu, Integer tipo) throws ServiceException;

    public List<Agendamento> disponibilidadeProfessor(String ctx, Integer professor, Date data) throws ServiceException;

    public void montarListaAlunos(String ctx, FiltrosAgendaTO filtros, Agendamento agendamento, Integer empresaZW) throws ServiceException;

    public void escolherTipoEvento(FiltrosAgendaTO filtros, Agendamento agendamento, boolean atualizarHoras);

    public List<Agendamento> consultarPrevistosNosProximosMinutos(final String ctx, final Date dataBase,
                                                                  Integer professor, Integer aluno,
                                                                  Integer start,
                                                                  TipoLembreteEnum tipoLembrete) throws ServiceException;

    public List<Agendamento> consultarAgendamentosNovosOuAlterados(final String ctx,
                                                                   Integer professor, Integer aluno,
                                                                   TipoLembreteEnum tipoLembrete) throws ServiceException;

    public boolean clienteTemAgendamentoHoje(final String ctx, final ClienteSintetico cliente) throws ServiceException;

    public List<Agendamento> agendamentosFuturosCliente(final String ctx, final ClienteSintetico cliente, final TipoAgendamentoEnum tipo) throws ServiceException;

    public List<Agendamento> obterMaisAgendamentos(final String ctx, final Integer idProfessor, final Integer idCliente,
                                                   final Integer maxResults, final Integer index) throws ServiceException;

    public List<Agendamento> verificarEventosConcomitantes(final String ctx, Date inicio, Date fim,
                                                           Integer professor, Integer codigoEvento) throws ServiceException;

    public Agendamento alterarStatus(final String ctx, Agendamento object,
                                     StatusAgendamentoEnum novoStatus, String observacao) throws ServiceException;

    public String getTiposEvSelecionados(FiltrosAgendaTO filtrosAgenda) throws Exception;

    public List<Agendamento> listaAgendamentos(String ctx, Usuario usuario, FiltrosAgendaTO filtros) throws ServiceException;

    public void alterarProfessorAgendamentosFuturos(String ctx, Integer professorNovo,Integer professorAntigo, Integer tipo) throws ServiceException;

    Agendamento obterAgendamentoNSUMaiorDataFim(String ctx, Integer nsu) throws Exception;

    boolean existeAgendamentoFuturo(String ctx, final TipoEvento tipoEvento) throws Exception;

    public List<String> sugestoesHorario(final String key, final Integer professor, final TipoEvento tipoEvento, final Date hora, final Integer empresa) throws Exception;

    public void gravarAgendamentoAvaliacao(String ctx, Agendamento agendamento, Usuario usuario, AvaliacaoFisica avaliacaoFisica) throws Exception;

    public ConfirmacaoAgendamentoJSON lancarAgendamentoPeloAluno(final String ctx,
                                                                 final Date data,
                                                                 final String horario,
                                                                 final Integer professor,
                                                                 final String matricula,
                                                                 final Integer empresa,
                                                                 final Integer tipoEvento) throws Exception;

    public List<Agendamento> consultarAgendamentoDataHora(final String ctx,
                                                          final Date dataInicio,
                                                          final String horaInicial,
                                                          final String horaFinal,
                                                          final Integer codigoCliente,
                                                          final Integer codigoClienteSintetico) throws Exception;

    ServicoAgendamentoDTO criarAgendamentoAluno(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException;

    ServicoAgendamentoDTO alterarAgendamentoAluno(HttpServletRequest request, Integer empresaId, Integer id, AgendamentoDTO agendamentoDTO) throws ServiceException,
            HorarioConcomitanteException;

    List<Agendamento> obterPorNSU(String ctx, Integer nsu) throws ServiceException;

    List<Agendamento> obterDisponibilidadePorNSUApartirDia(String ctx, Integer nsu, Date dia) throws ServiceException;

    void refresh(final String ctx, Agendamento agendamento);

    void removerAgendamentoAluno(Integer agendamentoId) throws ServiceException;

    HorarioPersonalAgendaJSON sugestoesHorarioApp(final String key, final TipoEvento tipoEvento, final Date hora, final Integer empresa) throws Exception;

    ServicoAgendamentoPersonalDTO criarAgendamentoAlunoPersonal(HttpServletRequest request, Integer empresaId, AgendamentoPersonalDTO agendamentoDTO, String ctx) throws ServiceException;

    void removerAgendamentoAlunoPersonal(String ctx, Integer agendamentoId) throws ServiceException;

    ServicoAgendamentoPersonalDTO alterarAgendamentoAlunoPersonal(HttpServletRequest request, Integer empresaId, Integer id, AgendamentoPersonalDTO agendamentoDTO, String ctx) throws ServiceException;

    Agendamento alterarTodasEmpresas(final String ctx, Agendamento object, Usuario usuario, Date date) throws ServiceException;

    void validarAgendamentoAluno(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException;


    List<Agendamento> consultarAgendamentosDoDia(String ctx, Date dataInicio, Date dataFim) throws Exception;

    List<Agendamento> consultarAgendamentoDataHoraProfessor(String ctx, Date data,
                                                            String horaInicial,
                                                            String horaFinal,
                                                            Integer codigoProfessor) throws Exception;

    void aplicarAlteracoesAoNSU(final String ctx, Agendamento object,
                                boolean somenteMesmoTipo,boolean somenteMesmoTipoPosteriores, Usuario usuario) throws ServiceException;

    Agendamento alterarAlgunsCamposDisponibilidade(final String ctx, Agendamento object, Usuario usuario) throws ServiceException;

    List<Date> criarAgendamentoAlunoRecorrente(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException;

    void validaAgendamento(Integer empresaId, Integer id, AgendamentoDTO agendamentoDTO) throws ServiceException;

    Agendamento validaAgendamento(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException;

    Agendamento validaAgendamentoV2(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException;

    void gravarLogAlteracao(String ctx, String username, Agendamento antes, Agendamento depois);

    void gravarLogCancelamento(String ctx, String username, Agendamento antes, Agendamento depois);

    List<Agendamento> consultarDisponibilidade(final String ctx, Date inicio, Date fim, Integer professor,
                                               List<TipoEvento> tipos, boolean between, boolean inside, final Integer empresaZW) throws ServiceException;

    Integer obterNsuPorCodigoAgendamento(final String ctx, Integer codigo);

    List<ObjetivoAlunoVO> objetivos(String ctx, Integer matricula, Integer status, Boolean primario) throws Exception;

    ObjetivoAlunoVO criarObjetivo(String ctx, Integer matricula, ObjetivoAlunoDTO objetivoDTO) throws ValidacaoException;

    ObjetivoAlunoVO alterarObjetivo(String ctx, Integer id, ObjetivoAlunoDTO objetivoDTO) throws ValidacaoException;

    void removerObjetivo(String ctx, Integer id) throws Exception;

    ObjetivoIntermediarioAlunoVO criarObjetivoIntermediario(String ctx, ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO);

    ObjetivoIntermediarioAlunoVO alterarObjetivoIntermediario(String ctx, Integer id, ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO);

    ObjetivoAlunoVO objetivoPorCodigo(String ctx, Integer id) throws Exception;

    void removerObjetivoIntermediario(String ctx, Integer id);

    ObjetivoIntermediarioAlunoVO objetivoIntermediario(String ctx, Integer id);
    List<TreinosExecutadosEAcessosPorDiaVO> calculaMediaFrequenciaAluno(HttpServletRequest request, Integer codigoProfessor, String ctx, Integer periodo) throws Exception;

    ConfiguracoesDiasDeBloqueioDTO configurarDiasBloqueio(ConfiguracoesDiasDeBloqueioDTO configuracoesDiasDeBloqueioDTO) throws ServiceException;

    ConfiguracoesDiasDeBloqueioDTO buscarConfiguracaoDiasBloqueio() throws ServiceException;

    FrequenciaAtendimentoAlunoVO frequenciaTreinoEAtendimentoAluno(String ctx, Integer id, Integer periodo, HttpServletRequest request) throws Exception;

    String[]  buscaHorarioTurno(String ctx, String turno);
}
