/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 */
//informamos qual é a retenção, ou seja, em que situação
//a marcação será usada. No nosso caso em run time, outras opções seriam: SOURCE  e CLASS.
@Retention(RetentionPolicy.RUNTIME)
//Qual elemento sofrerá anotação, no nosso caso FIELD.
//Outros valores seriam: TYPE (class e interface), METHOD, PARAMETER, CONSTRUCTOR, LOCAL_VARIABLE e PACKAGE.
@Target(ElementType.METHOD)
//declaração da Annotation, note a keywork @interface!
public @interface Processo {

    String autor();

    /**
     * Formato dd/MM/yyyy
     *
     * @return
     */
    String data();

    String descricao() default "N/A";

    String motivacao() default "N/A";
    
    boolean permitirMultiplasExecucoes() default true;
    
    boolean ignorarErro() default false;
}
