package br.com.pacto.base.scheduling.treino;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;

public class TaskCorrigirSeriesZeradas extends TaskBase {

    public TaskCorrigirSeriesZeradas(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        Uteis.logar(null, "Started -> " + TaskExecutionContext.class.getSimpleName() + " " + getCtx());
        SerieService serieService = (SerieService) UtilContext.getBean(SerieService.class);
        try {
            serieService.corrigirSerieZerada(getCtx());
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
