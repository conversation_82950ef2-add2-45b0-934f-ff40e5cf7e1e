package br.com.pacto.base.scheduling.base;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import it.sauronsoftware.cron4j.Scheduler;
import it.sauronsoftware.cron4j.TaskCollector;
import java.util.Set;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * This listener starts a scheduler bounded to the web application: the
 * scheduler is started when the application is started, and the scheduler is
 * stopped when the application is destroyed. The scheduler uses a custom
 * TaskCollector to retrieve, once a minute, its job list. Moreover the
 * scheduler is registered on the application context, in a attribute named
 * according to the value of the {@link Constants#SCHEDULER} constant.
 */
public class SchedulerServletContextListener implements ServletContextListener {

    private boolean isHaveControlScheduling(final String msg) {
        if (Aplicacao.isInstanciaAgendamento()) {
            Uteis.logar(null, msg + " SCHEDULING INSTANCE FOR ======> " + Aplicacao.getProp(Aplicacao.instanceSchedulingName));
            return true;
        }
        return false;
    }

    @Override
    public void contextInitialized(ServletContextEvent event) {
        if (isHaveControlScheduling("ENABLING")) {
            ServletContext context = event.getServletContext();
            // 1. Creates the scheduler.
            Scheduler scheduler = new Scheduler();
            scheduler.setDaemon(true);
            // 2. Registers a custom task collector.
            Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
            for (String k : keys) {
                TaskCollector collector = new TaskCollectorBase(k);
                scheduler.addTaskCollector(collector);
            }
            // 3. Starts the scheduler.
            scheduler.start();
            // 4. Registers the scheduler.
            context.setAttribute(Constants.SCHEDULER, scheduler);
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent event) {
        if (isHaveControlScheduling("DISABLING")) {
            ServletContext context = event.getServletContext();
            // 1. Retrieves the scheduler from the context.
            Scheduler scheduler = (Scheduler) context.getAttribute(Constants.SCHEDULER);
            // 2. Removes the scheduler from the context.
            context.removeAttribute(Constants.SCHEDULER);
            // 3. Stops the scheduler.
            scheduler.stop();
        }
    }
}
