/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.personal;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TaskExpirarCreditos  extends TaskBase {
    
    public TaskExpirarCreditos(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskExpirarCreditos.class.getSimpleName() + " " + getCtx());
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            creditoService.expirarCreditos(getCtx(), Calendario.hoje());
        } catch (ServiceException ex) {
            Logger.getLogger(TaskCheckOutAutomatico.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "Ended -> " + TaskExpirarCreditos.class.getSimpleName() + " " + getCtx());
        }
    }
}
