package br.com.pacto.base.util;

/**
 * Exceção especializada, lançada quando tenta-se recuperar um módulo não existente.
 *
 * <AUTHOR>
 * @since 08/11/2018
 */
public final class ModuloNaoEncontradoException extends RuntimeException {

    private static final String MENSAGEM_ERROR = "Não foi possível recuperar o módulo através da sigla informada: (%s)";

    public ModuloNaoEncontradoException(String siglaIncorretaInformada) {
        super(String.format(MENSAGEM_ERROR, siglaIncorretaInformada));
    }
}
