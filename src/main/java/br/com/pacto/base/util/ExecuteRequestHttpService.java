/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.util;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.RequestException;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.zip.GZIPInputStream;

/**
 *
 * <AUTHOR>
 */
public class ExecuteRequestHttpService {
    private static final long serialVersionUID = -7110880934684074185L;
    public int timeout = 0;

    public static final String METODO_POST = "POST";
    public static final String METODO_GET = "GET";
    public static final String METODO_PUT = "PUT";
    public static final String METODO_DELETE = "DELETE";
    public static final String ENCODE_UTF8 = "UTF-8";

    public int connectTimeout = 0;
    public int readTimeout = 0;

    public static String obterIPExterno() {
        String urlRequest = "http://jsonip.com";
        String retorno;
        try {
            String ipSessao = (String) JSFUtilities.getFromSession("ip");
            if (ipSessao != null && !ipSessao.isEmpty()) {
                return ipSessao;
            } else {
                retorno = executeRequest(urlRequest, null);
                if (retorno != null && !retorno.isEmpty()) {
                    JSONObject j = new JSONObject(retorno);
                    retorno = j.getString("ip");
                }
            }
        } catch (Exception ex) {
            retorno = "SEM_IP";
        }
        return retorno;
    }

    public static String executeRequest(String urlRequest, Map<String, String> params) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
//            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public String executeRequestInner(String urlRequest, Map<String, String> params) throws IOException {
        return executeRequestInner(urlRequest, params, "ISO-8859-1");
    }


    public String executeRequestInner(String urlRequest, Map<String, String> params, String encode) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (timeout != 0) {
            conn.setReadTimeout(timeout);
            conn.setConnectTimeout(timeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encode));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        Uteis.logar(null, resposta);
        return resposta;
    }

    public synchronized static String executeRequestGET(String urlRequest) throws IOException {

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }

    public static String executeHttpRequestGenerico(String urlRequest, Map<String, String> paramsHeader, Map<String, String> paramsCorpo, String metodo, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
}

        String parametrosCodificados = "";
        if (paramsCorpo != null) {
            Set<String> s = paramsCorpo.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = paramsCorpo.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }

        conn.setRequestMethod(metodo);
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public static String executeRequestGETEncode(String urlRequest, Map<String, String> paramsHeader, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }

    public static Map<Integer, String> executeRequestGETEncodeResponseRest(String urlRequest, Map<String, String> paramsHeader, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        Map<Integer,String> reMap = new HashMap<Integer,String>();
        InputStream _is;

        if (conn.getResponseCode() < HttpURLConnection.HTTP_BAD_REQUEST) {
            _is = conn.getInputStream();
        }else{
            _is = conn.getErrorStream();
        }

        BufferedReader rd = new BufferedReader(new InputStreamReader(_is, encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            resposta += line ;
        }
        reMap.put(conn.getResponseCode(), resposta);
        rd.close();

        conn.disconnect();
        return reMap;
    }

    public static String obterUrlComParams(String urlBase, Map<String, String> params, String charset) throws UnsupportedEncodingException {
        if (params == null || params.isEmpty()) {
            return urlBase;
        }

        StringBuilder result = new StringBuilder();
        boolean first = true;
        for(Map.Entry<String, String> entry : params.entrySet()){
            if (first) {
                first = false;
            } else {
                result.append("&");
            }
            result.append(URLEncoder.encode(entry.getKey(), charset));
            result.append("=");
            result.append(URLEncoder.encode(entry.getValue(), charset));
        }
        return urlBase + "?" + result.toString();
    }

    public static CloseableHttpClient createConnectorCloseable() {
        return createConnector(null);
    }

    public static String get(String urlRequest, Map<String, String> headers) throws IOException, RequestException {
        return executeRequestGet(urlRequest, headers);
    }

    public static String executeRequestGet(String urlRequest, Map<String, String> headers) throws IOException, RequestException {
        HttpGet httpGet = new HttpGet(urlRequest);
        httpGet.setHeader("Content-Type", "application/json");
        for (String key: headers.keySet()) {
            httpGet.setHeader(key, headers.get(key));
        }
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpGet);
        int codeResponse = response.getStatusLine().getStatusCode();
        if(codeResponse >= 200 && codeResponse <= 299 ){
            return EntityUtils.toString(response.getEntity());
        }else{
            throw new RequestException(EntityUtils.toString(response.getEntity()));
        }
    }

    public static CloseableHttpClient createConnector() {
        return createConnector(null);
    }

    public static CloseableHttpClient createConnector(RequestConfig requestConfig) {

            // Construir o contexto SSL que ignora a validação de certificado
        SSLContext sslContext = null;
        try {
            sslContext = SSLContextBuilder
                    .create()
                    .loadTrustMaterial((chain, authType) -> true) // Aceita qualquer certificado
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }

        return HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE) // Ignora a verificação do nome do host
                    .setDefaultRequestConfig(requestConfig != null ? requestConfig : RequestConfig.DEFAULT)
                    .build();

    }

    public static String post(String url, String body, Map<String, String> headers) throws IOException, RequestException {
        headers.put("Content-Type", "application/json");
        return executeHttpRequest(url, body, headers, METODO_POST, ENCODE_UTF8, true);
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode, boolean exceptionOnError) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : headers.keySet()){
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if(corpo != null){
            conn.setDoOutput(true);
            OutputStream os = conn.getOutputStream();
            BufferedWriter wr = new BufferedWriter(
                    new OutputStreamWriter(os, "UTF-8"));
            wr.write(corpo);
            wr.flush();
            wr.close();
            os.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }

        InputStreamReader inputStreamReader = new InputStreamReader(in, Charset.forName(encode));
        if ("gzip".equals(conn.getContentEncoding())) {
            if (conn.getResponseCode() == 200 || conn.getResponseCode() == 201 || conn.getResponseCode() == 202) {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getInputStream()), encode);
            } else {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getErrorStream()), encode);
            }
        }

        BufferedReader rd = new BufferedReader(inputStreamReader);
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();

        if((conn.getResponseCode() < 200 || conn.getResponseCode() > 299) && exceptionOnError){
            throw new IOException(resposta.toString());
        }

        return resposta.toString();
    }
}
