/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.oamd;

import br.com.pacto.base.impl.EntityManagerFactoryService;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.threads.ThreadRecursoEmpresa;
import br.com.pacto.threads.ThreadUCP;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import org.apache.commons.io.FileUtils;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.admapp.client.Midia;

/**
 *
 * <AUTHOR>
 */
public class OamdContextListener implements ServletContextListener {

    private ThreadUCP threadUCP = new ThreadUCP();
    private ThreadRecursoEmpresa threadRecursoEmpresa = new ThreadRecursoEmpresa();

    private boolean isLoadEntityManagerFactoryOnStart() {
        if (Aplicacao.getProp(Aplicacao.loadFactoryOnStart) != null
                && Aplicacao.isTrue(Aplicacao.loadFactoryOnStart)) {
            return true;
        }
        return false;
    }

    private void loadConfigs() {
        if (Aplicacao.getProp(Aplicacao.loadInstancesFromCloud).equals("true")) {
            Uteis.logar(null, "Carregando endereços de instancias...");
            try {
                List<String> instancias = Aplicacao.atualizarInfoInstancias();
                Uteis.logar(null, "Obtidas: " + instancias);
            } catch (Exception e) {
                Uteis.logar(null, "FALHOU ao carregar endereços de instancias: "
                        + e.getMessage());
            }
        }
    }

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        loadConfigs();
        if (isLoadEntityManagerFactoryOnStart()) {
            EntityManagerFactoryService.init();
            Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
            int i = 1;
            for (String k : keys) {
                Uteis.logar(null, String.format("%s/%s Iniciando EntityManagerFactory do contexto %s para chave %s",
                        i, keys.size(), sce.getServletContext().getContextPath(), k));
                try {
                    EntityManagerFactoryService
                            .getEntityManagerFactory(k);
                    i++;
                } catch (Exception ex) {
                    Logger.getLogger(OamdContextListener.class.getName()).log(Level.SEVERE, null, ex);
                }

            }
            System.gc();
        }

        if (Aplicacao.isTrue(Aplicacao.useBetaTesters)) {
            BetaTestersService testersService = new BetaTestersService();
            testersService.init();
        }

        try {
            if (!Aplicacao.isEmpty(Aplicacao.dirMidiasEmbedded)) {
                final String dirLocalBase = Aplicacao.getProp(Aplicacao.dirMidiasEmbedded) + "/midias";
                FileUtils.forceMkdir(new File(dirLocalBase));
                EntityManagerFactoryService.init();
                Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
                int i = 1;
                for (String k : keys) {
                    Uteis.logar(null, String.format("%s/%s Obtendo midias de Atividades (fotos e videos)... %s para chave %s",
                            i, keys.size(), sce.getServletContext().getContextPath(), k));
                    try {
                        final String urlBase = AdmAppWSConsumer.obterUrlBase(k);
                        List<Midia> lista = AdmAppWSConsumer.obterMidias(k);
                        List<MidiasNiveisEnum> niveis = Arrays.asList(MidiasNiveisEnum.values());
                        for (Midia midia : lista) {
                            for (MidiasNiveisEnum nivelMidia : niveis) {
                                final String sufixo = String.format("%s%s",
                                        nivelMidia.getLocal(),//ja possui a barra
                                        midia.getNome());
                                String fileName = String.format("%s/%s%s", dirLocalBase, midia.getTema().name(), sufixo);
                                if (!(new File(fileName).exists())) {
                                    Uteis.logar(null, String.format("%s/%s Gravando arquivo %s de midia de Atividade (fotos e videos)... %s para chave %s",
                                            i, keys.size(), fileName, sce.getServletContext().getContextPath(), k));
                                    final String urlMidia = String.format("%s%s", urlBase, sufixo);
                                    Uteis.logar(null, String.format("Baixar %s para %s", urlMidia, fileName));
                                    try {
                                        File f = new File(fileName);
                                        FileUtils.forceMkdir(f.getParentFile());
                                        saveImage(urlMidia, fileName);
                                    } catch (Exception e) {
                                        Uteis.logar(null, String.format("ERRO: Sincronizando %s -> %s -> %s - Motivo: %s", k, urlMidia, fileName, e.getMessage()));
                                    }
                                }/* else {
                                 Uteis.logar(null, String.format("Arquivo %s já existe?!", fileName));
                                 }*/
                            }
                        }
                        i++;
                    } catch (Exception ex) {
                        Logger.getLogger(OamdContextListener.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(OamdContextListener.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void saveImage(String imageUrl, String destinationFile) throws IOException {

        URL url = new URL(imageUrl);
        URLConnection urlConn = url.openConnection();
        urlConn.setConnectTimeout(1000);
        urlConn.setReadTimeout(15000);
        urlConn.setAllowUserInteraction(false);
        urlConn.setDoOutput(true);

        InputStream inStream = urlConn.getInputStream();
        OutputStream os = new FileOutputStream(destinationFile);

        byte[] b = new byte[4096];
        int length;
        int bytesEscritos = 0;
        while ((length = inStream.read(b)) != -1) {
            os.write(b, 0, length);
            bytesEscritos += length;
        }

        inStream.close();
        os.close();
        if (bytesEscritos > 0) {
            System.out.println("TreinoWeb -> gravei Imagem -> " + bytesEscritos + " " + destinationFile);
        } else {
            System.out.println("TreinoWeb -> NÃO gravei Imagem -> " + destinationFile);
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        if (isLoadEntityManagerFactoryOnStart()) {
            Set<String> keys = new HashSet<String>(EntityManagerFactoryService.getEmpresas().keySet());
            int i = 1;
            for (String k : keys) {
                Uteis.logar(null, String.format("%s/%s Desativando EntityManagerFactory do contexto %s para chave %s",
                        i, keys.size(), sce.getServletContext().getContextPath(), k));
                try {
                    EntityManagerFactoryService.close(k);
                    i++;
                } catch (Exception ex) {
                    Logger.getLogger(OamdContextListener.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            EntityManagerFactoryService.getEmpresas().clear();
            System.gc();
        }
        if (threadRecursoEmpresa != null){
            threadRecursoEmpresa.interrupt();
        }
    }

    private void iniciarThreadUCP() {
        try {
            threadUCP.setDaemon(true);
            threadUCP.start();
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
    }

    private void iniciarThreadRecursoEmpresa() {
        try {
            threadRecursoEmpresa.setDaemon(true);
            threadRecursoEmpresa.start();
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
    }
}
