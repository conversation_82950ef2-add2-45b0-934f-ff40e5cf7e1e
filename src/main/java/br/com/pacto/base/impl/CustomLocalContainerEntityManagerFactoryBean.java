/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.impl;

import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceException;
import javax.persistence.spi.PersistenceUnitInfo;
import javax.sql.DataSource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.instrument.classloading.LoadTimeWeaver;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.persistenceunit.PersistenceUnitManager;
import org.springframework.orm.jpa.persistenceunit.PersistenceUnitPostProcessor;

/**
 *
 * <AUTHOR>
 */
public class CustomLocalContainerEntityManagerFactoryBean extends LocalContainerEntityManagerFactoryBean {

    @Override
    protected EntityManagerFactory createNativeEntityManagerFactory() throws PersistenceException {
        return super.createNativeEntityManagerFactory(); //To change body of generated methods, choose Tools | Templates.
//        Persistence.
    }

    @Override
    public void setPersistenceUnitManager(PersistenceUnitManager persistenceUnitManager) {
        super.setPersistenceUnitManager(persistenceUnitManager); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void setPersistenceXmlLocation(String persistenceXmlLocation) {
        super.setPersistenceXmlLocation(persistenceXmlLocation); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void setDataSource(DataSource dataSource) {
        super.setDataSource(dataSource); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void setPersistenceUnitPostProcessors(PersistenceUnitPostProcessor[] postProcessors) {
        super.setPersistenceUnitPostProcessors(postProcessors); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void setLoadTimeWeaver(LoadTimeWeaver loadTimeWeaver) {
        super.setLoadTimeWeaver(loadTimeWeaver); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        super.setResourceLoader(resourceLoader); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    protected PersistenceUnitInfo determinePersistenceUnitInfo(PersistenceUnitManager persistenceUnitManager) {
        return super.determinePersistenceUnitInfo(persistenceUnitManager); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    protected void postProcessEntityManagerFactory(EntityManagerFactory emf, PersistenceUnitInfo pui) {
        super.postProcessEntityManagerFactory(emf, pui); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public PersistenceUnitInfo getPersistenceUnitInfo() {
        return super.getPersistenceUnitInfo(); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getPersistenceUnitName() {
        return super.getPersistenceUnitName(); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public DataSource getDataSource() {
        return super.getDataSource(); //To change body of generated methods, choose Tools | Templates.
    }
}
