/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.impl;

import java.util.Properties;
import javax.persistence.spi.PersistenceUnitInfo;
import javax.persistence.spi.PersistenceUnitTransactionType;
import org.hibernate.ejb.packaging.PersistenceMetadata;

/**
 *
 * <AUTHOR>
 */
public class PersistenceUnitManager implements org.springframework.orm.jpa.persistenceunit.PersistenceUnitManager {

    public PersistenceUnitInfo obtainDefaultPersistenceUnitInfo() throws IllegalStateException {
        PersistenceMetadata pm = new PersistenceMetadata();
        pm.setProvider("org.hibernate.ejb.HibernatePersistence");
        pm.setTransactionType(PersistenceUnitTransactionType.RESOURCE_LOCAL);
        Properties p = new Properties();
        p.setProperty("hibernate.archive.autodetetion", "class");
        p.setProperty("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        p.setProperty("hibernate.connection.driver_class", "org.postgresql.Driver");
        p.setProperty("hibernate.hbm2ddl.auto", "update");
        p.setProperty("hibernate.connection.url", "***************************************");
        p.setProperty("hibernate.connection.username", "postgres");
        p.setProperty("hibernate.connection.password", "pactodb");
        p.setProperty("hibernate.show_sql", "true");
        p.setProperty("hibernate.format_sql", "true");
        p.setProperty("hibernate.generate_statistics", "true");
        p.setProperty("hibernate.use_sql_comments", "false");
        p.setProperty("hibernate.pool_size", "10");
        p.setProperty("transaction.factory_class", "org.hibernate.transaction.JDBCTransactionFactory");
        pm.setProps(p);
        return (PersistenceUnitInfo) pm;
//        PersistenceManagerFactoryUtils.



    }

    public PersistenceUnitInfo obtainPersistenceUnitInfo(String string) throws IllegalArgumentException, IllegalStateException {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }
}
