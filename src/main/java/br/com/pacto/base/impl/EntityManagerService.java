/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.impl;

import br.com.pacto.base.jpa.MigracaoDadosJPAService;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.FlushModeType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class EntityManagerService {

    private final static String THERE_IS_NO_ENTITY_MANAGER_IN_SESSION = "Não há Entity Manager na sessão!";
    private final static String THERE_IS_NO_SESSION = "Nenhuma Sessão Persistente Existente!";
    // há uma sessão para cada Unidade de Persistência (Key)
    private final Map<String, ThreadLocal<EntityManager>> sessions = new HashMap<>();
    private final ThreadLocal<Usuario> users = new ThreadLocal<>();
    private final ThreadLocal<String> keys = new ThreadLocal<>();
    private final Map<String, String> status = new HashMap<>();
    @Autowired
    private MigracaoDadosJPAService migracaoService;

    /**
     * Retorna um Entity Manager disponível ou cria-o, se possível
     *
     * @return Uma instância de EntityManager
     */
    public EntityManager getEntityManager(String ctx) throws Exception {
        return  getEntityManager(ctx, true, false);
    }

    public EntityManager getEntityManager(String ctx, boolean esperar, boolean forceDDL) throws Exception {
        ThreadLocal<EntityManager> session = sessions.get(ctx);
        boolean updateBD = false;
        if (session == null) {
            session = new ThreadLocal<>();
            sessions.put(ctx, session);
            updateBD = true;
        }
        EntityManager em = session.get();
        try {
            if (em == null) {
                if (status.containsKey(ctx)) {
                    if(esperar){
                        Thread.sleep(3000);
                        return getEntityManager(ctx, false, false);
                    }
                    throw new ServiceException(String.format("Aguarde alguns instantes e tente novamente %s ... sua sessão está sendo iniciada...", ctx));
                }
                Uteis.logar(null, String.format("Creating entity manager %s", ctx));
                status.put(ctx, "Start");
                EntityManagerFactory emf = EntityManagerFactoryService.getEntityManagerFactory(ctx, forceDDL);
                em = emf.createEntityManager();
                em.setFlushMode(FlushModeType.COMMIT);
                session.set(em);
                if (updateBD) {
                    Uteis.logar(null, String.format("Updating Database %s...", ctx));
                    migracaoService.atualizar(ctx);
                    Uteis.logar(null, String.format("Database %s ready!", ctx));
                }
            }
        } finally {
            status.remove(ctx);
        }
        //
        return em;
    }

    public Usuario getUserFromCurrentThread() {
        return users.get();
    }

    public void putUserFromCurrentThread(final Usuario user) {
        users.set(user);
    }

    public void leaveAccessControlFromCurrentThread() {
        users.remove();
    }

    public String getContextFromCurrentThread() {
        String ctx = keys.get();
        return ctx;
    }

    public Map getStatus() {
        return status;
    }

    public void putContextFromCurrentThread(final String ctx) {
        keys.set(ctx);
    }

    private void log(final String msg) {
        Logger.getLogger(EntityManagerService.class.getName()).log(Level.INFO, msg);
    }

    /**
     * Obtem o entity manager para sessão de uma Unidade de Persistência
     *
     * @return o entity manager para sessão
     */
    public EntityManager getEntityManagerForSession(
            String persistenceUnit) throws Exception {
        log("Pegando entity manager para sessão: " + persistenceUnit);
        ThreadLocal<EntityManager> session = sessions.get(persistenceUnit);
        checkSession(session);
        EntityManager entityManager = session.get();
        checkEntityManager(entityManager);
        return entityManager;
    }

    /**
     * Fecha entity manager e remove-o da sessão da Unidade de Persistência
     */
    public void close(String persistenceUnit) throws Exception {
        ThreadLocal<EntityManager> session = sessions.get(persistenceUnit);
        checkSession(session);
        EntityManager entityManager = session.get();
        if (entityManager != null) {
            if (entityManager.isOpen()) {
                /*Uteis.logar(null, "Fechando entity manager");*/
                entityManager.close();
            }
            /*Uteis.logar(null, "Removendo entity manager da sessão");*/
            session.set(null);
        }
    }

    public void closeAll() throws Exception {
        log("Fechando todos entity managers");
        for (String persistenceUnit : sessions.keySet()) {
            close(persistenceUnit);
        }
    }

    /**
     * Fecha silenciosamente o entity manager e a sessão session da Unidade de
     * Persistência
     */
    public void closeQuietly(String persistenceUnit) {
        try {
            close(persistenceUnit);
        } catch (Exception e) {
            log("Erro ao fechar silenciosamente Entity Manager: "
                    + e.getMessage());
        }
    }

    private void checkEntityManager(EntityManager entityManager) throws Exception {
        if (entityManager == null) {
            log(THERE_IS_NO_ENTITY_MANAGER_IN_SESSION);
            throw new Exception(THERE_IS_NO_ENTITY_MANAGER_IN_SESSION);
        }
    }

    private void checkSession(ThreadLocal<EntityManager> session) throws Exception {
        if (session == null) {
            log(THERE_IS_NO_SESSION);
            throw new Exception(THERE_IS_NO_SESSION);
        }
    }
}
