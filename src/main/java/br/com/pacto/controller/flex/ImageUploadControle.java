/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.flex;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import java.io.File;
import org.apache.commons.io.FileUtils;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR>
 */
public class ImageUploadControle {

    private String nome = "Upload foto";

    public ImageUploadControle() {
    }

    public String doUpload(byte[] bytes, String idObjeto, String key) throws Exception {
        return doUpload(bytes, idObjeto, key, null);
    }

    public String doUpload(byte[] bytes, String idObjeto, String key, String tipoDoc) throws Exception {
        if (bytes.length == 0) {
            throw new Exception("Erro: Arquivo vazio.");
        }
        Uteis.logar(null, String.format("Pegou a foto da empresa %s, pessoa %s, tamanho -> %s bytes",
                key, idObjeto, bytes.length));
        String retorno = "OK";
        try {
            MidiaEntidadeEnum entidade = MidiaEntidadeEnum.FOTO_PESSOA;
            if (tipoDoc != null && !tipoDoc.isEmpty() && !tipoDoc.contains("treinoIndependente")) {
                entidade = MidiaEntidadeEnum.valueOf(tipoDoc);
            }
            if (Aplicacao.isTrue(Aplicacao.fotosParaNuvem) || (tipoDoc != null && tipoDoc.contains("treinoIndependente"))) {
                File tmp = File.createTempFile(String.format("zwphoto-%s-%s-%s", key, entidade, idObjeto), ".jpg");
                try {
                    Uteis.logar(null, "Criado arquivo temporario -> " + tmp.getAbsolutePath());
                    FileUtils.writeByteArrayToFile(tmp, bytes);
                    final String gencode = MidiaService.getInstance().uploadObject(key, entidade, idObjeto, tmp);
                    retorno = gencode;                    
                } finally {
                    tmp.delete();
                }
            }
        } finally {
            //
        }
        return retorno;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}