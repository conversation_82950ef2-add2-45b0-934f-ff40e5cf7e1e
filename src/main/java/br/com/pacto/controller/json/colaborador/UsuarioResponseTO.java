package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * paulo 19/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioResponseTO {

    private Integer id;
    private String nome;
    private String appUserName;

    public UsuarioResponseTO(Usuario usuario) {
        this.id          = usuario.getCodigo();
        this.nome        = usuario.getProfessor().getPessoa().getNome();
        this.appUserName = usuario.getUserName();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }
}
