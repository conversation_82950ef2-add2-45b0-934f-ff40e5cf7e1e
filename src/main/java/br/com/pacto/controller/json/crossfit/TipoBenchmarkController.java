package br.com.pacto.controller.json.crossfit;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.anamnese.AnamneseController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.benchmark.TipoBenchmarkService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 23/08/2018.
 */
@Controller
@RequestMapping("/psec/tipos-benchmark")
public class TipoBenchmarkController {

    private TipoBenchmarkService tipoBenchmarkService;

    @Autowired
    public TipoBenchmarkController(TipoBenchmarkService tipoBenchmarkService){
        Assert.notNull(tipoBenchmarkService, "O serviço de tipo Benchmark não foi injetado corretamente");
        this.tipoBenchmarkService = tipoBenchmarkService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoBenchmark(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroTipoBenchmarkJSON filtroTipoBenchmarkJSON = new FiltroTipoBenchmarkJSON(filtros);
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultarTipoBenchmark(filtroTipoBenchmarkJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao consultar os tipos de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os tipos de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirBenchmark(@RequestBody TipoBenchmarkTO tipoBenchmarkTO) {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.inserir(tipoBenchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTipoBenchmark(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(tipoBenchmarkService.consultar(id));
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o tipo de Benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarTipoBenchmark(@PathVariable("id") final Integer id,
                                                                    @RequestBody TipoBenchmarkTO tipoBenchmarkTO) {
        try {
            tipoBenchmarkTO.setId(id);
            return ResponseEntityFactory.ok(tipoBenchmarkService.alterar(tipoBenchmarkTO));

        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TIPO_BENCHMARK)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirTipoBenchmark(@PathVariable("id") final Integer id){
        try {
            tipoBenchmarkService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TipoBenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir tipo de Benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }



}
