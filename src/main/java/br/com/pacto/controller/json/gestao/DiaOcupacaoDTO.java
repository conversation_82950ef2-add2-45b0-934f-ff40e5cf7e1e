package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DiaOcupacaoDTO {

    private Integer manha = 0;
    private Integer tarde = 0;
    private Integer noite = 0;

    public DiaOcupacaoDTO() {
    }

    public DiaOcupacaoDTO(Map<String, Integer> mapa) {
        this.manha = mapa.get("manha");
        this.tarde = mapa.get("tarde");
        this.noite = mapa.get("noite");
    }
    public DiaOcupacaoDTO(Integer manha, Integer tarde, Integer noite) {
        this.manha = manha;
        this.tarde = tarde;
        this.noite = noite;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }
}
