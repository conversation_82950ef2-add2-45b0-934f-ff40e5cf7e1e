package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BICrossfitDTO {

    private Integer numeroDealunos;
    private Integer resultadosLancados;
    private Integer numeroAgendamentos;
    private Integer faltasNosUltimos7Dias;
    private OcupacaoDTO ocupacao;
    private List<FrequenciaProfessorDTO> frequenciaPorProfessor;

    public Integer getNumeroDealunos() {
        return numeroDealunos;
    }

    public void setNumeroDealunos(Integer numeroDealunos) {
        this.numeroDealunos = numeroDealunos;
    }

    public Integer getResultadosLancados() {
        return resultadosLancados;
    }

    public void setResultadosLancados(Integer resultadosLancados) {
        this.resultadosLancados = resultadosLancados;
    }

    public Integer getNumeroAgendamentos() {
        return numeroAgendamentos;
    }

    public void setNumeroAgendamentos(Integer numeroAgendamentos) {
        this.numeroAgendamentos = numeroAgendamentos;
    }

    public Integer getFaltasNosUltimos7Dias() {
        return faltasNosUltimos7Dias;
    }

    public void setFaltasNosUltimos7Dias(Integer faltasNosUltimos7Dias) {
        this.faltasNosUltimos7Dias = faltasNosUltimos7Dias;
    }

    public OcupacaoDTO getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(OcupacaoDTO ocupacao) {
        this.ocupacao = ocupacao;
    }

    public List<FrequenciaProfessorDTO> getFrequenciaPorProfessor() {
        return frequenciaPorProfessor;
    }

    public void setFrequenciaPorProfessor(List<FrequenciaProfessorDTO> frequenciaPorProfessor) {
        this.frequenciaPorProfessor = frequenciaPorProfessor;
    }
}
