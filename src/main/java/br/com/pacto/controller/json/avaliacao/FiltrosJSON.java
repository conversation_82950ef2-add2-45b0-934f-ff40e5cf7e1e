package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltrosJSON extends SuperJSON {

    private Boolean nome = false;
    private String paramentro;
    private List<String> paramAtivos;

    public FiltrosJSON(JSONObject filters) throws JSONException {
        this.nome = nome;
        if (filters != null) {
            this.paramentro = filters.optString("quicksearchValue");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            JSONArray status = filters.optJSONArray("status");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }
            if(status != null){
                paramAtivos = new ArrayList<String>();
                for(int i = 0; i< status.length(); i++){
                    paramAtivos.add(status.getString(i));
                }
            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParamentro() {
        return paramentro;
    }

    public void setParamentro(String paramentro) {
        this.paramentro = paramentro;
    }

    public List<String> getParamAtivos() {
        return paramAtivos;
    }

    public void setParamAtivos(List<String> paramAtivos) {
        this.paramAtivos = paramAtivos;
    }
}
