package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.Telefone;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Siqueira 01/04/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoOlympiaDTO {
    private String nome;
    private SexoEnum sexo;
    private List<String> emails = new ArrayList<>();
    private List<TelefoneDTO> fones = new ArrayList<>();
    private Long dataNascimento;
    private String codigoExterno;

    public AlunoOlympiaDTO() {
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public Long getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Long dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }
}
