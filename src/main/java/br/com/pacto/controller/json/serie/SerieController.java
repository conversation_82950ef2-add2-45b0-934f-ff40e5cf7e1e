package br.com.pacto.controller.json.serie;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.SerieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by paulo on 22/11/2018
 */
@Controller
@RequestMapping("/psec/series")
public class SerieController {

    private final SerieService serieService;

    @Autowired
    public SerieController(SerieService serieService) {
        Assert.notNull(serieService, "O serviço de serie não foi injetado corretamente");
        this.serieService = serieService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarSerie(
            @RequestBody SerieEndpointTO serieEndpointTO
    ) {
        try {
            return ResponseEntityFactory.ok(serieService.cadastrarSerie(serieEndpointTO));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar uma nova serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerSerie(
            @PathVariable(value = "id") Integer id
    ) {
        try {
            serieService.removerSerie(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarSerie(
            @PathVariable(value = "id") Integer id,
            @RequestBody SerieEndpointTO serieEndpointTO
    ) {
        try {
            serieEndpointTO.setId(id);
            return ResponseEntityFactory.ok(serieService.editarSerie(serieEndpointTO));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}/serieRealizada", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarSerieRealizada(
            @PathVariable(value = "id") Integer id
    ) {
        try {
            SerieEndpointTO serie = new SerieEndpointTO();
            serie.setId(id);
            serie.setSerieRealizada(true);
            return ResponseEntityFactory.ok(serieService.editarSerie(serie));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}/espelhar", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> espelharSerie(
            @PathVariable(value = "id") Integer id
    ) {
        try {
            serieService.espelharSerie(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar espelhar serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
