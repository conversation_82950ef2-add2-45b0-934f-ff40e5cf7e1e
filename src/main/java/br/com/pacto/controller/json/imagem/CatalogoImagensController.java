package br.com.pacto.controller.json.imagem;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.animacao.AnimacaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 29/08/2018.
 */
@Controller
@RequestMapping("/psec/imagens-catalogo")
public class CatalogoImagensController {

    private final AnimacaoService animacaoService;

    @Autowired
    public CatalogoImagensController(AnimacaoService animacaoService){
        Assert.notNull(animacaoService, "O serviço de animação não foi injetado corretamente");
        this.animacaoService = animacaoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.IMAGENS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarImagens() {
        try {
            return ResponseEntityFactory.ok(animacaoService.listarImagens());
        } catch (ServiceException e) {
            Logger.getLogger(CatalogoImagensController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as imagens", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
