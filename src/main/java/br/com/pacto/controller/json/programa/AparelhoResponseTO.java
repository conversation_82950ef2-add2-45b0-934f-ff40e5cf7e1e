package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoResponseTO {

    private Integer id;
    private String nome;
    private String sigla;
    private String icone;
    private Boolean usarEmReservaEquipamentos;
    private List<AparelhoAjusteResponseTO> ajustes = new ArrayList<>();
    private List<AtividadeAparelhoResponseTO> atividades = new ArrayList<>();
    private String sensorSelfloops;

    public AparelhoResponseTO(){

    }

    public AparelhoResponseTO(Aparelho aparelho){
        this.id = aparelho.getCodigo();
        this.nome = aparelho.getNome();
        this.sensorSelfloops = aparelho.getSensorSelfloops();
        for (AparelhoAjuste aparelhoAjuste: aparelho.getAjustes()){
            getAjustes().add(new AparelhoAjusteResponseTO(aparelhoAjuste));
        }
        for (AtividadeAparelho atividadeAparelho: aparelho.getAtividades()){
            getAtividades().add(new AtividadeAparelhoResponseTO(atividadeAparelho.getAtividade()));
        }
        this.sigla = aparelho.getSigla();
        this.icone = aparelho.getIcone();
        this.usarEmReservaEquipamentos = aparelho.getUsarEmReservaEquipamentos();
    }

    public AparelhoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AparelhoAjusteResponseTO> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AparelhoAjusteResponseTO> ajustes) {
        this.ajustes = ajustes;
    }

    public List<AtividadeAparelhoResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAparelhoResponseTO> atividades) {
        this.atividades = atividades;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Boolean getUsarEmReservaEquipamentos() {
        return usarEmReservaEquipamentos;
    }

    public void setUsarEmReservaEquipamentos(Boolean usarEmReservaEquipamentos) {
        this.usarEmReservaEquipamentos = usarEmReservaEquipamentos;
    }

    public String getSensorSelfloops() {
        return sensorSelfloops;
    }

    public void setSensorSelfloops(String sensorSelfloops) {
        this.sensorSelfloops = sensorSelfloops;
    }
}
