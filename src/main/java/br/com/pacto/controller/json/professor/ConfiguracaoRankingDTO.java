package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo 28/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoRankingDTO {

    private Integer id;
    private IndicadorDashboardEnum indicador;
    private Boolean operacao = true;
    private Boolean ativa = false;
    private Double peso;
    private String pontuacao;

    public ConfiguracaoRankingDTO() {
    }

    public ConfiguracaoRankingDTO(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
        this.operacao = true;
        this.ativa = false;
        this.peso = 0.0;
        this.pontuacao = "0";
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IndicadorDashboardEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
    }

    public Boolean getOperacao() {
        return operacao;
    }

    public void setOperacao(Boolean operacao) {
        this.operacao = operacao;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getPontuacao() {
        return pontuacao;
    }

    public void setPontuacao(String pontuacao) {
        this.pontuacao = pontuacao;
    }

    public String getIndicadorSort(){
        return indicador == null ? "" : indicador.getLabelSort();
    }
}
