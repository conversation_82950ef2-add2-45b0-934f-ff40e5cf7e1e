package br.com.pacto.controller.json.eventoUsuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.aluno.AlunoController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.eventoUsuario.EventoUsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> 19/02/2019
 */
@Controller
@RequestMapping("/psec/eventos-usuario")
public class EventoUsuarioController {

    private final EventoUsuarioService eventoUsuarioService;

    @Autowired
    public EventoUsuarioController(EventoUsuarioService eventoUsuarioService) {
        Assert.notNull(eventoUsuarioService, "O serviço de evento usuário não foi injetado corretamente");
        this.eventoUsuarioService = eventoUsuarioService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosEventosUsuario () {
        try {
            return ResponseEntityFactory.ok(eventoUsuarioService.obterTodosEventoUsuario());
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos eventos dos usuários", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarEventosUsuario (@PathVariable("id") String eventoId, @RequestBody EventoUsuario eventoUsuario) {
        try {
            eventoUsuario.setCodigo(eventoId);
            return ResponseEntityFactory.ok(eventoUsuarioService.atualizarEventoUsuario(eventoUsuario));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar evento do usuario", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
