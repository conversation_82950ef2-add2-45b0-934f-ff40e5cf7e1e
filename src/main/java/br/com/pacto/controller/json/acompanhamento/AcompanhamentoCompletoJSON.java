/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AcompanhamentoCompletoJSON extends SuperJSON {

    private String data;
    private AcompanhamentoDadosJSON dados;
    private List<DesempenhoJSON> desempenho = new ArrayList<DesempenhoJSON>();

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public AcompanhamentoDadosJSON getDados() {
        return dados;
    }

    public void setDados(AcompanhamentoDadosJSON dados) {
        this.dados = dados;
    }

    public List<DesempenhoJSON> getDesempenho() {
        return desempenho;
    }

    public void setDesempenho(List<DesempenhoJSON> desempenho) {
        this.desempenho = desempenho;
    }
}
