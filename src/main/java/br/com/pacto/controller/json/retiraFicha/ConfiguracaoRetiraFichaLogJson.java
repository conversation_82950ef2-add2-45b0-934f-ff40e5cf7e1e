package br.com.pacto.controller.json.retiraFicha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoRetiraFichaLogJson extends SuperJSON {


    private Integer codigo;

    private ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson;

    private Date dataAlteraco;

    private String nomeUsuario;

    private Integer codigoUsuario;


    public ConfiguracaoRetiraFichaLogJson() {
    }


    public ConfiguracaoRetiraFichaLogJson(ConfiguracaoRetiraFichaLog configuracaoRetiraFichaLog) throws Exception {
        this.codigo = configuracaoRetiraFichaLog.getCodigo();

        JSONObject jsonObject = new JSONObject(configuracaoRetiraFichaLog.getConfiguracaoRetiraFicha());
        this.configuracaoRetiraFichaJson = JSONMapper.getObject(jsonObject, ConfiguracaoRetiraFichaJson.class);

        this.dataAlteraco = configuracaoRetiraFichaLog.getDataAlteracao();
        this.nomeUsuario = configuracaoRetiraFichaLog.getUsuario().getNome();
        this.codigoUsuario = configuracaoRetiraFichaLog.getUsuario().getCodigo();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAlteraco() {
        return dataAlteraco;
    }

    public void setDataAlteraco(Date dataAlteraco) {
        this.dataAlteraco = dataAlteraco;
    }

    public ConfiguracaoRetiraFichaJson getConfiguracaoRetiraFichaJson() {
        return configuracaoRetiraFichaJson;
    }

    public void setConfiguracaoRetiraFichaJson(ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson) {
        this.configuracaoRetiraFichaJson = configuracaoRetiraFichaJson;
    }
    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }
}
