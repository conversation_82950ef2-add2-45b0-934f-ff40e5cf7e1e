package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoSimplesDTO {

    private Integer id;
    private String nome;
    private NivelAlunoResponseTO nivel;
    private Integer matricula;
    private String situacao;
    private Date terminoContrato;
    private Date terminoProgramaVigente;
    private List<String> emails = new ArrayList<>();
    private List<TelefoneDTO> fones = new ArrayList<>();
    private String dataAcompanhamento;

    public AlunoSimplesDTO() {

    }

    public AlunoSimplesDTO(ClienteSintetico clienteSintetico) {
        this.id = clienteSintetico.getCodigo();
        this.matricula = clienteSintetico.getMatricula();
        this.nome = clienteSintetico.getNome();
        this.terminoContrato = clienteSintetico.getDataFimPeriodoAcesso();
        if (clienteSintetico.getProgramaVigente() != null) {
            Date dataterminoPrograma = Uteis.getDataComHoraZerada(clienteSintetico.getProgramaVigente().getDataTerminoPrevisto());
            this.terminoProgramaVigente = dataterminoPrograma;
        }
        this.situacao = clienteSintetico.getSituacao();
        if(clienteSintetico.getDia()!=null){
            this.dataAcompanhamento = Uteis.getDataComHHMM(clienteSintetico.getDia());
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public NivelAlunoResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelAlunoResponseTO nivel) {
        this.nivel = nivel;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getTerminoContrato() {
        return terminoContrato;
    }

    public void setTerminoContrato(Date terminoContrato) {
        this.terminoContrato = terminoContrato;
    }

    public Date getTerminoProgramaVigente() {
        return terminoProgramaVigente;
    }

    public void setTerminoProgramaVigente(Date terminoProgramaVigente) {
        this.terminoProgramaVigente = terminoProgramaVigente;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public String getDataAcompanhamento() {
        return dataAcompanhamento;
    }

    public void setDataAcompanhamento(String dataAcompanhamento) {
        this.dataAcompanhamento = dataAcompanhamento;
    }
}
