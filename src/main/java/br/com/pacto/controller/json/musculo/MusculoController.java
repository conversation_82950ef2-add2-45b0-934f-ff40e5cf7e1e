package br.com.pacto.controller.json.musculo;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.musculo.MusculoTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.controller.json.nivel.NivelController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.musculo.MusculoService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/musculos")
public class MusculoController {

    private final MusculoService musculoService;

    @Autowired
    public MusculoController(MusculoService musculoService){
        Assert.notNull(musculoService, "O serviço de Musculo não foi injetado corretamente");
        this.musculoService = musculoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(musculoService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os músculos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarMusculos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroNivelJSON filtroNivelJSON = new FiltroNivelJSON(filtros);
            return ResponseEntityFactory.ok(musculoService.consultarMusculos(filtroNivelJSON, paginadorDTO),paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os níveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarMusculo(@PathVariable("id") Integer id){
        try {
            return ResponseEntityFactory.ok(musculoService.consultarMusculo(id));
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o músculo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirMusculo(@RequestBody MusculoTO musculoTO) {
        try {
            return ResponseEntityFactory.ok(musculoService.inserir(musculoTO));
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir músculo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarMusculo(@PathVariable("id") final Integer id,
                                                                    @RequestBody MusculoTO musculoTO) {
        try {
            return ResponseEntityFactory.ok(musculoService.alterar(id, musculoTO));

        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar músculo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirMusculo(@PathVariable("id") final Integer id){
        try {
            musculoService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir músculo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
