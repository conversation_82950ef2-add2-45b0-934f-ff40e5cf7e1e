package br.com.pacto.controller.json.parceiros;

import br.com.pacto.bean.parceiro.Parceiro;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroResponseTO {

    private Integer id;
    private String nome;
    private String uri;
    private Boolean situacao;

    public ParceiroResponseTO(Parceiro parceiro) {
        this.id = parceiro.getCodigo();
        this.uri = parceiro.getUrlImagem();
        this.situacao = parceiro.getSituacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }
}
