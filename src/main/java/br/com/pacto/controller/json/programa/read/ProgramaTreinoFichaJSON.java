/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaTreinoFichaJSON extends SuperJSON {

    private Integer codigo;
    private Integer programa;
    private Integer ficha;
    private String nomeFicha;
    private Integer tipoExecucao;
    private List<String> diaSemana = new ArrayList<String>();//<List>DiasSemana.class    
    private Integer numeroAtividades;
    private Date ultimaExecucao;

    public ProgramaTreinoFichaJSON() {
    }

    public ProgramaTreinoFichaJSON(ProgramaTreinoFicha programaTreinoFicha) {
        this.codigo = programaTreinoFicha.getCodigo();
        this.programa =  programaTreinoFicha.getPrograma() != null ? programaTreinoFicha.getPrograma().getCodigo() : null;
        this.ficha = programaTreinoFicha.getFicha() != null ? programaTreinoFicha.getFicha().getCodigo() : null;
        this.tipoExecucao = programaTreinoFicha.getTipoExecucao() != null ? programaTreinoFicha.getTipoExecucao().getId() : null;
        this.nomeFicha = programaTreinoFicha.getFicha() != null ? programaTreinoFicha.getFicha().getNome() : null;
        this.diaSemana = programaTreinoFicha.getDiaSemana();
        this.numeroAtividades = programaTreinoFicha.getFicha() == null ? 0 : programaTreinoFicha.getFicha().getAtividades() == null ? 0 : programaTreinoFicha.getFicha().getAtividades().size();
        this.ultimaExecucao = programaTreinoFicha.getFicha() == null ? null : programaTreinoFicha.getFicha().getUltimaExecucao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPrograma() {
        return programa;
    }

    public void setPrograma(Integer programa) {
        this.programa = programa;
    }

    public Integer getFicha() {
        return ficha;
    }

    public void setFicha(Integer ficha) {
        this.ficha = ficha;
    }

    public Integer getTipoExecucao() {
        return tipoExecucao;
    }

    public void setTipoExecucao(Integer tipoExecucao) {
        this.tipoExecucao = tipoExecucao;
    }

    public List<String> getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(List<String> diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getNomeFicha() {
        return nomeFicha;
    }

    public void setNomeFicha(String nomeFicha) {
        this.nomeFicha = nomeFicha;
    }

    public Integer getNumeroAtividades() {
        return numeroAtividades;
    }

    public void setNumeroAtividades(Integer numeroAtividades) {
        this.numeroAtividades = numeroAtividades;
    }

    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }
}
