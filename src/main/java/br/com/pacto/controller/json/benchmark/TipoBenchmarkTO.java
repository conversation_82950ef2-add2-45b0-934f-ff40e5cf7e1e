package br.com.pacto.controller.json.benchmark;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by joa<PERSON> moit<PERSON> on 27/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TipoBenchmarkTO {

    private Integer id;
    private String nome;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
