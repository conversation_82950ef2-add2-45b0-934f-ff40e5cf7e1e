package br.com.pacto.controller.json.colaborador.dep;

import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import br.com.pacto.bean.pessoa.SexoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ColaboradorTO {

    private String nome;
    private SituacaoColaboradorEnum situacao;
    private Date dataNascimento;
    private SexoEnum sexo;
    private List<EmailTO> emails;
    private List<FoneTO> fones;
    private Boolean usarApp;
    private String appUserName;
    private String appPassword;
    private TipoUsuarioColaboradorEnum tipoUsuario;
    private PerfilUsuarioTO perfilUsuario;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<EmailTO> getEmails() {
        return emails;
    }

    public void setEmails(List<EmailTO> emails) {
        this.emails = emails;
    }

    public List<FoneTO> getFones() {
        return fones;
    }

    public void setFones(List<FoneTO> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public PerfilUsuarioTO getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(PerfilUsuarioTO perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }
}
