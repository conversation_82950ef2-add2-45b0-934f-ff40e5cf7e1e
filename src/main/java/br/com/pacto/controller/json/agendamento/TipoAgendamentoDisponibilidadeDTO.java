package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class TipoAgendamentoDisponibilidadeDTO {

    private Integer tipoAgendamentoId;
    private List<AgendamentoDisponibilidadeDTO> disponibilidades;

    public TipoAgendamentoDisponibilidadeDTO(Integer tipoAgendamentoId, List<AgendamentoDisponibilidadeDTO> disponibilidades) {
        this.tipoAgendamentoId = tipoAgendamentoId;
        this.disponibilidades = disponibilidades;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public List<AgendamentoDisponibilidadeDTO> getDisponibilidades() {
        return disponibilidades;
    }

    public void setDisponibilidades(List<AgendamentoDisponibilidadeDTO> disponibilidades) {
        this.disponibilidades = disponibilidades;
    }
}
