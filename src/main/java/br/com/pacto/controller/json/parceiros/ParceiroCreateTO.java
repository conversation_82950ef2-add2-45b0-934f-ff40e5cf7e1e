package br.com.pacto.controller.json.parceiros;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by paulo on 15/10/2018.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroCreateTO {

    private List<ParceiroTO> parceiros;
    private List<Integer> parceirosExcluidosIds;

    public List<ParceiroTO> getParceiros() {
        return parceiros;
    }

    public void setParceiros(List<ParceiroTO> parceiros) {
        this.parceiros = parceiros;
    }

    public List<Integer> getParceirosExcluidosIds() {
        return parceirosExcluidosIds;
    }

    public void setParceirosExcluidosIds(List<Integer> parceirosExcluidosIds) {
        this.parceirosExcluidosIds = parceirosExcluidosIds;
    }
}
