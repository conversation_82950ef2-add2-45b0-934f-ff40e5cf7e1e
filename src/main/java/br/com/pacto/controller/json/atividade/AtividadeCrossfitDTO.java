package br.com.pacto.controller.json.atividade;

import br.com.pacto.bean.atividade.*;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * <AUTHOR> 31/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeCrossfitDTO {

    private Integer id;
    private String nome;
    private CategoriaAtividadeWodEnum categoria;
    private UnidadeMedidaEnum unidadeMedida;
    private boolean ativo;
    private List<AtividadeEmpresaTO> empresas;
    private String descricao;
    private String videoUri;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CategoriaAtividadeWodEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaAtividadeWodEnum categoria) {
        this.categoria = categoria;
    }

    public UnidadeMedidaEnum getUnidadeMedida() {
        return unidadeMedida;
    }

    public void setUnidadeMedida(UnidadeMedidaEnum unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public List<AtividadeEmpresaTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaTO> empresas) {
        this.empresas = empresas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }
}
