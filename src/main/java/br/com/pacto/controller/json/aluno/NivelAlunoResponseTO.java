package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.nivel.Nivel;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NivelAlunoResponseTO {

    private Integer id;
    private String nome;

    public NivelAlunoResponseTO(Nivel nivel) {
        this.id = nivel.getCodigo();
        this.nome = nivel.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
