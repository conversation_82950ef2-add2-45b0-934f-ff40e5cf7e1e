/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class AcompanhamentoDadosJSON extends SuperJSON {

    private Integer diasProgramaAtual;
    private Integer diasProgramaAtualTotal;
    private Integer diasPresencaSemana;
    private Integer expectativaSemana;
    private Integer faltasNaSemana;
    private Integer diasDeTreinamento;
    private Integer programas;
    private Integer atividades;

    public Integer getDiasProgramaAtual() {
        return diasProgramaAtual;
    }

    public void setDiasProgramaAtual(Integer diasProgramaAtual) {
        this.diasProgramaAtual = diasProgramaAtual;
    }

    public Integer getDiasPresencaSemana() {
        return diasPresencaSemana;
    }

    public void setDiasPresencaSemana(Integer diasPresencaSemana) {
        this.diasPresencaSemana = diasPresencaSemana;
    }

    public Integer getExpectativaSemana() {
        return expectativaSemana;
    }

    public void setExpectativaSemana(Integer expectativaSemana) {
        this.expectativaSemana = expectativaSemana;
    }

    public Integer getDiasDeTreinamento() {
        return diasDeTreinamento;
    }

    public void setDiasDeTreinamento(Integer diasDeTreinamento) {
        this.diasDeTreinamento = diasDeTreinamento;
    }

    public Integer getProgramas() {
        return programas;
    }

    public void setProgramas(Integer programas) {
        this.programas = programas;
    }

    public Integer getAtividades() {
        return atividades;
    }

    public void setAtividades(Integer atividades) {
        this.atividades = atividades;
    }

    public Integer getDiasProgramaAtualTotal() {
        return diasProgramaAtualTotal;
    }

    public void setDiasProgramaAtualTotal(Integer diasProgramaAtualTotal) {
        this.diasProgramaAtualTotal = diasProgramaAtualTotal;
    }

    public Integer getFaltasNaSemana() {
        return faltasNaSemana;
    }

    public void setFaltasNaSemana(Integer faltasNaSemana) {
        this.faltasNaSemana = faltasNaSemana;
    }
}
