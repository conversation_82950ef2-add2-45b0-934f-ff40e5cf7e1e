package br.com.pacto.controller.json.modalidade;


import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoresResponseTO {

    private Integer id;
    private String nome;
    private String valor;

    public CoresResponseTO(PaletaCoresEnum cor) {
        this.id = cor.getId();
        this.nome = cor.getDescricao();
        this.valor = cor.getCor();
    }
    public CoresResponseTO(Integer id, String nome, String valor) {
        this.id = id;
        this.nome = nome;
        this.valor = valor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}
