package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.anamnese.AgrupamentoAvaliacaoIntegradaEnum;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.ViewUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class AvaliacaoIntegradaJSON {

    private Integer codigo;
    private Date dataAvaliacao;
    private Date dataProxima;
    private List<PerguntaRespondidaJSON> geral = new ArrayList<PerguntaRespondidaJSON>();
    private List<PerguntaRespondidaJSON> qualidadeVida = new ArrayList<PerguntaRespondidaJSON>();
    private List<PerguntaRespondidaJSON> qualidadeMovimento = new ArrayList<PerguntaRespondidaJSON>();
    private List<ResultadoMovimento3DJSON> mobilidade = new ArrayList<ResultadoMovimento3DJSON>();
    private List<ResultadoMovimento3DJSON> estabilidadeControle = new ArrayList<ResultadoMovimento3DJSON>();
    private String[] placares;
    private Integer somaMobilidadeEsquerda;
    private Integer somaMobilidadeDireita;
    private Double mediaMobilidadeEsquerda;
    private Double mediaMobilidadeDireita;

    private Integer somaEstabilidadeEsquerda;
    private Integer somaEstabilidadeDireita;
    private Double mediaEstabilidadeEsquerda;
    private Double mediaEstabilidadeDireita;

    private String resultadoQualidadeVida;
    private String resultadoQualidadeMovimento;

    public AvaliacaoIntegradaJSON(ItemAvaliacaoFisica item, Map<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> mapPerguntas,
                                  List<Movimento3D> mobilidade3d, List<Movimento3D> estabilidade3d, ViewUtils vu) {
        this.codigo = item.getCodigo();
        this.dataAvaliacao = item.getDataResposta();
        for (AgrupamentoAvaliacaoIntegradaEnum ag : mapPerguntas.keySet()) {
            for (PerguntaAnamnese p : mapPerguntas.get(ag)) {
                PerguntaRespondidaJSON pjson = new PerguntaRespondidaJSON(p);
                switch (ag) {
                    case GERAL:
                        geral.add(pjson);
                        break;
                    case QUALIDADE_MOVIMENTO:
                        qualidadeMovimento.add(pjson);
                        break;
                    case QUALIDADE_VIDA:
                        qualidadeVida.add(pjson);
                        break;
                }
            }

            this.somaMobilidadeEsquerda = item.getSomaMobilidadeEsq();
            this.somaMobilidadeDireita = item.getSomaMobilidadeDir();
            this.mediaMobilidadeEsquerda = item.getMediaMobilidadeEsq();
            this.mediaMobilidadeDireita = item.getMediaMobilidadeDir();

            this.somaEstabilidadeEsquerda = item.getSomaEstabilidadeEsq();
            this.somaEstabilidadeDireita = item.getSomaEstabilidadeDir();
            this.mediaEstabilidadeEsquerda = item.getMediaEstabilidadeEsq();
            this.mediaEstabilidadeDireita = item.getMediaEstabilidadeDir();
        }

        placares = new String[]{
                vu.getLabel("placar0"),
                vu.getLabel("placar1"),
                vu.getLabel("placar2"),
                vu.getLabel("placar3")};

        for (Movimento3D m : mobilidade3d) {
            mobilidade.add(new ResultadoMovimento3DJSON(m, vu));
        }

        for (Movimento3D m : estabilidade3d) {
            estabilidadeControle.add(new ResultadoMovimento3DJSON(m, vu));
        }

        resultadoQualidadeMovimento = vu.getLabel(item.getQualidadeMovimento().name()) + "(" + item.getSomaQualidadeMovimento() + " pts)";
        resultadoQualidadeVida = vu.getLabel(item.getQualidadeVida().name()) + "(" + item.getSomaQualidadeVida() + " pts)";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<PerguntaRespondidaJSON> getGeral() {
        return geral;
    }

    public void setGeral(List<PerguntaRespondidaJSON> geral) {
        this.geral = geral;
    }

    public List<PerguntaRespondidaJSON> getQualidadeVida() {
        return qualidadeVida;
    }

    public void setQualidadeVida(List<PerguntaRespondidaJSON> qualidadeVida) {
        this.qualidadeVida = qualidadeVida;
    }

    public List<PerguntaRespondidaJSON> getQualidadeMovimento() {
        return qualidadeMovimento;
    }

    public void setQualidadeMovimento(List<PerguntaRespondidaJSON> qualidadeMovimento) {
        this.qualidadeMovimento = qualidadeMovimento;
    }

    public String getDataAvaliacao() {
        return dataAvaliacao == null ? "" : Uteis.getData(dataAvaliacao);
    }

    public Long getDataAvaliacaoLong(){
        return dataAvaliacao == null ? null : dataAvaliacao.getTime();
    }

    public void setDataAvaliacao(Date dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public String getDataProxima() {
        return dataProxima == null ? "" : Uteis.getData(dataProxima);
    }

    public Long getDataProximaLong(){
        return dataProxima == null ? null : dataProxima.getTime();
    }

    public void setDataProxima(Date dataProxima) {
        this.dataProxima = dataProxima;
    }

    public String[] getPlacares() {
        return placares;
    }

    public void setPlacares(String[] placares) {
        this.placares = placares;
    }

    public Integer getSomaMobilidadeEsquerda() {
        return somaMobilidadeEsquerda;
    }

    public void setSomaMobilidadeEsquerda(Integer somaMobilidadeEsquerda) {
        this.somaMobilidadeEsquerda = somaMobilidadeEsquerda;
    }

    public Integer getSomaMobilidadeDireita() {
        return somaMobilidadeDireita;
    }

    public void setSomaMobilidadeDireita(Integer somaMobilidadeDireita) {
        this.somaMobilidadeDireita = somaMobilidadeDireita;
    }

    public Double getMediaMobilidadeEsquerda() {
        return mediaMobilidadeEsquerda;
    }

    public void setMediaMobilidadeEsquerda(Double mediaMobilidadeEsquerda) {
        this.mediaMobilidadeEsquerda = mediaMobilidadeEsquerda;
    }

    public Double getMediaMobilidadeDireita() {
        return mediaMobilidadeDireita;
    }

    public void setMediaMobilidadeDireita(Double mediaMobilidadeDireita) {
        this.mediaMobilidadeDireita = mediaMobilidadeDireita;
    }

    public Integer getSomaEstabilidadeEsquerda() {
        return somaEstabilidadeEsquerda;
    }

    public void setSomaEstabilidadeEsquerda(Integer somaEstabilidadeEsquerda) {
        this.somaEstabilidadeEsquerda = somaEstabilidadeEsquerda;
    }

    public Integer getSomaEstabilidadeDireita() {
        return somaEstabilidadeDireita;
    }

    public void setSomaEstabilidadeDireita(Integer somaEstabilidadeDireita) {
        this.somaEstabilidadeDireita = somaEstabilidadeDireita;
    }

    public Double getMediaEstabilidadeEsquerda() {
        return mediaEstabilidadeEsquerda;
    }

    public void setMediaEstabilidadeEsquerda(Double mediaEstabilidadeEsquerda) {
        this.mediaEstabilidadeEsquerda = mediaEstabilidadeEsquerda;
    }

    public Double getMediaEstabilidadeDireita() {
        return mediaEstabilidadeDireita;
    }

    public void setMediaEstabilidadeDireita(Double mediaEstabilidadeDireita) {
        this.mediaEstabilidadeDireita = mediaEstabilidadeDireita;
    }

    public List<ResultadoMovimento3DJSON> getMobilidade() {
        return mobilidade;
    }

    public List<ResultadoMovimento3DJSON> getEstabilidadeControle() {
        return estabilidadeControle;
    }

    public void setEstabilidadeControle(List<ResultadoMovimento3DJSON> estabilidadeControle) {
        this.estabilidadeControle = estabilidadeControle;
    }

    public void setMobilidade(List<ResultadoMovimento3DJSON> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public String getResultadoQualidadeVida() {
        return resultadoQualidadeVida;
    }

    public void setResultadoQualidadeVida(String resultadoQualidadeVida) {
        this.resultadoQualidadeVida = resultadoQualidadeVida;
    }

    public String getResultadoQualidadeMovimento() {
        return resultadoQualidadeMovimento;
    }

    public void setResultadoQualidadeMovimento(String resultadoQualidadeMovimento) {
        this.resultadoQualidadeMovimento = resultadoQualidadeMovimento;
    }
}
