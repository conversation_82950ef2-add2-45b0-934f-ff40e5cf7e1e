package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;

/**
 * Created paulo 07/11/2018
 */
public class ComposicaoCorporalDTO {

    private Double massaMagra;
    private Double massaGorda;
    private Double pesoOsseo;
    private Double residuos;

    public ComposicaoCorporalDTO(AvaliacaoFisica avaliacaoFisica) {
        this.massaMagra = avaliacaoFisica.getMassaMagra();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
        this.pesoOsseo = avaliacaoFisica.getPesoOsseo();
        this.residuos = avaliacaoFisica.getResidual();
    }

    public Double getMassaMagra() {
        return massaMagra;
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }
}
