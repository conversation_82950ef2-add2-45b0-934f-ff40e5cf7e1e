package br.com.pacto.controller.json.crossfit;

import br.com.pacto.bean.wod.ComentarioWod;

import java.util.Date;

/**
 * Created by <PERSON><PERSON>
 */
public class ComentarioWodJSON {

    private Integer codigo;
    private String matricula;
    private String nome;
    private String urlFoto;
    private Integer codWod;
    private String comentario;
    private Date dataRegistro;
    private String dataRegistroApresentar;
    private Integer qtdLike;
    private boolean clienteDeuLike = false;
    private boolean podeExcluir = false;
    private Integer nivel;
    private String nivelApresentar;
    private Integer codUsuario;


    public ComentarioWodJSON(ComentarioWod comentarioWod) {
        this.setCodigo(comentarioWod.getCodigo());
        this.setCodWod(comentarioWod.getWod().getCodigo());
        this.setComentario(comentarioWod.getComentario());
        this.setDataRegistro(comentarioWod.getDataRegistro());
        this.setDataRegistroApresentar(comentarioWod.getDataRegistroApresentar());
        this.setNome(comentarioWod.getNome());
        this.setUrlFoto(comentarioWod.getUsuario().getFotoKeyApp());
        this.setQtdLike(comentarioWod.getQtdLike());
        this.setClienteDeuLike(comentarioWod.isClienteDeuLike());
        this.setPodeExcluir(comentarioWod.isPodeExcluir());
        this.setCodUsuario(comentarioWod.getUsuario().getCodigo());
        if (comentarioWod.getUsuario().getCliente() != null) {
            this.setMatricula(comentarioWod.getUsuario().getCliente().getMatriculaString());
        } else {
            this.setMatricula("");
        }
        if (comentarioWod.getNivelCrossfit() != null) {
            this.setNivel(comentarioWod.getNivelCrossfit().getId());
            this.setNivelApresentar(comentarioWod.getNivelCrossfit().getDescricao());
        }
    }

    public ComentarioWodJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodWod() {
        if (codWod == null) {
            codWod = 0;
        }
        return codWod;
    }

    public void setCodWod(Integer codWod) {
        this.codWod = codWod;
    }

    public String getComentario() {
        if (comentario == null) {
            comentario = "";
        }
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistroApresentar() {
        if (dataRegistroApresentar == null) {
            dataRegistroApresentar = "";
        }
        return dataRegistroApresentar;
    }

    public void setDataRegistroApresentar(String dataRegistroApresentar) {
        this.dataRegistroApresentar = dataRegistroApresentar;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUrlFoto() {
        if (urlFoto == null) {
            urlFoto = "";
        }
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getQtdLike() {
        if (qtdLike == null) {
            qtdLike = 0;
        }
        return qtdLike;
    }

    public void setQtdLike(Integer qtdLike) {
        this.qtdLike = qtdLike;
    }

    public boolean isClienteDeuLike() {
        return clienteDeuLike;
    }

    public void setClienteDeuLike(boolean clienteDeuLike) {
        this.clienteDeuLike = clienteDeuLike;
    }

    public boolean isPodeExcluir() {
        return podeExcluir;
    }

    public void setPodeExcluir(boolean podeExcluir) {
        this.podeExcluir = podeExcluir;
    }

    public Integer getNivel() {
        return nivel;
    }

    public void setNivel(Integer nivel) {
        this.nivel = nivel;
    }

    public String getNivelApresentar() {
        if (nivelApresentar == null) {
            nivelApresentar = "";
        }
        return nivelApresentar;
    }

    public void setNivelApresentar(String nivelApresentar) {
        this.nivelApresentar = nivelApresentar;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCodUsuario() {
        if (codUsuario == null) {
            codUsuario = 0;
        }
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }
}
