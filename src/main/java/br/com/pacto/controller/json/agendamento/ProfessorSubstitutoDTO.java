package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON><PERSON>;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorSubstitutoDTO {
    private Integer id;
    private String imageUri;
    private String nome;
    private String username;

    public ProfessorSubstitutoDTO(ProfessorSintetico p) {
        this.id = p.getCodigo();
        this.imageUri = p.getUriImagem();
        this.nome = p.getNome();
        this.username = "";
    }

    public ProfessorSubstitutoDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
