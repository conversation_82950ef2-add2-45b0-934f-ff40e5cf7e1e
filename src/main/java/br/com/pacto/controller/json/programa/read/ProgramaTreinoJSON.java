/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.impl.JSFUtilities;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaTreinoJSON extends SuperJSON {

    private Integer cod;
    private String nome;
    private String objetivos;
    private String restricoes;
    private String nomeMetodo;
    private String descricaoMetodo;
    private String versao;
    private Date dataInicio;
    private Date dataTerminoPrevisto;
    private String professor<PERSON><PERSON><PERSON>;
    private String professorMontou;
    private Boolean usarNovaMontagemFicha;
    private List<FichaJSON> fichas = new ArrayList<FichaJSON>();
    private List<AtividadeJSON> atividades = new ArrayList<AtividadeJSON>();
    private String userName;
    private String cref;
    private String crefProfessorMontou;
    private Date dataProximaRevisao;
    private String revisado;
    private String mensagemAviso;
    private Boolean emRevisaoProfessor;
    private Boolean isGeradoPorIA;
    private OrigemProgramaTreinoEnum origem;

    public ProgramaTreinoJSON() {
        this.cod = 0;
        this.nome = "";
        this.objetivos = "";
        this.restricoes = "";
        this.nomeMetodo = "";
        this.descricaoMetodo = "";
        this.versao = "";
        this.dataInicio = null;
        this.dataTerminoPrevisto = null;
        this.professorCarteira = "";
        this.professorMontou = "";
        this.usarNovaMontagemFicha = null;
        this.userName = "";
        this.cref = "";
        this.crefProfessorMontou = "";
        this.mensagemAviso = "";
    }
    
    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public String getNomeMetodo() {
        return nomeMetodo;
    }

    public void setNomeMetodo(String nomeMetodo) {
        this.nomeMetodo = nomeMetodo;
    }

    public String getDescricaoMetodo() {
        return descricaoMetodo;
    }

    public void setDescricaoMetodo(String descricaoMetodo) {
        this.descricaoMetodo = descricaoMetodo;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public List<FichaJSON> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaJSON> fichas) {
        this.fichas = fichas;
    }

    public List<AtividadeJSON> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeJSON> atividades) {
        this.atividades = atividades;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    public void setDataTerminoPrevisto(Date dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public String getProfessorCarteira() {
        return professorCarteira;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public void setProfessorCarteira(String professorCarteira) {
        this.professorCarteira = professorCarteira;
    }

    public String getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(String professorMontou) {
        this.professorMontou = professorMontou;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }

    public String getCref() {
        return cref;
    }

    public String getRestricoes() {
        return restricoes;
    }

    public void setRestricoes(String restricoes) {
        this.restricoes = restricoes;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    public void setDataProximaRevisao(Date dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    public String getRevisado() {
        return revisado;
    }

    public void setRevisado(String revisado) {
        this.revisado = revisado;
    }

    public Boolean getUsarNovaMontagemFicha() {
        return usarNovaMontagemFicha;
    }

    public void setUsarNovaMontagemFicha(Boolean usarNovaMontagemFicha) {
        this.usarNovaMontagemFicha = usarNovaMontagemFicha;
    }

    public String getMensagemAviso() {
        return mensagemAviso;
    }

    public void setMensagemAviso(String mensagemAviso) {
        this.mensagemAviso = mensagemAviso;
    }

    public String getCrefProfessorMontou() {
        return crefProfessorMontou;
    }

    public void setCrefProfessorMontou(String crefProfessorMontou) {
        this.crefProfessorMontou = crefProfessorMontou;
    }

    public OrigemProgramaTreinoEnum getOrigem() {
        return origem;
    }

    public void setOrigem(OrigemProgramaTreinoEnum origem) {
        this.origem = origem;
    }
}
