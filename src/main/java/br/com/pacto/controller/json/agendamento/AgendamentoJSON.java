/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class AgendamentoJSON extends SuperJSON {

    private Integer id;
    private long dataLong;
    private String data;
    private String hora;
    private String horaFinal;
    private String nome;
    private Integer statusCod;
    private String status;
    private String statusCor;
    private String cor;
    private String nomeProfessor;
    private Integer codProfessor;
    private String fotoProfessor;

    public AgendamentoJSON(Integer id, final String data, final String hora, final String horaFinal, final String nome, final String statusAgendamento,
            final String nomeProfessor, final String statusCor, long dataLong) {
        this.id = id;
        this.data = data;
        this.hora = hora;
        this.horaFinal = horaFinal;
        this.nome = nome;
        this.status = statusAgendamento;
        this.nomeProfessor = nomeProfessor;
        this.statusCor = statusCor;
        this.dataLong = dataLong;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getStatusCor() {
        return statusCor;
    }

    public void setStatusCor(String statusCor) {
        this.statusCor = statusCor;
    }

    public long getDataLong() {
        return dataLong;
    }

    public void setDataLong(long dataLong) {
        this.dataLong = dataLong;
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public Integer getStatusCod() {
        return statusCod;
    }

    public void setStatusCod(Integer statusCod) {
        this.statusCod = statusCod;
    }
}
