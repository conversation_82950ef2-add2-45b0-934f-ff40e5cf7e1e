package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.anamnese.OpcaoPergunta;
import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.util.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

public class PerguntaRespondidaJSON {

    private String pergunta;
    private String resposta;
    private List<OpcaoJSON> opcoes;

    public PerguntaRespondidaJSON(PerguntaAnamnese p) {
        this.pergunta = p.getPergunta().getDescricao();
        if(UteisValidacao.emptyList(p.getRespostas())){
            this.resposta = p.getResposta();
        }else{
            this.resposta = "";
            for(String s : p.getRespostas()){
                this.resposta += ","+s;
            }
            this.resposta = this.getResposta().replaceFirst(",", "");
        }
        opcoes = new ArrayList<OpcaoJSON>();
        for(OpcaoPergunta op : p.getPergunta().getOpcoes()){
            OpcaoJSON o = new OpcaoJSON();
            if(UteisValidacao.emptyList(p.getRespostas())){
                o.setEscolhida(this.resposta != null && this.resposta.equals(op.getCodigo().toString()));
            }else{
                o.setEscolhida(p.getRespostas().contains(op.getCodigo().toString()));

            }
            o.setCodigo(op.getCodigo());
            o.setOpcao(op.getOpcao());
            opcoes.add(o);
        }
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public List<OpcaoJSON> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoJSON> opcoes) {
        this.opcoes = opcoes;
    }
}
