package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/disponibilidade")
public class DisponibilidadeController extends SuperController {

    private DisponibilidadeService disponibilidadeService;

    @Autowired
    public DisponibilidadeController(DisponibilidadeService disponibilidadeService){
        Assert.notNull(disponibilidadeService, "O serviço de disponibilidade não foi injetado corretamente");
        this.disponibilidadeService = disponibilidadeService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarDisponibilidades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                      PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroDisponibilidadeDTO filters = new FiltroDisponibilidadeDTO(filtros);
            return ResponseEntityFactory.ok(disponibilidadeService.findAllDisponibilidades(empresaId, paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findDisponibilidadeById(@PathVariable(value = "codigo") Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.findDisponibilidadeById(codigo));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateDisponibilidade(@RequestBody DisponibilidadeDTO dto) throws JSONException {
        try {
            if(dto.getCodigo() != null && dto.getCodigo() > 0){
                return ResponseEntityFactory.ok(disponibilidadeService.updateDisponibilidade(dto));
            }
            return ResponseEntityFactory.ok(disponibilidadeService.insertDisponibilidade(dto));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar persistir disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/removerDisponibilidade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerDisponibilidade(@RequestBody DisponibilidadeDTO dto) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.removerDisponibilidade(dto));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar persistir disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/existeAgendamentoAlunoHorarioDisponibilidade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> existeAgendamentoAlunoHorarioDisponibilidade(@RequestParam(value = "idHorario")  Integer idHorario,
                                                                                            @RequestBody List<HorarioDisponibilidadeDTO> horarios) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.existeAgendamentoAlunoHorarioDisponibilidade(idHorario, horarios));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar agendamento disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{tipo}/itensValidacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> itensValidacao(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           @PathVariable Integer tipo,
                                                           @RequestParam(value = "tiposProduto", required = false) String tipos,
                                                           PaginadorDTO paginadorDTO) throws JSONException {
        try {
            if (tipo == 1) {
                return ResponseEntityFactory.ok(disponibilidadeService.findAllTipoValidacaoPlano(paginadorDTO, filtros), paginadorDTO);
            } else if (tipo == 2) {
                return ResponseEntityFactory.ok(disponibilidadeService.findAllTipoValidacaoProduto(paginadorDTO, filtros, tipos), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(new ArrayList<>(), paginadorDTO);
            }
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar itens de validacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/migrarDados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> migrarDados() throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.migrarDadosAgendamentoModeloNovoDisponibilidade());
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar itens de validacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/corrigir-existentes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> corrigirDisponibilidadesExistentes(
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            disponibilidadeService.corrigirDisponibilidadesExistentes(empresaId);
            return ResponseEntityFactory.ok("Disponibilidades corrigidas com sucesso");
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao corrigir disponibilidades existentes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro inesperado ao corrigir disponibilidades existentes", e);
            return ResponseEntityFactory.erroInterno("ERRO_INTERNO", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/corrigir-especifica/{codigo}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> corrigirDisponibilidadeEspecifica(
            @PathVariable("codigo") Integer codigoDisponibilidade,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            disponibilidadeService.corrigirDisponibilidadeEspecifica(codigoDisponibilidade);
            return ResponseEntityFactory.ok("Disponibilidade " + codigoDisponibilidade + " corrigida com sucesso");
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao corrigir disponibilidade específica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro inesperado ao corrigir disponibilidade específica", e);
            return ResponseEntityFactory.erroInterno("ERRO_INTERNO", e.getMessage());
        }
    }
}
