package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.bi.ProfessorRanking;
import br.com.pacto.bean.bi.ProfessorRankingIndicador;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.to.RankingProfessoresTO;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Created by paulo
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RankingProfessoresResponseDTO {

    private Integer colaborador;
    private Integer posicaoNumero;
    private String urlFoto;
    private String nome;
    private String posicao;
    private String geracao;
    private String desempenho;
    private String porcentagemCrescimento;
    private String alunosAtivos;
    private String mediaCarteira;
    private String percRenovaramContrato;
    private String treinoEmDia;
    private String treinoVencido;
    private String agAguardandoConf;
    private String agCancelaram;
    private String agConfirmaram;
    private String agFaltaram;
    private String agAvaliacoesRealizadas;
    private String agCompareceram;
    private String agNovosTreino;
    private String agOcupacao;
    private String agTreinosRenovado;
    private String agTreinosRevisado;
    private String agendamentos;
    private String alunosNaoUtilizaApp;
    private String alunosAtivosUtilizaApp;
    private String alunosInativosUtilizaApp;
    private String alunosCancelados;
    private String alunosInativos;
    private String alunosNaoRenovaram;
    private String estrela1;
    private String estrela2;
    private String estrela3;
    private String estrela4;
    private String estrela5;
    private String avaliacaoPeloApp;
    private String comAvaliacaoFisica;
    private String comTreino;
    private String contratoAVencer;
    private String contratoVencido;
    private String agDisponibilidade;
    private String entraramCarteira;
    private String agHorasAtendimento;
    private String agHorasDisponibilidade;
    private String mediaPrograma;
    private String novosAlunos;
    private String acessaramTreino;
    private String alunosDoTreinoAcessaramTreino;
    private String execucoesTreino;
    private String execucoesTreinoPeloApp;
    private String programaEmDia;
    private String agReagendaram;
    private String renovaramContrato;
    private String sairamCarteira;
    private String semAvaliacaoFisica;
    private String alunosAtivosSemTreino;
    private String totalAlunos;
    private String treinosVencendo;
    private String trocaramDeCarteira;
    private Double total;

    public RankingProfessoresResponseDTO(ProfessorRanking ranking) {
        this.urlFoto = Uteis.getPaintFotoDaNuvem(ranking.getProfessor().getPessoa().getFotoKey());
        if(urlFoto != null && urlFoto.contains("http://1/zw-photos/")){
            this.urlFoto = Uteis.getPaintFotoDaNuvem(this.urlFoto.replace("http://1/zw-photos/", ""));
        } else {
            this.urlFoto = Uteis.getPaintFotoDaNuvem(ranking.getProfessor().getPessoa().getFotoKey());
        }
        this.nome = ranking.getProfessor().getNome().toLowerCase()  + (ranking.getProfessor().isAtivo() ? "" : " (inativo)") ;
        BigDecimal valorFormat = new BigDecimal(ranking.getPontos()).setScale(2, RoundingMode.HALF_EVEN);
        this.total = Double.parseDouble(valorFormat.toString());
        if(ranking.getIndicadores() != null) {
            this.porcentagemCrescimento = retornaValor(IndicadorDashboardEnum.PERCENTUAL_CRESCIMENTO_CARTEIRA, ranking);
            this.alunosAtivos = retornaValor(IndicadorDashboardEnum.ATIVOS, ranking);
            this.mediaCarteira = retornaValor(IndicadorDashboardEnum.BI_TEMPO_CARTEIRA, ranking);
            this.percRenovaramContrato = retornaValor(IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA, ranking);
            this.treinoEmDia = retornaValor(IndicadorDashboardEnum.PERC_TREINO_EM_DIA, ranking);
            this.treinoVencido = retornaValor(IndicadorDashboardEnum.PERC_TREINO_VENCIDOS, ranking);
            this.agAguardandoConf = retornaValor(IndicadorDashboardEnum.AG_CONFIRMACAO, ranking);
            this.agCancelaram = retornaValor(IndicadorDashboardEnum.CANCELARAM, ranking);
            this.agConfirmaram = retornaValor(IndicadorDashboardEnum.CONFIRMARAM, ranking);
            this.agFaltaram = retornaValor(IndicadorDashboardEnum.FALTARAM, ranking);
            this.agAvaliacoesRealizadas = retornaValor(IndicadorDashboardEnum.AVALIACOES_FISICAS, ranking);
            this.agCompareceram = retornaValor(IndicadorDashboardEnum.COMPARECERAM, ranking);
            this.agNovosTreino = retornaValor(IndicadorDashboardEnum.NOVOS_TREINOS, ranking);
            this.agOcupacao = retornaValor(IndicadorDashboardEnum.OCUPACAO, ranking);
            this.agTreinosRenovado = retornaValor(IndicadorDashboardEnum.TREINOS_RENOVADOS, ranking);
            this.agTreinosRevisado = retornaValor(IndicadorDashboardEnum.TREINOS_REVISADOS, ranking);
            this.agendamentos = retornaValor(IndicadorDashboardEnum.AGENDAMENTOS, ranking);
            this.alunosNaoUtilizaApp = retornaValor(IndicadorDashboardEnum.ALUNOS_APP_NAO_INSTALADO, ranking);
            this.alunosAtivosUtilizaApp = retornaValor(IndicadorDashboardEnum.ALUNOS_APP_INSTALADO_ATIVOS, ranking);
            this.alunosCancelados = retornaValor(IndicadorDashboardEnum.ALUNOS_CANCELADOS, ranking);
            this.alunosInativos = retornaValor(IndicadorDashboardEnum.INATIVOS, ranking);
            this.alunosInativosUtilizaApp = retornaValor(IndicadorDashboardEnum.ALUNOS_APP_INSTALADO, ranking);
            this.alunosNaoRenovaram = retornaValor(IndicadorDashboardEnum.NAO_RENOVARAM, ranking);
            this.estrela1 = retornaValor(IndicadorDashboardEnum.ESTRELAS_1, ranking);
            this.estrela2 = retornaValor(IndicadorDashboardEnum.ESTRELAS_2, ranking);
            this.estrela3 = retornaValor(IndicadorDashboardEnum.ESTRELAS_3, ranking);
            this.estrela4 = retornaValor(IndicadorDashboardEnum.ESTRELAS_4, ranking);
            this.estrela5 = retornaValor(IndicadorDashboardEnum.ESTRELAS_5, ranking);
            this.avaliacaoPeloApp = retornaValor(IndicadorDashboardEnum.AVALIACOES, ranking);
            this.comAvaliacaoFisica = retornaValor(IndicadorDashboardEnum.COM_AVALIACAO_FISICA, ranking);
            this.comTreino = retornaValor(IndicadorDashboardEnum.ATIVOS_COM_TREINO, ranking);
            this.contratoAVencer = retornaValor(IndicadorDashboardEnum.ALUNOS_A_VENCER, ranking);
            this.contratoVencido = retornaValor(IndicadorDashboardEnum.VENCIDOS, ranking);
            this.agDisponibilidade = retornaValor(IndicadorDashboardEnum.AGENDAMENTOS_DISPONIBILIDADE, ranking);
            this.entraramCarteira = retornaValor(IndicadorDashboardEnum.NOVOS_CARTEIRA, ranking);
            this.agHorasAtendimento = retornaValor(IndicadorDashboardEnum.HRS_ATENDIMENTO, ranking);
            this.agHorasDisponibilidade = retornaValor(IndicadorDashboardEnum.HRS_DISPONIBILIDADE, ranking);
            this.mediaPrograma = retornaValor(IndicadorDashboardEnum.BI_TEMPO_PROGRAMA, ranking);
            this.novosAlunos = retornaValor(IndicadorDashboardEnum.NOVOS_CARTEIRA_NOVOS, ranking);
            this.acessaramTreino = retornaValor(IndicadorDashboardEnum.ACESSOS, ranking);
            this.alunosDoTreinoAcessaramTreino = retornaValor(IndicadorDashboardEnum.ACESSOS_TREINO, ranking);
            this.execucoesTreino = retornaValor(IndicadorDashboardEnum.EXECUCOES_TREINO, ranking);
            this.execucoesTreinoPeloApp = retornaValor(IndicadorDashboardEnum.SMARTPHONE, ranking);
            this.programaEmDia = retornaValor(IndicadorDashboardEnum.EM_DIA, ranking);
            this.agReagendaram = retornaValor(IndicadorDashboardEnum.REAGENDARAM, ranking);
            this.renovaramContrato = retornaValor(IndicadorDashboardEnum.RENOVARAM, ranking);
            this.sairamCarteira = retornaValor(IndicadorDashboardEnum.TROCARAM_CARTEIRA, ranking);
            this.semAvaliacaoFisica = retornaValor(IndicadorDashboardEnum.SEM_AVALIACAO, ranking);
            this.alunosAtivosSemTreino = retornaValor(IndicadorDashboardEnum.ATIVOS_SEM_TREINO, ranking);
            this.totalAlunos = retornaValor(IndicadorDashboardEnum.TOTAL_ALUNOS, ranking);
            this.treinosVencendo = retornaValor(IndicadorDashboardEnum.TREINOS_A_VENCER, ranking);
            this.trocaramDeCarteira = retornaValor(IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM, ranking);
        }
    }

    private String retornaValor(IndicadorDashboardEnum indicador, ProfessorRanking ranking) {
        String retorno = "-";
        for (ProfessorRankingIndicador valorIndicador : ranking.getIndicadores()) {
            if(valorIndicador.getIndicador().equals(indicador)){
                if (valorIndicador.getMultiplicador() != 1.0) {
                    BigDecimal valorFormat = new BigDecimal(valorIndicador.getValor()).setScale(2, RoundingMode.HALF_EVEN);
                    BigDecimal valorComPesoFormat = new BigDecimal((valorIndicador.getValor() * valorIndicador.getMultiplicador()) * (valorIndicador.getPositivo() ? 1 : -1)).setScale(2, RoundingMode.HALF_EVEN);
                    retorno = valorFormat + " (" + valorComPesoFormat + ")";
                    break;
                } else {
                    BigDecimal valorFormat = new BigDecimal(valorIndicador.getValor()).setScale(2, RoundingMode.HALF_EVEN);
                    retorno = valorFormat.toString();
                    break;
                }
            }
        }
        return retorno;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getPorcentagemCrescimento() {
        return porcentagemCrescimento;
    }

    public void setPorcentagemCrescimento(String porcentagemCrescimento) {
        this.porcentagemCrescimento = porcentagemCrescimento;
    }

    public String getAlunosAtivos() {
        return alunosAtivos;
    }

    public void setAlunosAtivos(String alunosAtivos) {
        this.alunosAtivos = alunosAtivos;
    }

    public String getMediaCarteira() {
        return mediaCarteira;
    }

    public void setMediaCarteira(String mediaCarteira) {
        this.mediaCarteira = mediaCarteira;
    }

    public String getRenovaramContrato() {
        return renovaramContrato;
    }

    public void setRenovaramContrato(String renovaramContrato) {
        this.renovaramContrato = renovaramContrato;
    }

    public String getTreinoEmDia() {
        return treinoEmDia;
    }

    public void setTreinoEmDia(String treinoEmDia) {
        this.treinoEmDia = treinoEmDia;
    }

    public String getTreinoVencido() {
        return treinoVencido;
    }

    public void setTreinoVencido(String treinoVencido) {
        this.treinoVencido = treinoVencido;
    }

    public String getAgAguardandoConf() {
        return agAguardandoConf;
    }

    public void setAgAguardandoConf(String agAguardandoConf) {
        this.agAguardandoConf = agAguardandoConf;
    }

    public String getAgCancelaram() {
        return agCancelaram;
    }

    public void setAgCancelaram(String agCancelaram) {
        this.agCancelaram = agCancelaram;
    }

    public String getAgConfirmaram() {
        return agConfirmaram;
    }

    public void setAgConfirmaram(String agConfirmaram) {
        this.agConfirmaram = agConfirmaram;
    }

    public String getAgFaltaram() {
        return agFaltaram;
    }

    public void setAgFaltaram(String agFaltaram) {
        this.agFaltaram = agFaltaram;
    }

    public String getAgAvaliacoesRealizadas() {
        return agAvaliacoesRealizadas;
    }

    public void setAgAvaliacoesRealizadas(String agAvaliacoesRealizadas) {
        this.agAvaliacoesRealizadas = agAvaliacoesRealizadas;
    }

    public String getAgCompareceram() {
        return agCompareceram;
    }

    public void setAgCompareceram(String agCompareceram) {
        this.agCompareceram = agCompareceram;
    }

    public String getAgNovosTreino() {
        return agNovosTreino;
    }

    public void setAgNovosTreino(String agNovosTreino) {
        this.agNovosTreino = agNovosTreino;
    }

    public String getAgOcupacao() {
        return agOcupacao;
    }

    public void setAgOcupacao(String agOcupacao) {
        this.agOcupacao = agOcupacao;
    }

    public String getPercRenovaramContrato() {
        return percRenovaramContrato;
    }

    public void setPercRenovaramContrato(String percRenovaramContrato) {
        this.percRenovaramContrato = percRenovaramContrato;
    }

    public String getAgTreinosRenovado() {
        return agTreinosRenovado;
    }

    public void setAgTreinosRenovado(String agTreinosRenovado) {
        this.agTreinosRenovado = agTreinosRenovado;
    }

    public String getAgTreinosRevisado() {
        return agTreinosRevisado;
    }

    public void setAgTreinosRevisado(String agTreinosRevisado) {
        this.agTreinosRevisado = agTreinosRevisado;
    }

    public String getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(String agendamentos) {
        this.agendamentos = agendamentos;
    }

    public String getAlunosNaoUtilizaApp() {
        return alunosNaoUtilizaApp;
    }

    public void setAlunosNaoUtilizaApp(String alunosNaoUtilizaApp) {
        this.alunosNaoUtilizaApp = alunosNaoUtilizaApp;
    }

    public String getAlunosAtivosUtilizaApp() {
        return alunosAtivosUtilizaApp;
    }

    public void setAlunosAtivosUtilizaApp(String alunosAtivosUtilizaApp) {
        this.alunosAtivosUtilizaApp = alunosAtivosUtilizaApp;
    }

    public String getAlunosInativosUtilizaApp() {
        return alunosInativosUtilizaApp;
    }

    public void setAlunosInativosUtilizaApp(String alunosInativosUtilizaApp) {
        this.alunosInativosUtilizaApp = alunosInativosUtilizaApp;
    }

    public String getAlunosCancelados() {
        return alunosCancelados;
    }

    public void setAlunosCancelados(String alunosCancelados) {
        this.alunosCancelados = alunosCancelados;
    }

    public String getAlunosInativos() {
        return alunosInativos;
    }

    public void setAlunosInativos(String alunosInativos) {
        this.alunosInativos = alunosInativos;
    }

    public String getAlunosNaoRenovaram() {
        return alunosNaoRenovaram;
    }

    public void setAlunosNaoRenovaram(String alunosNaoRenovaram) {
        this.alunosNaoRenovaram = alunosNaoRenovaram;
    }

    public String getEstrela1() {
        return estrela1;
    }

    public void setEstrela1(String estrela1) {
        this.estrela1 = estrela1;
    }

    public String getEstrela2() {
        return estrela2;
    }

    public void setEstrela2(String estrela2) {
        this.estrela2 = estrela2;
    }

    public String getEstrela3() {
        return estrela3;
    }

    public void setEstrela3(String estrela3) {
        this.estrela3 = estrela3;
    }

    public String getEstrela4() {
        return estrela4;
    }

    public void setEstrela4(String estrela4) {
        this.estrela4 = estrela4;
    }

    public String getEstrela5() {
        return estrela5;
    }

    public void setEstrela5(String estrela5) {
        this.estrela5 = estrela5;
    }

    public String getAvaliacaoPeloApp() {
        return avaliacaoPeloApp;
    }

    public void setAvaliacaoPeloApp(String avaliacaoPeloApp) {
        this.avaliacaoPeloApp = avaliacaoPeloApp;
    }

    public String getComAvaliacaoFisica() {
        return comAvaliacaoFisica;
    }

    public void setComAvaliacaoFisica(String comAvaliacaoFisica) {
        this.comAvaliacaoFisica = comAvaliacaoFisica;
    }

    public String getComTreino() {
        return comTreino;
    }

    public void setComTreino(String comTreino) {
        this.comTreino = comTreino;
    }

    public String getContratoAVencer() {
        return contratoAVencer;
    }

    public void setContratoAVencer(String contratoAVencer) {
        this.contratoAVencer = contratoAVencer;
    }

    public String getContratoVencido() {
        return contratoVencido;
    }

    public void setContratoVencido(String contratoVencido) {
        this.contratoVencido = contratoVencido;
    }

    public String getAgDisponibilidade() {
        return agDisponibilidade;
    }

    public void setAgDisponibilidade(String agDisponibilidade) {
        this.agDisponibilidade = agDisponibilidade;
    }

    public String getEntraramCarteira() {
        return entraramCarteira;
    }

    public void setEntraramCarteira(String entraramCarteira) {
        this.entraramCarteira = entraramCarteira;
    }

    public String getAgHorasAtendimento() {
        return agHorasAtendimento;
    }

    public void setAgHorasAtendimento(String agHorasAtendimento) {
        this.agHorasAtendimento = agHorasAtendimento;
    }

    public String getAgHorasDisponibilidade() {
        return agHorasDisponibilidade;
    }

    public void setAgHorasDisponibilidade(String agHorasDisponibilidade) {
        this.agHorasDisponibilidade = agHorasDisponibilidade;
    }

    public String getMediaPrograma() {
        return mediaPrograma;
    }

    public void setMediaPrograma(String mediaPrograma) {
        this.mediaPrograma = mediaPrograma;
    }

    public String getNovosAlunos() {
        return novosAlunos;
    }

    public void setNovosAlunos(String novosAlunos) {
        this.novosAlunos = novosAlunos;
    }

    public String getAcessaramTreino() {
        return acessaramTreino;
    }

    public void setAcessaramTreino(String acessaramTreino) {
        this.acessaramTreino = acessaramTreino;
    }

    public String getAlunosDoTreinoAcessaramTreino() {
        return alunosDoTreinoAcessaramTreino;
    }

    public void setAlunosDoTreinoAcessaramTreino(String alunosDoTreinoAcessaramTreino) {
        this.alunosDoTreinoAcessaramTreino = alunosDoTreinoAcessaramTreino;
    }

    public String getExecucoesTreino() {
        return execucoesTreino;
    }

    public void setExecucoesTreino(String execucoesTreino) {
        this.execucoesTreino = execucoesTreino;
    }

    public String getExecucoesTreinoPeloApp() {
        return execucoesTreinoPeloApp;
    }

    public void setExecucoesTreinoPeloApp(String execucoesTreinoPeloApp) {
        this.execucoesTreinoPeloApp = execucoesTreinoPeloApp;
    }

    public String getProgramaEmDia() {
        return programaEmDia;
    }

    public void setProgramaEmDia(String programaEmDia) {
        this.programaEmDia = programaEmDia;
    }

    public String getAgReagendaram() {
        return agReagendaram;
    }

    public void setAgReagendaram(String agReagendaram) {
        this.agReagendaram = agReagendaram;
    }

    public String getSairamCarteira() {
        return sairamCarteira;
    }

    public void setSairamCarteira(String sairamCarteira) {
        this.sairamCarteira = sairamCarteira;
    }

    public String getSemAvaliacaoFisica() {
        return semAvaliacaoFisica;
    }

    public void setSemAvaliacaoFisica(String semAvaliacaoFisica) {
        this.semAvaliacaoFisica = semAvaliacaoFisica;
    }

    public String getAlunosAtivosSemTreino() {
        return alunosAtivosSemTreino;
    }

    public void setAlunosAtivosSemTreino(String alunosAtivosSemTreino) {
        this.alunosAtivosSemTreino = alunosAtivosSemTreino;
    }

    public String getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(String totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public String getTreinosVencendo() {
        return treinosVencendo;
    }

    public void setTreinosVencendo(String treinosVencendo) {
        this.treinosVencendo = treinosVencendo;
    }

    public String getTrocaramDeCarteira() {
        return trocaramDeCarteira;
    }

    public void setTrocaramDeCarteira(String trocaramDeCarteira) {
        this.trocaramDeCarteira = trocaramDeCarteira;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getPosicao() {
        return posicao;
    }

    public void setPosicao(String posicao) {
        this.posicao = posicao;
    }

    public Integer getPosicaoNumero() { return posicaoNumero; }

    public void setPosicaoNumero(Integer posicaoNumero) { this.posicaoNumero = posicaoNumero; }

    public String getDesempenho() {
        return desempenho;
    }

    public void setDesempenho(String desempenho) {
        this.desempenho = desempenho;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getGeracao() {
        return geracao;
    }

    public void setGeracao(String geracao) {
        this.geracao = geracao;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }
}
