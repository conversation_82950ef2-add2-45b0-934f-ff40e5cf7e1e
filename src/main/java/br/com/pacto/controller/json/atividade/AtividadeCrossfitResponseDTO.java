package br.com.pacto.controller.json.atividade;

import br.com.pacto.bean.atividade.*;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 01/02/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeCrossfitResponseDTO {

    private Integer id;
    private String nome;
    private CategoriaAtividadeWodEnum categoria;
    private UnidadeMedidaEnum unidadeMedida;
    private boolean ativo;
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();
    private String descricao;
    private String videoUri;

    public AtividadeCrossfitResponseDTO(Atividade atividade) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.categoria = atividade.getCategoriaAtividadeWod() == CategoriaAtividadeWodEnum.NENHUM ? null : atividade.getCategoriaAtividadeWod();
        this.unidadeMedida = atividade.getUnidadeMedida() == UnidadeMedidaEnum.NENHUM ? null : atividade.getUnidadeMedida();
        this.ativo = atividade.isAtivo();
        this.descricao = atividade.getDescricao();
        this.videoUri = atividade.getLinkVideo();

        if (atividade.getEmpresasHabilitadas() != null && !UteisValidacao.emptyList(atividade.getEmpresasHabilitadas())) {
            for (AtividadeEmpresa empresa : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(empresa));
            }
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CategoriaAtividadeWodEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaAtividadeWodEnum categoria) {
        this.categoria = categoria;
    }

    public UnidadeMedidaEnum getUnidadeMedida() {
        return unidadeMedida;
    }

    public void setUnidadeMedida(UnidadeMedidaEnum unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }
}
