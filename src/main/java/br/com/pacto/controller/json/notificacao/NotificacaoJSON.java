/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.notificacao;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class NotificacaoJSON extends SuperJSON {

    private Integer cod;
    private String tipo;
    private Integer codProfessor;
    private String data;
    private String titulo;
    private String texto;
    private Integer codCliente;
    private String telAluno;
    private String gravidadeCor;
    private String opcoes;
    private String resposta;
    private boolean lida = false;
    private boolean pushEnviado = false;

    public NotificacaoJSON(final Integer cod, final String titulo, final String texto,
            final String tipoNotf, final String data, final Integer codProfessor,
            final Integer codCliente, final String telAluno, final String gravidadeCor, final String opcoes, final String resposta, final  boolean lida, final  boolean pushEnviado) {
        this.cod = cod;
        this.titulo = titulo;
        this.texto = texto;
        this.tipo = tipoNotf;
        this.data = data;
        this.codProfessor = codProfessor;
        this.codCliente = codCliente;
        this.telAluno = telAluno;
        this.gravidadeCor = gravidadeCor;
        this.opcoes = opcoes;
        this.resposta = resposta;
        this.lida = lida;
        this.pushEnviado = pushEnviado;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getTelAluno() {
        return telAluno;
    }

    public void setTelAluno(String telAluno) {
        this.telAluno = telAluno;
    }

    public String getGravidadeCor() {
        return gravidadeCor;
    }

    public void setGravidadeCor(String gravidadeCor) {
        this.gravidadeCor = gravidadeCor;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }


    public boolean isLida() {
        return lida;
    }

    public void setLida(boolean lida) {
        this.lida = lida;
    }

    public boolean isPushEnviado() {
        return pushEnviado;
    }

    public void setPushEnviado(boolean pushEnviado) {
        this.pushEnviado = pushEnviado;
    }
}
