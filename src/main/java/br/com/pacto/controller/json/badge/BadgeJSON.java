/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.badge;

import br.com.pacto.bean.badge.TipoBadgeEnum;
import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class BadgeJSON extends SuperJSON {

    private String id;
    private String nome;
    private String img;
    private String descricao1;
    private String descricao2;

    public BadgeJSON(String id, String nome, String img, String descricao1, String descricao2) {
        this.id = id;
        this.nome = nome;
        this.img = img;
        this.descricao1 = descricao1;
        this.descricao2 = descricao2;
    }

    public BadgeJSON(TipoBadgeEnum badges) {
        this.id = badges.getId().toString();
        this.nome = badges.getNome();
        this.img = badges.getImg();
        this.descricao1 = badges.getDescricao1();
        this.descricao2 = badges.getDescricao2();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getDescricao1() {
        return descricao1;
    }

    public void setDescricao1(String descricao1) {
        this.descricao1 = descricao1;
    }

    public String getDescricao2() {
        return descricao2;
    }

    public void setDescricao2(String descricao2) {
        this.descricao2 = descricao2;
    }
}
