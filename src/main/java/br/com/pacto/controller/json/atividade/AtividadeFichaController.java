package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.anamnese.AnamneseController;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 13/08/2018.
 */
@Controller
@RequestMapping("/psec/atividades-ficha")
public class AtividadeFichaController {

    private final ProgramaTreinoService programaTreinoService;

    @Autowired
    public AtividadeFichaController(ProgramaTreinoService programaTreinoService){
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAtividadeFicha(@RequestBody AtividadeFichaEndpointTO atividadeFichaTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarAtividadeFicha(atividadeFichaTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividadeFicha(@PathVariable("id") final Integer id,
                                                                       @RequestBody AtividadeFichaEndpointTO atividadeFichaTO) {
        try {
            atividadeFichaTO.setId(id);
            return ResponseEntityFactory.ok(programaTreinoService.atualizarAtividadeFicha(atividadeFichaTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadeFicha(@PathVariable("id") final Integer id){
        try {
            programaTreinoService.removerAtividadeFicha(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/add-atividade/{ficha}/{atividade}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAtividadeFicha(@PathVariable Integer ficha, @PathVariable Integer atividade) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.addAtividadeFicha(ficha, atividade));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
