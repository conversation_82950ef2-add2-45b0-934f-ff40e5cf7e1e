/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.controller.json.serialization.JsonDateTimeSerializerYYYYMMDDHHNN;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class TreinoRealizadoHistoricoJSON {

    private Integer cod;
    private String nomePrograma;
    private Date inicio;
    private String nomeFicha;
    private String nomeProfessor;
    private Integer totalAtividades;

    public TreinoRealizadoHistoricoJSON() {
    }

    public TreinoRealizadoHistoricoJSON(final Integer codigo, final String nomePrograma, Date inicio,
            final String nomeFicha, final String nomeProfessor, Integer totalAtividades) {
        this.cod = codigo;
        this.nomePrograma = nomePrograma;
        this.inicio = inicio;
        this.nomeFicha = nomeFicha;
        this.nomeProfessor = nomeProfessor;
        this.totalAtividades = totalAtividades;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNomePrograma() {
        return nomePrograma;
    }

    public void setNomePrograma(String nomePrograma) {
        this.nomePrograma = nomePrograma;
    }

    @JsonSerialize(using = JsonDateTimeSerializerYYYYMMDDHHNN.class)
    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public String getNomeFicha() {
        return nomeFicha;
    }

    public void setNomeFicha(String nomeFicha) {
        this.nomeFicha = nomeFicha;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public Integer getTotalAtividades() {
        return totalAtividades;
    }

    public void setTotalAtividades(Integer totalAtividades) {
        this.totalAtividades = totalAtividades;
    }
}
