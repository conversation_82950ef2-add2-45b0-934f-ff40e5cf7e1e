package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OcupacaoDTO {
    private DiaOcupacaoDTO segunda;
    private DiaOcupacaoDTO terca;
    private DiaOcupacaoDTO quarta;
    private DiaOcupacaoDTO quinta;
    private DiaOcupacaoDTO sexta;
    private DiaOcupacaoDTO sabado;
    private DiaOcupacaoDTO domingo;

    public OcupacaoDTO() {
    }



    public DiaOcupacaoDTO getSegunda() {
        return segunda;
    }

    public void setSegunda(DiaOcupacaoDTO segunda) {
        this.segunda = segunda;
    }

    public DiaOcupacaoDTO getTerca() {
        return terca;
    }

    public void setTerca(DiaOcupacaoDTO terca) {
        this.terca = terca;
    }

    public DiaOcupacaoDTO getQuarta() {
        return quarta;
    }

    public void setQuarta(DiaOcupacaoDTO quarta) {
        this.quarta = quarta;
    }

    public DiaOcupacaoDTO getQuinta() {
        return quinta;
    }

    public void setQuinta(DiaOcupacaoDTO quinta) {
        this.quinta = quinta;
    }

    public DiaOcupacaoDTO getSexta() {
        return sexta;
    }

    public void setSexta(DiaOcupacaoDTO sexta) {
        this.sexta = sexta;
    }

    public DiaOcupacaoDTO getSabado() {
        return sabado;
    }

    public void setSabado(DiaOcupacaoDTO sabado) {
        this.sabado = sabado;
    }

    public DiaOcupacaoDTO getDomingo() {
        return domingo;
    }

    public void setDomingo(DiaOcupacaoDTO domingo) {
        this.domingo = domingo;
    }
}
