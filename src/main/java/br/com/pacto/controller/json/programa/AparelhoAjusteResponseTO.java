package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.aparelho.AparelhoAjuste;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoAjusteResponseTO {

    private Integer id;
    private String nome;

    public AparelhoAjusteResponseTO(){

    }

    public AparelhoAjusteResponseTO(AparelhoAjuste aparelhoAjuste){
        this.id = aparelhoAjuste.getCodigo();
        this.nome = aparelhoAjuste.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
