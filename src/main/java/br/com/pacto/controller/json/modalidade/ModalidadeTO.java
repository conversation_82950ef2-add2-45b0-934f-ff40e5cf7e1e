package br.com.pacto.controller.json.modalidade;


import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModalidadeTO {

    private Integer id;
    private String nome;
    private Integer corId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCorId() {
        return corId;
    }

    public void setCorId(Integer corId) {
        this.corId = corId;
    }
}
