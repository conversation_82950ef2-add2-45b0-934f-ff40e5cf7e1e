package br.com.pacto.controller.json.musculo;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.musculo.GrupoMuscularTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.musculo.GrupoMuscularService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/grupos-musculares")
public class GrupoMuscularController {

    private final GrupoMuscularService grupoMuscularService;

    @Autowired
    public GrupoMuscularController(GrupoMuscularService grupoMuscularService){
        Assert.notNull(grupoMuscularService, "O serviço de grupo muscular não foi injetado corretamente");
        this.grupoMuscularService = grupoMuscularService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGruposMusculares(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         PaginadorDTO paginadorDTO) throws JSONException     {
        try {
            FiltroGrupoMuscularJSON filtroGrupoMuscularJSON = new FiltroGrupoMuscularJSON(filtros);
            return ResponseEntityFactory.ok(grupoMuscularService.consultarGruposMusculares(filtroGrupoMuscularJSON,paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodosGruposMusculares() {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.consultarTodosGruposMusculares());
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGrupoMuscular(@PathVariable("id") Integer id){
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.consultarGrupoMuscular(id));
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o grupo muscular", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirGrupoMuscular(@RequestBody GrupoMuscularTO grupoMuscularTO) {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.inserir(grupoMuscularTO));
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir Grupo Muscular", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarGrupoMuscular(@PathVariable("id") final Integer id,
                                                                    @RequestBody GrupoMuscularTO grupoMuscularTO) {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.alterar(id, grupoMuscularTO));

        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar grupo muscular", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirGrupoMuscular(@PathVariable("id") final Integer id){
        try {
            grupoMuscularService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir grupo muscular", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }



}
