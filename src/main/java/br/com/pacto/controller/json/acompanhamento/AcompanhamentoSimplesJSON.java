/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class AcompanhamentoSimplesJSON extends SuperJSON {

    private String frequencia;
    private Double assiduidade;
    private Integer frequenciaSemanal;
    private Integer nrTreinos;
    private Integer aulasPrevistas;

    public AcompanhamentoSimplesJSON() {
    }

    public AcompanhamentoSimplesJSON(String frequencia, Double assiduidade) {
        this.frequencia = frequencia;
        this.assiduidade = assiduidade;
    }

    public String getFrequencia() {
        if (frequencia == null) {
            frequencia = "";
        }
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public Double getAssiduidade() {
        if (assiduidade == null) {
            assiduidade = 0.0;
        }
        return assiduidade;
    }

    public void setAssiduidade(Double assiduidade) {
        this.assiduidade = assiduidade;
    }

    public Integer getFrequenciaSemanal() {
        return frequenciaSemanal;
    }

    public void setFrequenciaSemanal(Integer frequenciaSemanal) {
        this.frequenciaSemanal = frequenciaSemanal;
    }

    public Integer getNrTreinos() {
        return nrTreinos;
    }

    public void setNrTreinos(Integer nrTreinos) {
        this.nrTreinos = nrTreinos;
    }

    public Integer getAulasPrevistas() {
        return aulasPrevistas;
    }

    public void setAulasPrevistas(Integer aulasPrevistas) {
        this.aulasPrevistas = aulasPrevistas;
    }
}
