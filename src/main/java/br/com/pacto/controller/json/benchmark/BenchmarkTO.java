package br.com.pacto.controller.json.benchmark;

import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by j<PERSON><PERSON> moita on 27/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BenchmarkTO {

    private String nome;
    private Integer tipoBenchmarkId;
    private TipoWodEnum tipoExercicio;
    private String exercicios;
    private String observacao;
    private String imagemData;
    private String videoUri;
    private String extensaoImagem;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoBenchmarkId() {
        return tipoBenchmarkId;
    }

    public void setTipoBenchmarkId(Integer tipoBenchmarkId) {
        this.tipoBenchmarkId = tipoBenchmarkId;
    }

    public TipoWodEnum getTipoExercicio() {
        return tipoExercicio;
    }

    public void setTipoExercicio(TipoWodEnum tipoExercicio) {
        this.tipoExercicio = tipoExercicio;
    }

    public String getExercicios() {
        return exercicios;
    }

    public void setExercicios(String exercicios) {
        this.exercicios = exercicios;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }
}
