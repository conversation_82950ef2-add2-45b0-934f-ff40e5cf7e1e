package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.gestao.IndicadorEnum;
import br.com.pacto.bean.gestao.SituacaoContratoEnum;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorExcecoes;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.*;

/**
 * Created paulo 01/11/2018
 */
public class FiltroGestaoJSON extends SuperJSON {

    public class MesAno{
        private Integer mes;
        private Integer ano;

        public Integer getAno() {return ano;}
        public void setAno(Integer ano) { this.ano = ano;}
        public Integer getMes() {return mes; }
        public void setMes(Integer mes) {this.mes = mes;}
    }
    private String nome;
    private Date dataReferencia;
    private Date inicio;
    private Date fim;
    private List<Integer> professoresIds;
    private List<Integer> tiposEventosIds;
    private List<Integer> cargaHoraria;
    private List<Integer> modalidadeIds;
    private List<String> situacao;
    private List<String> personal;
    private List<SituacaoAlunoEnum> status;
    private StatusAgendamentoEnum statusAgendamentoEnum;
    private List<SituacaoContratoEnum> contrato;
    private List<SituacaoProgramaEnum> situacaoPrograma;
    private Integer professorId;
    private IndicadorEnum indicador;
    private CategoriaIndicadorEnum categoria;
    private List<MesAno> mesAno;
    private Boolean incluirProfessorInativo;
    private Boolean todosStatus = false;

    public FiltroGestaoJSON(JSONObject filters) throws ServiceException {
        try {
        if (filters != null) {

            JSONArray colaboradorIds = filters.optJSONArray("colaboradorIds");
            this.professoresIds = new ArrayList<>();
            if (colaboradorIds != null) {
                for (int i = 0; i < colaboradorIds.length(); i++) {
                    getProfessoresIds().add(colaboradorIds.getInt(i));
                }
            }

            JSONArray tiposEventos = filters.optJSONArray("tiposEvento");
            this.tiposEventosIds = new ArrayList<>();
            if (tiposEventos != null) {
                for (int i = 0; i < tiposEventos.length(); i++) {
                    getTiposEventosIds().add(tiposEventos.getInt(i));
                }
            }
            this.tiposEventosIds = getTiposEventosIds();

            this.professorId =  filters.optInt("professorId" ) == 0 ? null : filters.optInt("professorId");
            String status =  filters.optString("status" ) == "" ? null : filters.optString("status");
            if(status != null && !status.equals("AGENDADOS")){
                for(StatusAgendamentoEnum senum : StatusAgendamentoEnum.values()){
                   if(senum.toString().equals(status)){
                       this.statusAgendamentoEnum = senum;
                   }
                }
            }else if (status != null){
                this.todosStatus = true;
            }
            this.inicio = filters.optLong("dataInicio") == 0 ? null : new Date(filters.optLong("dataInicio"));
            this.fim = filters.optLong("dataFim") == 0 ? null : new Date(filters.optLong("dataFim"));
            if(inicio != null && fim != null) {
                this.inicio = zerarHoras1(inicio);
                this.fim = horaFinal(fim);
            }

            JSONArray cargahoraria = filters.optJSONArray("cargaHoraria");
            this.cargaHoraria = new ArrayList<>();
            if (cargahoraria != null) {
                for (int i = 0; i < cargahoraria.length(); i++) {
                    getCargaHoraria().add(cargahoraria.getInt(i));
                }
            }
            this.cargaHoraria = getCargaHoraria();

            JSONArray modalidadeIds = filters.optJSONArray("modalidadeIds");
            this.modalidadeIds = new ArrayList<>();
            if (modalidadeIds != null) {
                for (int i = 0; i < modalidadeIds.length(); i++) {
                    getModalidadeIds().add(modalidadeIds.getInt(i));
                }
            }
            this.modalidadeIds = getModalidadeIds();

            JSONArray situacoes = filters.optJSONArray("situacoes");
            this.situacao = new ArrayList<>();
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    this.situacao.add(situacoes.getString(i));
                }
            }

            JSONArray personal = filters.optJSONArray("personal");
            this.personal = new ArrayList<>();
            if (personal != null) {
                for (int i = 0; i < personal.length(); i++) {
                    this.personal.add(personal.getString(i));
                }
            }

        }
        } catch (Exception ex) {
            throw new ServiceException(ProfessorExcecoes.ERRO_CRIAR_FILTRO);
        }

    }

    private static Date zerarHoras1(Date data){
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    private static Date horaFinal(Date data){
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 59);
        return cal.getTime();
    }



    public FiltroGestaoJSON(JSONObject filtros, JSONObject configs) throws ServiceException {
        try {
            this.nome = filtros.optString("quicksearchValue");
            this.dataReferencia = filtros.optLong("dataReferencia") == 0 ? null : new Date(filtros.optLong("dataReferencia"));
            this.inicio = filtros.optLong("dataInicio") == 0 ? null : new Date(filtros.optLong("dataInicio"));
            this.fim = filtros.optLong("dataFim") == 0 ? null : new Date(filtros.optLong("dataFim"));
            JSONArray situacoesAluno = filtros.optJSONArray("status");
            this.status = new ArrayList<>();
            if (situacoesAluno != null) {
                for (int i = 0; i < situacoesAluno.length(); i++) {
                    getStatus().add(SituacaoAlunoEnum.valueOf(situacoesAluno.getString(i)));
                }
            }
            this.status = getStatus();
            JSONArray situacoesContrato = filtros.optJSONArray("situacaoContrato");
            this.contrato = new ArrayList<>();
            if (situacoesContrato != null) {
                for (int i = 0; i < situacoesContrato.length(); i++) {
                    getContrato().add(SituacaoContratoEnum.valueOf(situacoesContrato.getString(i)));
                }
            }
            this.contrato = getContrato();

            JSONArray situacoesPrograma = filtros.optJSONArray("situacaoPrograma");
            this.situacaoPrograma = new ArrayList<>();
            if (situacoesPrograma != null) {
                for (int i = 0; i < situacoesPrograma.length(); i++) {
                    getSituacaoPrograma().add(SituacaoProgramaEnum.valueOf(situacoesPrograma.getString(i)));
                }
            }
            this.situacaoPrograma = getSituacaoPrograma();

            JSONArray mesAnoArray = filtros.optJSONArray("mesAno");
            this.mesAno = new ArrayList<>();
            if (mesAnoArray != null) {
                for (int i = 0; i < mesAnoArray.length(); i++) {
                    MesAno mesAno1 = new MesAno();
                    mesAno1.setMes(mesAnoArray.optJSONObject(i).optInt("mes"));
                    mesAno1.setAno(mesAnoArray.optJSONObject(i).optInt("ano"));
                    getMesAno().add(mesAno1);
                }
            }

            JSONArray professores = filtros.optJSONArray("professoresIds");
            this.professoresIds = new ArrayList<>();
            if (professores != null) {
                for (int i = 0; i < professores.length(); i++) {
                    getProfessoresIds().add(professores.getInt(i));
                }
            }
            this.professoresIds = getProfessoresIds();

            this.professorId = filtros.optInt("professorId");

            String indicadorStr = filtros.optString("indicador");
            if (!UteisValidacao.emptyString(indicadorStr)) {
                this.indicador = IndicadorEnum.valueOf(indicadorStr.trim());
            }
            this.incluirProfessorInativo = configs.optBoolean("incluirProfessorInativo");

            JSONArray tiposEventos = filtros.optJSONArray("tiposEvento");
            this.tiposEventosIds = new ArrayList<>();
            if (tiposEventos != null) {
                for (int i = 0; i < tiposEventos.length(); i++) {
                    getTiposEventosIds().add(tiposEventos.getInt(i));
                }
            }
            this.tiposEventosIds = getTiposEventosIds();

            JSONArray cargahoraria = filtros.optJSONArray("cargaHoraria");
            this.cargaHoraria = new ArrayList<>();
            if (cargahoraria != null) {
                for (int i = 0; i < cargahoraria.length(); i++) {
                    this.cargaHoraria.add(cargahoraria.getInt(i));
                }
            }

            JSONArray modalidadeIds = filtros.optJSONArray("modalidadeIds");
            this.modalidadeIds = new ArrayList<>();
            if (modalidadeIds != null) {
                for (int i = 0; i < modalidadeIds.length(); i++) {
                    this.modalidadeIds.add(modalidadeIds.getInt(i));
                }
            }

            JSONArray situacoes = filtros.optJSONArray("situacoes");
            this.situacao = new ArrayList<>();
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    this.situacao.add(situacoes.getString(i));
                }
            }

            JSONArray personal = filtros.optJSONArray("personal");
            this.personal = new ArrayList<>();
            if (personal != null) {
                for (int i = 0; i < personal.length(); i++) {
                    this.personal.add(personal.getString(i));
                }
            }

        } catch (Exception ex) {
            throw new ServiceException(ProfessorExcecoes.ERRO_CRIAR_FILTRO);
        }

    }

    public Date getDataReferencia() {
        return dataReferencia;
    }

    public void setDataReferencia(Date dataReferencia) {
        this.dataReferencia = dataReferencia;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<Integer> getProfessoresIds() {
        return professoresIds;
    }

    public void setProfessoresIds(List<Integer> professoresIds) {
        this.professoresIds = professoresIds;
    }

    public List<SituacaoAlunoEnum> getStatus() {
        return status;
    }

    public void setStatus(List<SituacaoAlunoEnum> status) {
        this.status = status;
    }

    public List<SituacaoContratoEnum> getContrato() {
        return contrato;
    }

    public void setContrato(List<SituacaoContratoEnum> contrato) {
        this.contrato = contrato;
    }

    public List<SituacaoProgramaEnum> getSituacaoPrograma() {
        return situacaoPrograma;
    }

    public void setSituacaoPrograma(List<SituacaoProgramaEnum> situacaoPrograma) {
        this.situacaoPrograma = situacaoPrograma;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public IndicadorEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorEnum indicador) {
        this.indicador = indicador;
    }

    public CategoriaIndicadorEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaIndicadorEnum categoria) {
        this.categoria = categoria;
    }

    public List<MesAno> getMesAno() {
        return mesAno;
    }

    public void setMesAno(List<MesAno> mesAno) {
        this.mesAno = mesAno;
    }

    public Boolean getIncluirProfessorInativo() {
        return incluirProfessorInativo;
    }

    public void setIncluirProfessorInativo(Boolean incluirProfessorInativo) {
        this.incluirProfessorInativo = incluirProfessorInativo;
    }

    public List<Integer> getTiposEventosIds() {
        return tiposEventosIds;
    }

    public void setTiposEventosIds(List<Integer> tiposEventosIds) {
        this.tiposEventosIds = tiposEventosIds;
    }

    public StatusAgendamentoEnum getStatusAgendamentoEnum() {
        return statusAgendamentoEnum;
    }

    public void setStatusAgendamentoEnum(StatusAgendamentoEnum statusAgendamentoEnum) {
        this.statusAgendamentoEnum = statusAgendamentoEnum;
    }

    public Boolean getTodosStatus() {
        return todosStatus;
    }

    public void setTodosStatus(Boolean todosStatus) {
        this.todosStatus = todosStatus;
    }

    public List<Integer> getCargaHoraria() { return cargaHoraria; }

    public void setCargaHoraria(List<Integer> cargaHoraria) { this.cargaHoraria = cargaHoraria; }

    public List<Integer> getModalidadeIds() { return modalidadeIds; }

    public void setModalidadeIds(List<Integer> modalidadeIds) { this.modalidadeIds = modalidadeIds; }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<String> getSituacao() {
        return situacao;
    }

    public void setSituacao(List<String> situacao) {
        this.situacao = situacao;
    }

    public List<String> getPersonal() {
        return personal;
    }

    public void setPersonal(List<String> personal) {
        this.personal = personal;
    }
}
