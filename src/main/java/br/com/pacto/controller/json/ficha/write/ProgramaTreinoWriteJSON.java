package br.com.pacto.controller.json.ficha.write;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProgramaSituacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.professor.ProfessorJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by glauc on 14/06/2017
 */
public class ProgramaTreinoWriteJSON extends SuperJSON {

    private Integer codigo;
    private String nome;
    private Date dataLancamento;
    private Date dataInicio;
    private Date dataProximaRevisao;
    private Date dataTerminoPrevisto;
    private Integer diasPorSemana;
    private Integer totalAulasPrevistas;
    private ProgramaSituacaoEnum situacao = ProgramaSituacaoEnum.ATIVO;
    private Integer versao;
    private Boolean treinoRapido;
    private Integer nrTreinosRealizados;
    private Integer programaTreinoRenovacao;
    private Integer programaTreinoRenovado;
    private Date dataRenovacao;
    private ClienteJSON cliente;
    private ProfessorJSON professorMontou;
    private Date dataUltimaAtualizacao;
    private List<FichaWriteJSON> programasFicha = new ArrayList<FichaWriteJSON>();

    public ProgramaTreinoWriteJSON() {

    }

    public ProgramaTreinoWriteJSON(ProgramaTreino programaTreino, Usuario usuario) {
        this.codigo = programaTreino.getCodigo();
        this.nome = programaTreino.getNome();
        this.dataLancamento = programaTreino.getDataLancamento();
        this.dataInicio = programaTreino.getDataInicio();
        this.dataProximaRevisao = programaTreino.getDataProximaRevisao();
        this.dataTerminoPrevisto = programaTreino.getDataTerminoPrevisto();
        this.diasPorSemana = programaTreino.getDiasPorSemana();
        this.totalAulasPrevistas = programaTreino.getTotalAulasPrevistas();
        this.situacao = programaTreino.getSituacao();
        this.versao = programaTreino.getVersao();
        this.treinoRapido = programaTreino.getTreinoRapido();
        this.nrTreinosRealizados = programaTreino.getNrTreinosRealizados();
        this.programaTreinoRenovacao = programaTreino.getProgramaTreinoRenovacao();
        this.programaTreinoRenovado = programaTreino.getProgramaTreinoRenovado();
        this.dataRenovacao = programaTreino.getDataRenovacao();
        this.dataUltimaAtualizacao = programaTreino.getDataUltimaAtualizacao();
        List<ProgramaTreinoFicha> listaProgramas = programaTreino.getProgramaFichas();
        this.cliente = new ClienteJSON(programaTreino.getCliente());
        this.cliente.setUserName(usuario.getUserName());
        this.professorMontou = new ProfessorJSON(programaTreino.getProfessorMontou());
        if (listaProgramas != null) {
            for (ProgramaTreinoFicha ptf : listaProgramas) {
                FichaWriteJSON fjson = new FichaWriteJSON(ptf.getFicha());
                this.programasFicha.add(fjson);
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    public void setDataProximaRevisao(Date dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    public Date getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    public void setDataTerminoPrevisto(Date dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public ProgramaSituacaoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(ProgramaSituacaoEnum situacao) {
        this.situacao = situacao;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public Boolean getTreinoRapido() {
        return treinoRapido;
    }

    public void setTreinoRapido(Boolean treinoRapido) {
        this.treinoRapido = treinoRapido;
    }

    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }

    public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
        this.nrTreinosRealizados = nrTreinosRealizados;
    }

    public Integer getProgramaTreinoRenovacao() {
        return programaTreinoRenovacao;
    }

    public void setProgramaTreinoRenovacao(Integer programaTreinoRenovacao) {
        this.programaTreinoRenovacao = programaTreinoRenovacao;
    }

    public Integer getProgramaTreinoRenovado() {
        return programaTreinoRenovado;
    }

    public void setProgramaTreinoRenovado(Integer programaTreinoRenovado) {
        this.programaTreinoRenovado = programaTreinoRenovado;
    }

    public Date getDataRenovacao() {
        return dataRenovacao;
    }

    public void setDataRenovacao(Date dataRenovacao) {
        this.dataRenovacao = dataRenovacao;
    }

    public ClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(ClienteJSON cliente) {
        this.cliente = cliente;
    }

    public ProfessorJSON getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(ProfessorJSON professorMontou) {
        this.professorMontou = professorMontou;
    }

    public Date getDataUltimaAtualizacao() {
        return dataUltimaAtualizacao;
    }

    public void setDataUltimaAtualizacao(Date dataUltimaAtualizacao) {
        this.dataUltimaAtualizacao = dataUltimaAtualizacao;
    }

    public List<FichaWriteJSON> getProgramasFicha() {
        return programasFicha;
    }

    public void setProgramasFicha(List<FichaWriteJSON> programasFicha) {
        this.programasFicha = programasFicha;
    }
}
