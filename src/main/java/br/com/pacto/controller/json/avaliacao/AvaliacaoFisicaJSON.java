package br.com.pacto.controller.json.avaliacao;

import java.util.List;

/**
 * Created by alcides on 21/09/2017.
 */
public class AvaliacaoFisicaJSON {

    private Integer id;
    private String dataAtual;
    private String dataProxima;

    private String avaliador;

    private long dataAtualLong;
    private long dataProximaLong;

    private Double altura;
    private Double peso;
    private Double imc;
    private String categoriaGordura;
    private String categoriaIMC;
    private String legendaIMC;
    private Double idadeMetabolica;

    private Double percGordura;
    private Double percResidual;
    private Double percMuscular;
    private Double percOsseo;
    private Double percAgua;
    private Double percMassaMagra;

    private Double pesoGordura;
    private Double pesoResidual;
    private Double pesoMuscular;
    private Double pesoOsseo;

    private String rmlAbdomen;
    private String rmlBracos;

    private List<DobraCutaneaJSON> dobras;
    private List<PerimetriaJSON> perimetria;
    private List<FotoAvaliacaoJSON> fotos;

    private Double musculos;
    private Double gordura;
    private Double residuos;
    private Double ossos;
    private Double naoInformado;

    private Boolean bioimpedancia = Boolean.FALSE;
    private AvaliacaoPosturalJSON avaliacaoPosturalJSON;

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public String getCategoriaGordura() {
        return categoriaGordura;
    }

    public void setCategoriaGordura(String categoriaGordura) {
        this.categoriaGordura = categoriaGordura;
    }

    public String getCategoriaIMC() {
        return categoriaIMC;
    }

    public void setCategoriaIMC(String categoriaIMC) {
        this.categoriaIMC = categoriaIMC;
    }

    public Double getPercGordura() {
        return percGordura;
    }

    public void setPercGordura(Double percGordura) {
        this.percGordura = percGordura;
    }

    public Double getPercResidual() {
        return percResidual;
    }

    public void setPercResidual(Double percResidual) {
        this.percResidual = percResidual;
    }

    public Double getPercMuscular() {
        return percMuscular;
    }

    public void setPercMuscular(Double percMuscular) {
        this.percMuscular = percMuscular;
    }

    public Double getPercOsseo() {
        return percOsseo;
    }

    public void setPercOsseo(Double percOsseo) {
        this.percOsseo = percOsseo;
    }

    public Double getPesoGordura() {
        return pesoGordura;
    }

    public void setPesoGordura(Double pesoGordura) {
        this.pesoGordura = pesoGordura;
    }

    public Double getPesoResidual() {
        return pesoResidual;
    }

    public void setPesoResidual(Double pesoResidual) {
        this.pesoResidual = pesoResidual;
    }

    public Double getPesoMuscular() {
        return pesoMuscular;
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public String getRmlAbdomen() {
        return rmlAbdomen;
    }

    public void setRmlAbdomen(String rmlAbdomen) {
        this.rmlAbdomen = rmlAbdomen;
    }

    public String getRmlBracos() {
        return rmlBracos;
    }

    public void setRmlBracos(String rmlBracos) {
        this.rmlBracos = rmlBracos;
    }

    public List<DobraCutaneaJSON> getDobras() {
        return dobras;
    }

    public void setDobras(List<DobraCutaneaJSON> dobras) {
        this.dobras = dobras;
    }

    public List<PerimetriaJSON> getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(List<PerimetriaJSON> perimetria) {
        this.perimetria = perimetria;
    }

    public List<FotoAvaliacaoJSON> getFotos() {
        return fotos;
    }

    public void setFotos(List<FotoAvaliacaoJSON> fotos) {
        this.fotos = fotos;
    }

    public void setDataAtual(String dataAtual) {
        this.dataAtual = dataAtual;
    }

    public void setDataProxima(String dataProxima) {
        this.dataProxima = dataProxima;
    }

    public long getDataAtualLong() {
        return dataAtualLong;
    }

    public void setDataAtualLong(long dataAtualLong) {
        this.dataAtualLong = dataAtualLong;
    }

    public long getDataProximaLong() {
        return dataProximaLong;
    }

    public void setDataProximaLong(long dataProximaLong) {
        this.dataProximaLong = dataProximaLong;
    }

    public String getDataAtual() {
        return dataAtual;
    }

    public String getDataProxima() {
        return dataProxima;
    }

    public String getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(String avaliador) {
        this.avaliador = avaliador;
    }

    public String getLegendaIMC() {
        return legendaIMC;
    }

    public void setLegendaIMC(String legendaIMC) {
        this.legendaIMC = legendaIMC;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getIdadeMetabolica() {
        return idadeMetabolica;
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }

    public Double getPercAgua() {
        return percAgua;
    }

    public void setPercAgua(Double percAgua) {
        this.percAgua = percAgua;
    }

    public Double getMusculos() {
        return musculos;
    }

    public void setMusculos(Double musculos) {
        this.musculos = musculos;
    }

    public Double getGordura() {
        return gordura;
    }

    public void setGordura(Double gordura) {
        this.gordura = gordura;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }

    public Double getOssos() {
        return ossos;
    }

    public void setOssos(Double ossos) {
        this.ossos = ossos;
    }

    public Boolean getBioimpedancia() {
        return bioimpedancia;
    }

    public void setBioimpedancia(Boolean bioimpedancia) {
        this.bioimpedancia = bioimpedancia;
    }

    public Double getNaoInformado() {
        return naoInformado;
    }

    public void setNaoInformado(Double naoInformado) {
        this.naoInformado = naoInformado;
    }

    public Double getPercMassaMagra() {
        return percMassaMagra;
    }

    public void setPercMassaMagra(Double percMassaMagra) {
        this.percMassaMagra = percMassaMagra;
    }

    public AvaliacaoPosturalJSON getAvaliacaoPosturalJSON() {
        return avaliacaoPosturalJSON;
    }

    public void setAvaliacaoPosturalJSON(AvaliacaoPosturalJSON avaliacaoPosturalJSON) {
        this.avaliacaoPosturalJSON = avaliacaoPosturalJSON;
    }
}
