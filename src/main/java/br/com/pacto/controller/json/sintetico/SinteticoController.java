package br.com.pacto.controller.json.sintetico;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSint<PERSON><PERSON>;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.sintetico.SinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.webservice.TreinoWS;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.zw.beans.ClienteZW;
import servicos.integracao.zw.beans.UsuarioZW;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/sintetico")
public class SinteticoController {

    @RequestMapping(value = "/sincronizarUsuario", method = RequestMethod.POST)
    public @ResponseBody
    String sincronizarUsuario(@RequestHeader String key, @RequestBody List<UsuarioZW> list) {
        for (UsuarioZW usuarioZW : list) {
            try {
                if (usuarioZW.getCodigoExterno() != null && !usuarioZW.getCodigoExterno().isEmpty()
                        && Integer.valueOf(usuarioZW.getCodigoExterno()) > 0) {
                    UsuarioService usuarioService = (UsuarioService) UtilContext.getBean(
                            UsuarioService.class);
                    EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
                    empresaService.persistirEmpresasZW(key, usuarioZW.getEmpresaZW(),
                            usuarioZW.getUsuarioZW());

                    TipoUsuarioEnum tipo = usuarioZW.getTipo();
                    UsuarioEmail usuarioEmail = null;
                    ClienteSintetico cli = null;
                    ProfessorSintetico profLocal = null;
                    SinteticoService sinteticoService = (SinteticoService) UtilContext.getBean(SinteticoService.class);
                    if (tipo.equals(TipoUsuarioEnum.ALUNO)) {
                        sinteticoService.sincronizarAluno(key, usuarioZW);
                        ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                                ClienteSinteticoService.class);
                        String s = "select obj from ClienteSintetico obj where codigoPessoa = :id";
                        Map<String, Object> p = new HashMap<String, Object>();
                        p.put("id", usuarioZW.getCliente().getCodigoPessoa());
                        cli = clienteService.obterObjetoPorParam(key, s, p);
                        if (cli != null) {
                            clienteService.refresh(key, cli);
                            usuarioZW.setCliente(ClienteZW.fromClienteSintetico(cli));
                            usuarioZW.getCliente().setCodigo(cli.getCodigo());
                        }
                    } else {
                        ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                                ProfessorSinteticoService.class);
                        professorService.sincronizarProfessor(key, usuarioZW.getProfessor(), usuarioZW.getEmpresaZW(), usuarioZW.getFotoKey());

                        String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
                        Map<String, Object> p = new HashMap<String, Object>();
                        p.put("id", usuarioZW.getProfessor().getCodigoColaborador());
                        profLocal = professorService.obterObjetoPorParam(key, s, p);
                        if (profLocal != null) {
                            usuarioZW.setProfessor(profLocal);
                        }
                    }

                    final String where = tipo.equals(TipoUsuarioEnum.ALUNO) ? " where cliente.codigoCliente = :chave " : " where professor.codigo = :chave";
                    String s = "select u from Usuario u " + where;
                    HashMap<String, Object> p = new HashMap<String, Object>();
                    p.put("chave", tipo.equals(TipoUsuarioEnum.ALUNO) ? usuarioZW.getCliente().getCodigoCliente() : usuarioZW.getProfessor().getCodigo());
                    Usuario uLocal = usuarioService.obterObjetoPorParam(key, s, p);

                    UsuarioEmailService usuarioEmailService = (UsuarioEmailService) UtilContext.getBean(UsuarioEmailService.class);
                    if (!tipo.equals(TipoUsuarioEnum.ALUNO) && uLocal != null && uLocal.getUsuarioEmail() != null) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "codigo", uLocal.getUsuarioEmail().getCodigo());
                    }

                    if (usuarioEmail == null) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "email", usuarioZW.getEmail());
                        if (usuarioEmail == null) {
                            usuarioEmail = new UsuarioEmail();
                        }
                    }
                    usuarioEmail.setEmail(usuarioZW.getEmail());

                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("usuarioEmail", usuarioEmail.getEmail());
                    Usuario usuarioDoEmail = usuarioService.obterObjetoPorParam(key, "SELECT u FROM Usuario u WHERE u.usuarioEmail.email = :usuarioEmail", params);
                    if (usuarioDoEmail != null && usuarioDoEmail.getUsuarioZW() != null && !usuarioDoEmail.getUsuarioZW().equals(usuarioZW.getUsuarioZW())) {
                        System.out.println("ERRO: ".concat("O e-mail está vinculado ao usuário ".concat(usuarioDoEmail.getNome())));
                        continue;
                    }

                    if (uLocal == null) {


                        usuarioZW.setCodigo(null);
                        uLocal = UsuarioZW.toUsuarioTreino(usuarioZW);
                        uLocal.setCliente(cli);
                        uLocal.setTipo(tipo);
                        uLocal.setProfessor(profLocal);
                        if (!tipo.equals(TipoUsuarioEnum.ALUNO)) {
                            uLocal.setUsuarioEmail(usuarioEmail);
                        }
                        sinteticoService.tratarPerfilUsuario(key, usuarioZW, uLocal);
                        usuarioService.inserir(key, uLocal);
                    } else {
                        usuarioZW.setCodigo(uLocal.getCodigo());
                        try {
                            if (usuarioEmail.getCodigo() != null) {
                                usuarioEmail = usuarioEmailService.alterar(key, usuarioEmail);
                            } else {
                                usuarioEmail = usuarioEmailService.inserir(key, usuarioEmail);
                            }
                        } catch (Exception ex) {
                            System.out.println("Não foi possível alterar e-mail: " + ex.getMessage());
                        }
                        if (!tipo.equals(TipoUsuarioEnum.ALUNO)) {
                            uLocal.setUsuarioEmail(usuarioEmail);
                        }
                        if (!UteisValidacao.emptyNumber(usuarioZW.getPerfilTw())) {
                            sinteticoService.tratarPerfilUsuario(key, usuarioZW, uLocal);
                        }
                        uLocal = usuarioService.alterar(key, uLocal, usuarioZW);
                        usuarioService.adicionarUsuarioServicoDescobrir(key, uLocal.getUserName());
                    }
                } else {
                    System.out.println("Usuário inválido!");
                    continue;
                }
            } catch (Exception ex) {
                Logger.getLogger(SinteticoController.class.getName()).log(Level.SEVERE, null, ex);
                return "ERRO: ".concat(ex.getMessage());
            }
        }
        return "OK";
    }

    @RequestMapping(value = "/incrementarVersaoCliente", method = RequestMethod.POST)
    public @ResponseBody
    String incrementarVersaoCliente(@RequestHeader String key, @RequestBody UsuarioZW usuarioZW) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            clienteService.incrementarVersao(key,usuarioZW.getCliente().getCodigoCliente());
            if(!UteisValidacao.emptyString(usuarioZW.getFotoKey())) {
                clienteService.atualizarFotoAlunoApp(key, usuarioZW);
            }
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @RequestMapping(value = "/excluir-aluno", method = RequestMethod.POST)
    public @ResponseBody String excluirAluno(@RequestHeader String key, @RequestBody Integer codigoCliente) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            clienteService.excluirAluno(key, codigoCliente);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
}
