package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventoAulaDTO {
    private Integer id;
    private AulaResponseDTO aula;
    private Long dia;
    private String horarioInicio;
    private String horarioFim;
    private ProfessorSubstitutoDTO professorSubstituto;
    private String justificativaSubstituicao;
    private AlunoResponseTO[] alunos;

    public EventoAulaDTO() {
    }

    public EventoAulaDTO(AulaHorario aulaHorario,
                         Date dia,
                         ProfessorSintetico professor,
                         String justificativaSubstituicao,
                         List<AlunoResponseTO> alunos,
                         Boolean treinoIndependente) {
        setId(aulaHorario.getCodigo());
        setHorarioInicio(aulaHorario.getInicio());
        setHorarioFim(aulaHorario.getFim());
        setDia(dia.getTime());
        setJustificativaSubstituicao(justificativaSubstituicao);
        setAula(new AulaResponseDTO(aulaHorario.getAula(), professor, treinoIndependente));
        setProfessorSubstituto(new ProfessorSubstitutoDTO(professor));
        setAlunos(alunos.toArray(new AlunoResponseTO[alunos.size()]));
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public AulaResponseDTO getAula() {
        return aula;
    }

    public void setAula(AulaResponseDTO aula) {
        this.aula = aula;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getHorarioInicio() {
        return horarioInicio;
    }

    public void setHorarioInicio(String horarioInicio) {
        this.horarioInicio = horarioInicio;
    }

    public String getHorarioFim() {
        return horarioFim;
    }

    public void setHorarioFim(String horarioFim) {
        this.horarioFim = horarioFim;
    }

    public String getJustificativaSubstituicao() {
        return justificativaSubstituicao;
    }

    public void setJustificativaSubstituicao(String justificativaSubstituicao) {
        this.justificativaSubstituicao = justificativaSubstituicao;
    }

    public AlunoResponseTO[] getAlunos() {
        return alunos;
    }

    public void setAlunos(AlunoResponseTO[] alunos) {
        this.alunos = alunos;
    }

    public ProfessorSubstitutoDTO getProfessorSubstituto() {
        return professorSubstituto;
    }

    public void setProfessorSubstituto(ProfessorSubstitutoDTO professorSubstituto) {
        this.professorSubstituto = professorSubstituto;
    }
}
