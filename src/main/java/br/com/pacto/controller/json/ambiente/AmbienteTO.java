package br.com.pacto.controller.json.ambiente;

import br.com.pacto.bean.aula.Ambiente;
import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmbienteTO {

    private Integer codigo;
    private Integer codigoZW;
    private String nome;
    private Integer capacidade;

    public AmbienteTO() {}

    public AmbienteTO(Ambiente ambiente) {
        this.codigo = ambiente.getCodigo();
        this.codigoZW = ambiente.getCodigoZW();
        this.nome = ambiente.getNome();
        this.capacidade = ambiente.getCapacidade();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoZW() {
        return codigoZW;
    }

    public void setCodigoZW(Integer codigoZW) {
        this.codigoZW = codigoZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }
}
