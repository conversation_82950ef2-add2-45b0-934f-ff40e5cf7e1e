package br.com.pacto.controller.json.parceiros;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.parceiro.Parceiro;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.parceiro.ParceiroService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */
@Controller
@RequestMapping("/psec/parceiros")
public class ParceiroController {

    private ParceiroService parceiroService;

    @Autowired
    public ParceiroController (ParceiroService parceiroService) {
        Assert.notNull(parceiroService, "O serviço de parceiro não foi injetado corretamente");
        this.parceiroService = parceiroService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroParceiro(@RequestBody ParceiroCreateTO parceiroCreateTO) {
        try {
            List<ParceiroTO> listaC = new ArrayList<>();
            List<ParceiroTO> listaA = new ArrayList<>();

            for (ParceiroTO parceiroTO: parceiroCreateTO.getParceiros()) {
                if(parceiroTO.getId() == null || parceiroTO.getId() < 1){
                    listaC.add(parceiroTO);
                } else {
                    listaA.add(parceiroTO);
                }
            }
            if (parceiroCreateTO.getParceirosExcluidosIds().size() > 0) {
                parceiroService.removerParceiros(parceiroCreateTO.getParceirosExcluidosIds());
            }
            parceiroService.removerParceirosNotIn(listaA);
            List<ParceiroResponseTO> parceiroResponseC = new ArrayList<ParceiroResponseTO>();
            List<ParceiroResponseTO> parceiroResponseA = new ArrayList<ParceiroResponseTO>();
            if(listaC.size() > 0){
                parceiroResponseC = parceiroService.cadastroListaParceiro(listaC);
            }
            if(listaA.size() > 0){
                parceiroResponseA = parceiroService.alterarListaParceiro(listaA);
            }

            parceiroResponseC.addAll(parceiroResponseA);
            return ResponseEntityFactory.ok(parceiroResponseC);
        } catch (ServiceException e) {
            Logger.getLogger(ParceiroController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar parceiro", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaParceiro() {
        try {

            return ResponseEntityFactory.ok(
                    parceiroService.listaParceiro()
            );
        } catch (ServiceException e) {
            Logger.getLogger(ParceiroController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar parceiro", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
