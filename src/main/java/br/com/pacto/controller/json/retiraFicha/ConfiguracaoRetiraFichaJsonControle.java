package br.com.pacto.controller.json.retiraFicha;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.notificacao.NotificacaoJSONControle;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaDao;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaLogDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.retiraFicha.ConfiguracaoRetiraFichaLogService;
import br.com.pacto.service.intf.retiraFicha.ConfiguracaoRetiraFichaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/retiraFicha")
public class ConfiguracaoRetiraFichaJsonControle  extends SuperControle {

    @Autowired
    private ConfiguracaoRetiraFichaService service;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ConfiguracaoRetiraFichaDao configuracaoRetiraFichaDao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConfiguracaoRetiraFichaLogService configuracaoRetiraFichaLogService;
    @Autowired
    private ConfiguracaoRetiraFichaLogDao configuracaoRetiraFichaLogDao;


    @RequestMapping(value = "{ctx}/buscarPorCodigo", method = RequestMethod.GET)
    @ResponseBody
    public ModelMap buscarPorCodigo(@PathVariable("ctx") String ctx, @RequestParam Integer codigo) {
        ModelMap mm = new ModelMap();

        try {
            ConfiguracaoRetiraFicha configuracaoRetiraFicha = configuracaoRetiraFichaDao.findById(ctx, codigo);
            mm.addAttribute(RETURN, new ConfiguracaoRetiraFichaJson(configuracaoRetiraFicha).toJSON());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/gravar", method = RequestMethod.POST)
    public ModelMap inserirConfiguracaoRetiraFicha(@PathVariable("ctx") String ctx,
                                                   @RequestParam Integer codigoUsuario,
                                                   @RequestBody ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson) {

        ModelMap mm = new ModelMap();
        Integer codigo = configuracaoRetiraFichaJson.getCodigo();
        if (configuracaoRetiraFichaJson.getCodigo() != null && codigo > 0){
            try {

                Empresa empresa = empresaService.obterPorId(ctx, configuracaoRetiraFichaJson.getCodigoEmpresa());
                if (empresa == null) {
                    throw new ServiceException("Ocorreu um erro ao consultar a configuração do retira ficha");
                }


                service.alterarConfiguracaoRetiraFicha(ctx,configuracaoRetiraFichaJson,codigoUsuario);

                mm.addAttribute(RETURN, "Operação realizada com sucesso! Codigo Cadastro Retira Ficha é: " + configuracaoRetiraFichaJson.getCodigo());
            } catch (Exception ex) {
                mm.addAttribute(STATUS_ERRO, ex.getMessage());
                Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }else {
            try {
                ConfiguracaoRetiraFicha configuracaoRetiraFicha = service.cadastrarConfiguracaoRetiraFicha(ctx, configuracaoRetiraFichaJson,codigoUsuario);

                mm.addAttribute(RETURN, "Operação realizada com sucesso! Codigo Cadastro Retira Ficha é: " + configuracaoRetiraFicha.getCodigo());
            } catch (Exception ex) {
                mm.addAttribute(STATUS_ERRO, ex.getMessage());
                Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }



        return mm;
    }


    @RequestMapping(value = "{ctx}/buscarLogs/{codigoConfiguracaoRetiraFicha}", method = RequestMethod.GET)
    @ResponseBody
    public ModelMap buscarLogPorCodigoConfiguracao(@PathVariable("ctx") String ctx,
                                                   @PathVariable("codigoConfiguracaoRetiraFicha") Integer codigoConfiguracaoRetiraFicha) {
        ModelMap mm = new ModelMap();

        try {
            List<ConfiguracaoRetiraFichaLog> listaConfiguracaoRetiraFichaLog = configuracaoRetiraFichaLogService.listarLogsAlteracoes(ctx, codigoConfiguracaoRetiraFicha);

            List<ConfiguracaoRetiraFichaLogJson> listaConfiguracaoRetiraFichaLogJson = new ArrayList<ConfiguracaoRetiraFichaLogJson>();


            for (ConfiguracaoRetiraFichaLog retiraFichaLog : listaConfiguracaoRetiraFichaLog) {
                listaConfiguracaoRetiraFichaLogJson.add(new ConfiguracaoRetiraFichaLogJson(retiraFichaLog));
            }

            mm.addAttribute(RETURN, listaConfiguracaoRetiraFichaLogJson);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

}
