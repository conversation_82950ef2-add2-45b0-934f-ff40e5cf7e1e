package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.gestao.AgrupadorIndicadores;
import br.com.pacto.bean.gestao.Indicador;
import br.com.pacto.bean.gestao.IndicadorEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

/**
 * Created paulo 05/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorIndicadorResponseDTO {

    private ProfessorSimplesResponseDTO professor;
    private Number comTreino;
    private Number semTreino;
    private Number vencidos;
    private Number proxVencimento;
    private Number avaliacao;
    private Number estrelas2;
    //############### Indicadores de atividades ######################
    private Number novos;
    private Number renovados;
    private Number revisados;
    private Number acompanhamentos;
    private Number revisar;
    private Number atividadesAcompanhamentos;
    private Date dataAcompanhamento;
    //############### Indicadores da agenda ######################
    private Number agendados;
    private Number executados;
    private Number cancelados;
    private Number faltas;
    private String nome;

    public ProfessorIndicadorResponseDTO() {
    }

    public ProfessorIndicadorResponseDTO(AgrupadorIndicadores agrupador, ProfessorSintetico professorSintetico){
        this.professor = new ProfessorSimplesResponseDTO(professorSintetico);
        this.nome = professorSintetico.getNomeAbreviado();
        for (Indicador indicador : agrupador.getIndicadores()) {
            if (indicador != null) {
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA) {
                    this.comTreino = indicador.getTotal();
                } else if (getComTreino() == null) {
                    this.comTreino = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_SEM_TREINO) {
                    this.semTreino = indicador.getTotal();
                } else if (getSemTreino() == null) {
                    this.semTreino = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_VENCIDOS) {
                    this.vencidos = indicador.getTotal();
                } else if (getVencidos() == null) {
                    this.vencidos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_PROX_VENCIMENTO) {
                    this.proxVencimento = indicador.getTotal();
                } else if (getProxVencimento() == null) {
                    this.proxVencimento = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_MEDIA_AVALIACAO) {
                    this.avaliacao = indicador.getTotal();
                } else if (getAvaliacao() == null) {
                    this.avaliacao = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_2_ESTRELAS) {
                    this.estrelas2 = indicador.getTotal();
                } else if (getEstrelas2() == null) {
                    this.estrelas2 = 0;
                }
                //##################### Indicadores de atividade ##########################

                if(indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_NOVO) {
                    this.novos = indicador.getTotal();
                } else if (getNovos() == null) {
                    this.novos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_RENOVADO) {
                    this.renovados = indicador.getTotal();
                } else if (getRenovados() == null) {
                    this.renovados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_REVISADO) {
                    this.revisados = indicador.getTotal();
                } else if (getRevisados() == null) {
                    this.revisados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_ACOMPANHADO) {
                    this.acompanhamentos = indicador.getTotal();
                    if (this.dataAcompanhamento == null) {
                        this.dataAcompanhamento = indicador.getDataInicio();
                    }
                } else if (getAcompanhamentos() == null) {
                    this.acompanhamentos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_REVISAR) {
                    this.revisar = indicador.getTotal();
                } else if (getRevisados() == null) {
                    this.revisar = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_ATIVIDADE_PROF_TREINO_ACOMPANHADO) {
                    this.atividadesAcompanhamentos = indicador.getTotal();
                } else if (getAtividadesAcompanhamentos() == null) {
                    this.atividadesAcompanhamentos = 0;
                }
                //##################### Indicadores da agenda ##########################
                if (indicador.getTipo() == IndicadorEnum.N_AGENDADOS) {
                    this.agendados = indicador.getTotal();
                } else if (getAgendados() == null) {
                    this.agendados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_EXECUTADOS) {
                    this.executados = indicador.getTotal();
                } else if (getExecutados() == null) {
                    this.executados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_CANCELADOS) {
                    this.cancelados = indicador.getTotal();
                } else if (getCancelados() == null) {
                    this.cancelados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_FALTOU) {
                    this.faltas = indicador.getTotal();
                } else if (getFaltas() == null) {
                    this.faltas = 0;
                }
            }
        }
    }

    public ProfessorSimplesResponseDTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSimplesResponseDTO professor) {
        this.professor = professor;
    }

    public Number getComTreino() {
        return comTreino;
    }

    public void setComTreino(Number comTreino) {
        this.comTreino = comTreino;
    }

    public Number getSemTreino() {
        return semTreino;
    }

    public void setSemTreino(Number semTreino) {
        this.semTreino = semTreino;
    }

    public Number getVencidos() {
        return vencidos;
    }

    public void setVencidos(Number vencidos) {
        this.vencidos = vencidos;
    }

    public Number getProxVencimento() {
        return proxVencimento;
    }

    public void setProxVencimento(Number proxVencimento) {
        this.proxVencimento = proxVencimento;
    }

    public Number getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(Number avaliacao) {
        this.avaliacao = avaliacao;
    }

    public Number getEstrelas2() {
        return estrelas2;
    }

    public void setEstrelas2(Number estrelas2) {
        this.estrelas2 = estrelas2;
    }

    public Number getNovos() {
        return novos;
    }

    public void setNovos(Number novos) {
        this.novos = novos;
    }

    public Number getRenovados() {
        return renovados;
    }

    public void setRenovados(Number renovados) {
        this.renovados = renovados;
    }

    public Number getRevisados() {
        return revisados;
    }

    public void setRevisados(Number revisados) {
        this.revisados = revisados;
    }

    public Number getAcompanhamentos() {
        return acompanhamentos;
    }

    public void setAcompanhamentos(Number acompanhamentos) {
        this.acompanhamentos = acompanhamentos;
    }

    public Number getRevisar() {
        return revisar;
    }

    public void setRevisar(Number revisar) {
        this.revisar = revisar;
    }

    public Number getAgendados() {return agendados;}

    public void setAgendados(Number agendados) {this.agendados = agendados;}

    public Number getExecutados() {return executados;}

    public void setExecutados(Number executados) {this.executados = executados;}

    public Number getCancelados() {return cancelados;}

    public void setCancelados(Number cancelados) {this.cancelados = cancelados;}

    public Number getFaltas() {return faltas;}

    public void setFaltas(Number faltas) {this.faltas = faltas;}

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Number getAtividadesAcompanhamentos() {
        return atividadesAcompanhamentos;
    }

    public void setAtividadesAcompanhamentos(Number atividadesAcompanhamentos) {
        this.atividadesAcompanhamentos = atividadesAcompanhamentos;
    }
    public Date getDataAcompanhamento() {
        return dataAcompanhamento;
    }

    public void setDataAcompanhamento(Date dataAcompanhamento) {
        this.dataAcompanhamento = dataAcompanhamento;
    }

}
